pipeline {
  agent {
      node {
        label 'alpakka_java21'
      }
  }
  environment {
      String SLACK_CHANNEL = '#dpgr_alpakka_notifications'
      String SLACK_MESSAGE = "@here Build #${BUILD_NUMBER} for profile service (<${BUILD_URL}|open>) has failed"
      String SLACK_TEAM_DOMAIN = 'dpos'
    }
  options {
    ansiColor('xterm')
    buildDiscarder(logRotator(numToKeepStr: '25'))
    disableConcurrentBuilds()
    disableResume()
  }
  stages {
    stage('Compile') {
        steps {
            ansiColor('xterm') {
                sh "sbt -Dversion=${BUILD_NUMBER} clean scalafmtCheckAll"
            }
        }
    }
    stage('Test and quality') {
      steps {
        ansiColor('xterm') {
            sh "sbt -Dversion=${BUILD_NUMBER} scapegoat coverage test coverageReport sonarScan"
        }
      }
    }
    stage('Publish') {
      steps {
        ansiColor('xterm') {
          sh "aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 674201978047.dkr.ecr.eu-west-1.amazonaws.com"
          sh "DOCKER_BUILDKIT=1 sbt -Dversion=${BUILD_NUMBER} docker:publishLocal"
          sh "docker push 674201978047.dkr.ecr.eu-west-1.amazonaws.com/profile-service:${BUILD_NUMBER}"
          sh "docker push 674201978047.dkr.ecr.eu-west-1.amazonaws.com/profile-service-saved-search-sender:${BUILD_NUMBER}"
        }
      }
    }
    stage('Deploy - ACC') {
      steps {
        ansiColor('xterm') {
          sh "sbt \"deploy acc ${BUILD_NUMBER}\""
        }
      }
    }
    stage('Deploy - PRO') {
      steps {
        ansiColor('xterm') {
          sh "sbt \"deploy pro ${BUILD_NUMBER}\""
        }
      }
      post {
        failure {
            ansiColor('xterm') {
                sh "sbt \"rollback pro\""
            }
        }
      }
    }
  }
  post {
    unsuccessful {
     withCredentials([string(credentialsId: 'SLACK_TOKEN', variable: 'SLACK_TOKEN')]) {
      slack(SLACK_CHANNEL, '#c11238', SLACK_MESSAGE, SLACK_TEAM_DOMAIN, SLACK_TOKEN)
     }
    }
  }
}

def slack(String channel, String color, String message, String teamDomain, String token) {
  slackSend channel: channel, color: color, message: message, teamDomain: teamDomain, token: token
}