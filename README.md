# Profile Search Service
This service contains the profile search functionalities

### Prerequisites
This project requires:
* [JAVA 11](http://openjdk.java.net)
* [SBT 1.8.x](https://www.scala-sbt.org/)
* [Scala 3.5.1](https://docs.scala-lang.org/getting-started/index.html)
* [Docker](https://docs.docker.com/engine/install/)
* [AWS CLI V1](https://docs.aws.amazon.com/cli/v1/userguide/install-linux.html)
* AWS Configuration:

Create two files in `~/.aws`:

`~/.aws/config`:
```
  [default]
  region = eu-west-1
```

`~/.aws/credentials`:
```
  [default]
  aws_access_key_id=AKIAxxxxxxx
  aws_secret_access_key=xxxxxx
```

AWS credentials should be handed out to you. With those credentials you can login to AWS and get an access key ID as well as an access key.

### Run locally
* to be able to view profiles make sure the credit service is running
```
$ aws ecr get-login-password | docker login  -u AWS --password-stdin https://************.dkr.ecr.eu-west-1.amazonaws.com
$ docker-compose up -d
```
* [Project is accessible via port **14200**](http://localhost:14200)
* You can check the [swagger documentation](http://0.0.0.0:14200/docs/profileservice/v1)


**Note**: Locally, you might encounter some issues with the virtual memory allocation for Elasticsearch. The issue will be something like the
following: `max virtual memory areas vm.max_map_count [65530] is too low, increase to at least [262144]`. To fix this issue, you need to
increase the virtual memory with this command: `sudo sysctl -w vm.max_map_count=262144`.

For further info, you can check the [virtual memory official documentation in Elasticsearch](https://www.elastic.co/guide/en/elasticsearch/reference/current/vm-max-map-count.html).


### Tests
```
$ sbt test
```

### Test Coverage
```
$ sbt coverageReport 
```

### Running individual test suites
Normal tests can be run with
```
$ sbt 'testOnly {reference to test file}'
```
When running tests using shared resources the resources need to be added too:
```
$ sbt 'testOnly {reference to test file} nl.dpes.profilesearch.testcontainers.SharedResources'
```

## How to contribute

To test your changes locally, you should rebuild the project using the following commands:
```
sbt docker:publishLocal
docker-compose stop profile-service
docker-compose up -d
```

To rebuild the whole project including the Elasticsearch cluster:
```
docker compose stop && docker compose rm
sbt docker:publishLocal
docker-compose up -d
```

Before pushing your code, you can run the following command to format the code, compile, run tests and generate the coverage report:
```
$ sbt prepare-commit
```

## Deployment
After pushing, your code will be deployed by [Jenkins](http://ndp-jenkins-master.persgroep.digital:8080/job/Profile%20Service/)

## Debugging
When running the service locally, you can attach a debugger to the running container (which is exposed on port 5005).
If you use IntelliJ, go to Run > Attach to Process... and select the Java process running in the container.

## How to test
Local using Swagger:
* Go to profile-service
* git pull --rebase
* run docker compose stop
* docker compose rm
* docker compose up -d
* Go to http://localhost:14200/docs/profileservice/v1/#/default/getProfiles

 
