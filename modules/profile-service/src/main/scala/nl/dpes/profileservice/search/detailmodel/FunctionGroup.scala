package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type FunctionGroup = String
object FunctionGroup {
  def apply(functionGroup: String): FunctionGroup = functionGroup

  given Encoder[FunctionGroup] = Encoder.encodeString
  given Decoder[FunctionGroup] = Decoder.decodeString
  given Schema[FunctionGroup] = Schema.schemaForString

  given Transformer[detailpage.FunctionGroup, FunctionGroup] = (functionGroup: detailpage.FunctionGroup) =>
    FunctionGroup(functionGroup.value)
}
