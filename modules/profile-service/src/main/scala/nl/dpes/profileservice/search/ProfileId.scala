package nl.dpes.profileservice
package search

import doobie.Meta
import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch.ProfileId as SearchProfileId
import nl.dpes.recruiterfavorites.service.domainmodel.ProfileId as FavoriteId
import sttp.tapir.CodecFormat.TextPlain
import sttp.tapir.{Codec, DecodeResult, Schema}

case class ProfileId(value: String)

object ProfileId {
  def apply(profileId: String): Either[String, ProfileId] =
    if (profileId.trim.length == 36)
      Right(new ProfileId(profileId.trim))
    else
      Left(s"ProfileId is a 36-character identifier. You have provided '${profileId.trim.length}' characters.")

  def unsafeApply(value: String): ProfileId = new ProfileId(value)

  given Encoder[ProfileId] = Encoder.encodeString.contramap(_.value)
  given Decoder[ProfileId] = Decoder.decodeString.emap(ProfileId(_))
  given Codec[String, ProfileId, TextPlain] = Codec.string.mapDecode { id =>
    ProfileId(id) match {
      case Right(profileId) => DecodeResult.Value(profileId)
      case Left(error)      => DecodeResult.Error(id, new RuntimeException(error))
    }
  }(_.value)
  given Schema[ProfileId] = Schema.string
  given Meta[ProfileId] = Meta.StringMeta
    .tiemap(ProfileId(_))(_.value)

  given toServiceTransformer: Transformer[ProfileId, SearchProfileId] = (profileId: ProfileId) => SearchProfileId.unsafe(profileId.value)
  given fromServiceTransformer: Transformer[SearchProfileId, ProfileId] = (profileId: SearchProfileId) =>
    ProfileId.unsafeApply(profileId.value)
  given toFavoriteIdTransformer: Transformer[ProfileId, FavoriteId] = (profileId: ProfileId) => FavoriteId.apply(profileId.value)
  given fromFavoriteIdTransformer: Transformer[FavoriteId, ProfileId] = (profileId: FavoriteId) => ProfileId.unsafeApply(profileId.value)
}
