package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.domain.DriversLicense as DomainDriversLicense
import sttp.tapir.Schema

opaque type DriverLicense = String

object DriverLicense {
  given Encoder[DriverLicense] = Encoder.encodeString
  given Decoder[DriverLicense] = Decoder.decodeString
  given Schema[DriverLicense] = Schema.string
  given Transformer[DomainDriversLicense, DriverLicense] = (domainDriversLicense: DomainDriversLicense) =>
    DriverLicense(domainDriversLicense.value)
  given Transformer[DriverLicense, DomainDriversLicense] = (driverLicense: DriverLicense) => DomainDriversLicense(driverLicense)

  def apply(driverLicense: String): DriverLicense = driverLicense
}
