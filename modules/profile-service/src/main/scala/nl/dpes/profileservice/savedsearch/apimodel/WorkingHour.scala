package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.{partial, PartialTransformer, Transformer}
import nl.dpes.profilesearch.domain.WorkingHours as DomainWorkingHours
import sttp.tapir.Schema

opaque type WorkingHour = String

object WorkingHour {
  given Encoder[WorkingHour] = Encoder.encodeString
  given Decoder[WorkingHour] = Decoder.decodeString
  given Schema[WorkingHour] = Schema.string
  given Transformer[DomainWorkingHours, WorkingHour] = (workingHour: DomainWorkingHours) =>
    WorkingHour(DomainWorkingHours.toEntryName(workingHour))
  given PartialTransformer[WorkingHour, DomainWorkingHours] =
    PartialTransformer[WorkingHour, DomainWorkingHours] { (workingHour: WorkingHour) =>
      partial.Result.fromOption(DomainWorkingHours.fromString(workingHour))
    }

  def apply(workingHour: String): WorkingHour = workingHour
}
