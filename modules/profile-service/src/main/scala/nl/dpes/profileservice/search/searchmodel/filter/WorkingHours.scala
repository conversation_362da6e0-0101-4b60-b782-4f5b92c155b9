package nl.dpes.profileservice.search.searchmodel.filter

import nl.dpes.profilesearch.utils.CommaSeparatedStrings
import sttp.tapir.{Codec, CodecFormat}

case class WorkingHours(value: String)

object WorkingHours {
  given Codec[String, WorkingHours, CodecFormat.TextPlain] = Codec.string.map(WorkingHours(_))(_.value)

  def createStringifiedWorkingHours: List[WorkingHour] => Option[WorkingHours] =
    CommaSeparatedStrings.stringify(_)(_.value, WorkingHours.apply)

  def extractWorkingHoursList: Option[WorkingHours] => List[WorkingHour] =
    CommaSeparatedStrings.extractList(_)(_.value, WorkingHour.apply)
}
