package nl.dpes.profileservice.search.searchmodel.filter

import io.circe.{Decoder, Encoder}
import sttp.tapir.{Schema, SchemaType}

case class AggregationKeyName(value: String)

object AggregationKeyName {
  given Encoder[AggregationKeyName] = Encoder.encodeString.contramap(_.value)
  given Decoder[AggregationKeyName] = Decoder.decodeString.map(AggregationKeyName(_))
  given Schema[AggregationKeyName] = Schema(SchemaType.SString())
}
