package nl.dpes.profileservice.search.searchmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch
import sttp.tapir.Schema

type Experience = String
object Experience {
  def apply(experience: String): Experience = experience
  given Encoder[Experience] = Encoder.encodeString
  given Decoder[Experience] = Decoder.decodeString
  given Schema[Experience] = Schema.schemaForString

  given Transformer[profilesearch.Experience, Experience] = (experience: profilesearch.Experience) => Experience(experience)
}
