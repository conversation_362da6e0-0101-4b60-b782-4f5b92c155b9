package nl.dpes.profileservice
package search.searchmodel

import nl.dpes.profilesearch.utils.SimpleSearchQueryTranslator
import nl.dpes.profilesearch.utils.SimpleSearchQueryTranslator.{TranslationError, UnmatchedQuoteOrBracket}
import nl.dpes.profileservice.tapir.CustomDecodeFailureHandler.CodedDecodeException
import sttp.tapir.*

case class SearchTerm private (value: String) extends AnyVal
object SearchTerm {
  given codec: Codec.PlainCodec[SearchTerm] = Codec.string.mapDecode { s =>
    SearchTerm(s) match {
      case Right(term) => DecodeResult.Value(term)
      case Left(UnmatchedQuoteOrBracket(query)) =>
        DecodeResult.Error(s, CodedDecodeException("UnmatchedQuoteOrBracket", s"Unmatched quotes or brackets in the search string: $query"))
//      case Left(error) => DecodeResult.Error(s, error) // Uncomment this line if you want to handle other TranslationErrors
    }
  }(_.value)

  def apply(value: String): Either[TranslationError, SearchTerm] =
    SimpleSearchQueryTranslator
      .translate(value)
      .map(new SearchTerm(_))

  def unsafe(value: String): SearchTerm = apply(value) match {
    case Left(value)  => throw new IllegalArgumentException(value)
    case Right(value) => value
  }

  def parameter(name: String, description: String): EndpointInput[Option[SearchTerm]] =
    query[Option[SearchTerm]](name)
      .description(description)
}
