package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type WorkLevel = String
object WorkLevel {
  def apply(level: String): WorkLevel = level

  given Encoder[WorkLevel] = Encoder.encodeString
  given Decoder[WorkLevel] = Decoder.decodeString
  given Schema[WorkLevel] = Schema.schemaForString

  given Transformer[detailpage.WorkLevel, WorkLevel] = (workLevel: detailpage.WorkLevel) => WorkLevel(workLevel.value)
}
