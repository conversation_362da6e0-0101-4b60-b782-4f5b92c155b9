package nl.dpes.profileservice
package savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.{PartialTransformer, Transformer}
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.domain.filter.GeoDistanceFilter
import nl.dpes.profileservice.savedsearch.apimodel.SearchFilters.*
import nl.dpes.savedsearch.domain.SavedSearchFilters as DomainSavedSearchFilters
import sttp.tapir.Schema
import nl.dpes.profileservice.savedsearch.apimodel.SingleFilter
import nl.dpes.profileservice.savedsearch
import nl.dpes.profilesearch.domain.CommuteDistance as DomainCommuteDistance

case class SearchFilters(
  searchTerm: Option[SingleFilter[SearchTerm]],
  city: Option[SingleFilter[City]],
  geoDistance: Option[SingleFilter[GeoDistance]],
  provinces: Option[MultiFilter[Province]],
  updatedDate: Option[SingleFilter[UpdatedDate]],
  functionGroups: Option[MultiFilter[FunctionGroup]],
  workLevels: Option[MultiFilter[WorkLevel]],
  workingHours: Option[MultiFilter[WorkingHour]],
  careerLevels: Option[MultiFilter[CareerLevel]],
  requestedSalaries: Option[MultiFilter[RequestedSalary]],
  availabilities: Option[MultiFilter[Availability]],
  driversLicenses: Option[MultiFilter[DriverLicense]],
  languages: Option[MultiFilter[Language]]
) derives Decoder,
      Schema

object SearchFilters {

  given [A](using d: Decoder[A]): Decoder[Option[A]] =
    Decoder.decodeOption(d).emap {
      case None => Right(None) // missing field or null
      case Some(value) =>
        value match
          case s: String if s.trim.isEmpty => Right(None)
          case arr: Seq[?] if arr.isEmpty  => Right(None)
          case m: Map[?, ?] if m.isEmpty   => Right(None)
          case other                       => Right(Some(other))
    }

  given Encoder[SearchFilters] = {
    given [A](using e: Encoder[A]): Encoder[Option[A]] =
      Encoder.instance {
        case None => Json.Null // Circe will drop if we post-process
        case Some(value) =>
          val json = e(value)
          json.fold(
            jsonNull = Json.Null,
            jsonBoolean = _ => json,
            jsonNumber = _ => json,
            jsonString = s => if s.trim.isEmpty then Json.Null else json,
            jsonArray = arr => if arr.isEmpty then Json.Null else json,
            jsonObject = obj => if obj.isEmpty then Json.Null else json
          )
      }

    Encoder.derived[SearchFilters].mapJson(_.deepDropNullValues)
  }
}
