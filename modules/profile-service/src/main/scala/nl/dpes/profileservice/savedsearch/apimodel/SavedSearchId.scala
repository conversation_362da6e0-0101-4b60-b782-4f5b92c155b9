package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.Transformer
import sttp.tapir.{Codec, Schema}
import sttp.tapir.CodecFormat.TextPlain
import nl.dpes.savedsearch.domain.SavedSearchId as DomainSavedSearchId

import java.util.UUID

opaque type SavedSearchId = String

object SavedSearchId {
  given Encoder[SavedSearchId] = Encoder.encodeString
  given Decoder[SavedSearchId] = Decoder.decodeString
  given Codec[String, SavedSearchId, TextPlain] = Codec.string.map(SavedSearchId(_))(_.value)
  given Schema[SavedSearchId] = Schema.string
  given Transformer[UUID, SavedSearchId] = (uuid: UUID) => apply(uuid.toString)
  given Transformer[SavedSearchId, UUID] = (id: SavedSearchId) => UUID.fromString(id.value)
  given Transformer[SavedSearchId, DomainSavedSearchId] = (id: SavedSearchId) => DomainSavedSearchId(id.value)

  def apply(savedSearchId: String): SavedSearchId = savedSearchId

  extension (savedSearchId: SavedSearchId) def value: String = savedSearchId
}
