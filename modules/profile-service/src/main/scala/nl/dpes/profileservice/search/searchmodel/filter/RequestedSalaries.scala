package nl.dpes.profileservice.search.searchmodel.filter

import nl.dpes.profilesearch.utils.CommaSeparatedStrings
import sttp.tapir.{Codec, CodecFormat}

case class RequestedSalaries(value: String)

object RequestedSalaries {
  given Codec[String, RequestedSalaries, CodecFormat.TextPlain] = Codec.string.map(RequestedSalaries(_))(_.value)

  def createStringifiedRequestedSalaries: List[RequestedSalary] => Option[RequestedSalaries] =
    CommaSeparatedStrings.stringify(_)(_.value, RequestedSalaries.apply)

  def extractRequestedSalariesList: Option[RequestedSalaries] => List[RequestedSalary] =
    CommaSeparatedStrings.extractList(_)(_.value, RequestedSalary.apply)
}
