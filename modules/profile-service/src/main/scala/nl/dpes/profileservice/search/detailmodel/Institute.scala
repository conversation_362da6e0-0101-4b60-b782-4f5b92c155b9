package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type Institute = String
object Institute {
  def apply(institute: String): Institute = institute

  given Encoder[Institute] = Encoder.encodeString
  given Decoder[Institute] = Decoder.decodeString
  given Schema[Institute] = Schema.schemaForString

  given Transformer[detailpage.Institute, Institute] = (institute: detailpage.Institute) => Institute(institute.value)
}
