package nl.dpes.profileservice.search.searchmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch
import sttp.tapir.Schema

opaque type LastName = String
object LastName {
  def apply(name: String): LastName = name
  given Encoder[LastName] = Encoder.encodeString
  given Decoder[LastName] = Decoder.decodeString
  given Schema[LastName] = Schema.schemaForString

  given Transformer[profilesearch.LastName, LastName] = (lastName: profilesearch.LastName) => LastName(lastName.value)
}
