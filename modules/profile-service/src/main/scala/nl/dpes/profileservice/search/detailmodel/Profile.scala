package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.circe.generic.semiauto
import sttp.tapir.Schema

case class Profile(
  site: SiteName,
  name: Option[Name],
  updatedDate: UpdatedDate,
  photo: Option[PhotoPath],
  introductionText: Option[Introduction],
  availability: Option[Availability],
  requestedSalary: Option[RequestedSalary],
  educationLevels: List[WorkLevel],
  preferredJobs: List[PreferredJob],
  functionGroups: List[FunctionGroup],
  emailAddress: EmailAddress,
  phoneNumber: Option[PhoneNumber],
  commute: Option[Commute],
  workingHours: Option[WorkingHours],
  experiences: List[Experience],
  educations: List[Education],
  training: List[Training],
  driversLicenses: List[DriversLicense],
  attachments: List[Attachment],
  isFavorite: Boolean
) derives Decoder,
      Schema {
  def setAsFavorite: Profile = this.copy(isFavorite = true)
}

object Profile {
  given Encoder[Profile] = semiauto.deriveEncoder[Profile].mapJson(_.deepDropNullValues)
}
