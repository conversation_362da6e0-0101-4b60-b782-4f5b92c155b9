package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type MinWorkingHours = Int
object MinWorkingHours {
  def apply(hours: Int): MinWorkingHours = hours

  given Encoder[MinWorkingHours] = Encoder.encodeInt
  given Decoder[MinWorkingHours] = Decoder.decodeInt
  given Schema[MinWorkingHours] = Schema.schemaForInt

  given Transformer[detailpage.MinWorkingHours, MinWorkingHours] = (minWorkingHours: detailpage.MinWorkingHours) =>
    MinWorkingHours(minWorkingHours.value)
}
