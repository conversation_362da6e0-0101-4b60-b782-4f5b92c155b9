package nl.dpes.profileservice.search
package searchmodel

import nl.dpes.profilesearch.service.model.dbmodel.FacetsResult.TermBucket
import nl.dpes.profileservice.search.searchmodel.filter.AggregationKeyName

case class SearchResult(
  profiles: List[ProfileSearchResult],
  // todo: Use domain type for aggregations
  aggregations: Map[AggregationKeyName, List[TermBucket]],
  totalNumberOfProfiles: Long = 0
) {
  def flagFavorites(favorites: List[ProfileId]): SearchResult = copy(profiles = profiles.map { profile =>
    profile.copy(isFavorite = favorites.contains(profile.id))
  })
}
