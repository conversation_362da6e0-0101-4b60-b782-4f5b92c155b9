package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type UpdatedDate = Long

object UpdatedDate {
  def apply(timestamp: Long): UpdatedDate = timestamp

  given Encoder[UpdatedDate] = Encoder.encodeLong
  given Decoder[UpdatedDate] = Decoder.decodeLong
  given Schema[UpdatedDate] = Schema.schemaForLong

  given Transformer[detailpage.UpdatedDate, UpdatedDate] = (timestamp: detailpage.UpdatedDate) => UpdatedDate(timestamp.value)
}
