package nl.dpes.profileservice.search.searchmodel.filter

import nl.dpes.profilesearch.utils.CommaSeparatedStrings
import sttp.tapir.{Codec, CodecFormat}

case class Provinces(value: String) extends AnyVal

object Provinces {
  given Codec[String, Provinces, CodecFormat.TextPlain] = Codec.string.map(Provinces(_))(_.value)

  def createStringifiedProvinces: List[Province] => Option[Provinces] =
    CommaSeparatedStrings.stringify(_)(_.value, Provinces.apply)

  def extractProvincesList: Option[Provinces] => List[Province] =
    CommaSeparatedStrings.extractList(_)(_.value, Province.apply)
}
