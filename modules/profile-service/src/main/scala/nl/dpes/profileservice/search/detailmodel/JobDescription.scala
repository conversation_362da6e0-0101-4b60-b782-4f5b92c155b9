package nl.dpes.profileservice.search.detailmodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type JobDescription = String
object JobDescription {
  def apply(description: String): JobDescription = description

  given Encoder[JobDescription] = Encoder.encodeString
  given Decoder[JobDescription] = Decoder.decodeString
  given Schema[JobDescription] = Schema.schemaForString

  given Transformer[detailpage.JobDescription, JobDescription] = (description: detailpage.JobDescription) =>
    JobDescription(description.value)
}
