package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type Introduction = String
object Introduction {
  def apply(introduction: String): Introduction = introduction

  given Encoder[Introduction] = Encoder.encodeString
  given Decoder[Introduction] = Decoder.decodeString
  given Schema[Introduction] = Schema.schemaForString

  given Transformer[detailpage.Introduction, Introduction] = (introduction: detailpage.Introduction) => Introduction(introduction.value)
}
