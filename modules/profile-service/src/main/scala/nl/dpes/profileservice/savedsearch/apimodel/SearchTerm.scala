package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.{partial, PartialTransformer, Transformer}
import sttp.tapir.Schema
import nl.dpes.profilesearch.domain.SearchTerm as DomainSearchTerm

opaque type SearchTerm = String

object SearchTerm {
  given Encoder[SearchTerm] = Encoder.encodeString
  given Decoder[SearchTerm] = Decoder.decodeString
  given Schema[SearchTerm] = Schema.string
  given Transformer[DomainSearchTerm, SearchTerm] = (term: DomainSearchTerm) => term.value
  given PartialTransformer[SearchTerm, DomainSearchTerm] = PartialTransformer[SearchTerm, DomainSearchTerm] { (searchTerm: SearchTerm) =>
    partial.Result.fromOption(DomainSearchTerm.fromString(searchTerm))
  }

  def apply(searchTerm: String): SearchTerm = searchTerm
  extension (searchTerm: SearchTerm) def value: String = searchTerm
}
