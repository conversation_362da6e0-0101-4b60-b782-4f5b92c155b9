package nl.dpes.profileservice
package search.searchmodel.filter

import sttp.tapir.{Codec, CodecFormat, DecodeResult}

enum CommuteDistance private:
  case Distance(value: Int)
  case WholeCountry

object CommuteDistance:
  sealed abstract class CommuteDistanceError(message: String) extends Throwable(message)
  case class NegativeDistance(value: Int) extends CommuteDistanceError(s"Negative distance not allowed, got '$value''")
  case class NotCommuteDistance(value: String) extends CommuteDistanceError(s"Not a valid commute distance, got '$value'")

  object Distance:
    def apply(value: Int): Either[CommuteDistanceError, CommuteDistance.Distance] =
      if (value < 0) Left(NegativeDistance(value))
      else Right(new CommuteDistance.Distance(value))

  def apply(value: String): Either[CommuteDistanceError, CommuteDistance] =
    if (value.equalsIgnoreCase("Heel Nederland")) Right(WholeCountry)
    else
      value.replace("km", "").toIntOption match
        case Some(intValue) => Distance(intValue)
        case None           => Left(NotCommuteDistance(value))

  given Codec[String, CommuteDistance, CodecFormat.TextPlain] =
    Codec.string.mapDecode(value =>
      CommuteDistance(value) match {
        case Left(error)     => DecodeResult.Error(value, error)
        case Right(distance) => DecodeResult.Value(distance)
      }
    ) {
      case CommuteDistance.WholeCountry    => "Heel Nederland"
      case CommuteDistance.Distance(value) => value.toString
    }
