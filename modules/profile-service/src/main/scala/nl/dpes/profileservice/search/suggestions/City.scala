package nl.dpes.profileservice.search.suggestions

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch.suggestions.CitySuggestion as SearchCity
import sttp.tapir.Schema

opaque type City = String
object City {
  def apply(value: String): City = value
  extension (city: City) {
    def value: String = city
  }
  given Encoder[City] = Encoder.encodeString
  given Decoder[City] = Decoder.decodeString
  given Schema[City] = Schema.schemaForString

  given Transformer[SearchCity, City] = (searchCity: SearchCity) => City(searchCity.value)
}
