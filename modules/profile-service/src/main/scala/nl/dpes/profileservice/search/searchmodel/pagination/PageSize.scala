package nl.dpes.profileservice.search.searchmodel.pagination

import io.scalaland.chimney.Transformer
import sttp.tapir.Codec
import sttp.tapir.CodecFormat.TextPlain
import nl.dpes.profilesearch.service.model.profilesearch.pagination

case class PageSize private (value: Int) extends AnyVal

object PageSize {
  val default = new PageSize(10)

  def apply(value: Int): Either[String, PageSize] =
    if (value < 1) Left(s"Page size ($value) must be positive")
    else Right(new PageSize(value))

  given Codec[String, PageSize, TextPlain] = Codec.int.mapEither(apply)(_.value)
  given Transformer[PageSize, pagination.PageSize] = (pageSize: PageSize) => pagination.PageSize.unsafe(pageSize.value)
}
