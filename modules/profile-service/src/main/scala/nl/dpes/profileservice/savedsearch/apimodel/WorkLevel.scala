package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.domain.WorkLevel as DomainWorkLevel
import sttp.tapir.Schema

opaque type WorkLevel = String

object WorkLevel {
  given Encoder[WorkLevel] = Encoder.encodeString
  given Decoder[WorkLevel] = Decoder.decodeString
  given Schema[WorkLevel] = Schema.string
  given Transformer[DomainWorkLevel, WorkLevel] = (workLevel: DomainWorkLevel) => WorkLevel(workLevel.value)
  given Transformer[WorkLevel, DomainWorkLevel] = (workLevel: WorkLevel) => DomainWorkLevel(workLevel)

  def apply(workLevel: String): WorkLevel = workLevel
}
