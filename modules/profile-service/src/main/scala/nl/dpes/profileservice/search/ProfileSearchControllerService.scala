package nl.dpes.profileservice
package search

import cats.Functor
import cats.effect.{Async, Resource}
import io.circe.Error
import io.scalaland.chimney.dsl.*
import nl.dpes.profileservice.tapir.ErrorMessage.*
import nl.dpes.profilesearch.creditservice.CreditServiceClient.NotEntitled
import nl.dpes.profilesearch.domain.Error.ProfileNotFound
import nl.dpes.profilesearch.domain.filter.Filter
import nl.dpes.profilesearch.service.ProfileSearchService
import nl.dpes.profilesearch.service.lock.LockingResult.LockTimedOut
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpes.profilesearch.service.model.profilesearch.pagination.Pagination as SearchPagination
import nl.dpes.profilesearch.service.model.profilesearch.sort.{SortField as SearchSortField, SortOrder as SearchSortOrder}
import nl.dpes.profilesearch.service.model.profilesearch.suggestions.CityPrefix as SearchCityPrefix
import nl.dpes.profilesearch.service.model.profilesearch.{ProfileId as SearchProfileId, RecruiterId as SearchRecruiterId}
import nl.dpes.profilesearch.service.visitedprofiles.VisitedProfilesService.ViewsExceeded
import nl.dpes.profileservice.search.detailmodel.Profile
import nl.dpes.profileservice.search.searchmodel.*
import nl.dpes.profileservice.search.searchmodel.pagination.*
import nl.dpes.profileservice.search.searchmodel.sort.*
import nl.dpes.profileservice.search.suggestions.{City as SuggestedCity, *}
import nl.dpes.profileservice.tapir.ErrorMessage
import nl.dpes.recruiterfavorites.service.FavoritesService
import nl.dpes.recruiterfavorites.service.domainmodel.{ProfileId as FavoriteId, RecruiterId as FavoriteRecruiterId}
import org.typelevel.log4cats.LoggerFactory

trait ProfileSearchControllerService[F[_]] {

  def getCitySuggestions(cityPrefix: CityPrefix): F[Either[UnknownError, List[SuggestedCity]]]

  def getProfiles(
    pagination: Pagination,
    filters: List[Filter] = List.empty,
    sortField: Option[SortField] = None,
    sortOrder: Option[SortOrder] = None,
    withoutAggregations: Boolean = false,
    recruiterId: Option[RecruiterId] = None
  ): F[Either[BadRequest, SearchResult]]

  def getProfilesById(ids: List[ProfileId], recruiterId: Option[RecruiterId]): F[Either[UnknownError, SearchResult]]

  def getProfile(recruiterId: RecruiterId, profileId: ProfileId): F[Either[ErrorMessage, Profile]]
}

object ProfileSearchControllerService {

  def impl[F[+_]: {Async, Functor, LoggerFactory}](
    profileSearchService: ProfileSearchService[F],
    favoritesService: FavoritesService[F]
  ): ProfileSearchControllerService[F] =
    new ProfileSearchControllerService[F] {

      override def getCitySuggestions(cityPrefix: CityPrefix): F[Either[UnknownError, List[SuggestedCity]]] =
        profileSearchService
          .getCitySuggestions(cityPrefix.transformInto[SearchCityPrefix])
          .attempt
          .flatMap {
            case Right(cities) => cities.into[List[SuggestedCity]].transform.asRight[UnknownError].pure[F]
            case Left(error)   => UnknownError(error.getMessage).asLeft[List[SuggestedCity]].pure[F]
          }

      override def getProfiles(
        pagination: Pagination,
        filters: List[Filter],
        sortField: Option[SortField],
        sortOrder: Option[SortOrder],
        withoutAggregations: Boolean = false,
        recruiterId: Option[RecruiterId] = None
      ): F[Either[BadRequest, SearchResult]] = for {
        profiles <-
          profileSearchService
            .getProfiles(
              pagination.transformInto[SearchPagination],
              filters,
              // sorting should be combined in Sorting, preferably in a request object with the other properties
              sortField.into[Option[SearchSortField]].transform,
              sortOrder.into[Option[SearchSortOrder]].transform,
              recruiterId.into[Option[SearchRecruiterId]].transform,
              withoutAggregations
            )
        favorites <- recruiterId match {
          case Some(id) =>
            favoritesService
              .filterFavorites(
                id.into[FavoriteRecruiterId].transform,
                profiles.profiles.map(_.id.into[ProfileId].transform.into[FavoriteId].transform)
              )
          case None => List().pure
        }
      } yield profiles
        .into[SearchResult]
        .transform
        .flagFavorites(favorites.into[List[ProfileId]].transform)
        .asRight[BadRequest] // asRight can go after changing function signature in controller

      override def getProfile(recruiterId: RecruiterId, profileId: ProfileId): F[Either[ErrorMessage, Profile]] =
        (for {
          profile <- profileSearchService
            .getProfile(recruiterId.into[SearchRecruiterId].transform, profileId.into[SearchProfileId].transform)
            .map(_.into[Profile].transform)
          completeProfile <- favoritesService
            .isFavorite(recruiterId.into[FavoriteRecruiterId].transform, profileId.into[FavoriteId].transform)
            .map(isFavorite => if (isFavorite) profile.setAsFavorite else profile)
        } yield completeProfile.asRight[ErrorMessage])
          .recover {
            case e: ProfileNotFound  => Left(NotFound(e.getMessage))
            case ViewsExceeded(_, _) => Left(TooManyRequests("Profile view limit exceeded"))
            case NotEntitled(_, _)   => Left(Forbidden("Not entitled to view profiles"))
            case LockTimedOut(_, _)  => Left(Timeout(s"Retieving profile ${profileId.value} timed out"))
          }

      override def getProfilesById(ids: List[ProfileId], recruiterId: Option[RecruiterId]): F[Either[UnknownError, SearchResult]] =
        profileSearchService
          .getProfilesById(ids.into[List[profilesearch.ProfileId]].transform, recruiterId.into[Option[SearchRecruiterId]].transform)
          .map(_.into[SearchResult].transform.asRight[UnknownError])
          .recover { case e: Error =>
            Left(UnknownError(e.getMessage))
          }
    }

  def resource[F[+_]: {Async, Functor, LoggerFactory}](
    profileSearchService: ProfileSearchService[F],
    favoritesService: FavoritesService[F]
  ): Resource[F, ProfileSearchControllerService[F]] =
    Resource.eval(Async[F].delay(impl(profileSearchService, favoritesService)))
}
