package nl.dpes.profileservice.config

import nl.dpes.profilesearch.config.{BlazeServerConfig, ElasticsearchConfig}
import nl.dpes.profilesearch.creditservice.CreditServiceConfig
import nl.dpes.profilesearch.database.config.DbConfig
import nl.dpes.profilesearch.service.lock.LockConfig
import nl.dpes.profilesearch.service.unlockedprofiles.UnlockedProfileConfig
import nl.dpes.profilesearch.service.viewedprofiles.ViewedProfilesConfig
import nl.dpes.profilesearch.service.visitedprofiles.VisitedProfilesConfig
import nl.dpes.recruiterfavorites.recruiterfavoritesstorage.config.FavoriteProfilesConfig
import nl.dpes.savedsearch.config.SavedSearchConfig
import pureconfig.ConfigReader

final case class AppConfig(
  blazeServerConfig: BlazeServerConfig,
  elasticsearchConfig: ElasticsearchConfig,
  databaseConfig: DbConfig,
  visitedProfilesConfig: VisitedProfilesConfig,
  creditServiceConfig: CreditServiceConfig,
  unlockedProfileConfig: UnlockedProfileConfig,
  favoriteProfilesConfig: FavoriteProfilesConfig,
  viewedProfilesConfig: ViewedProfilesConfig,
  lockConfig: LockConfig,
  savedSearchConfig: SavedSearchConfig
) derives ConfigReader
