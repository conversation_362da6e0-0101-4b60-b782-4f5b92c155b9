package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.generic.semiauto.*
import io.scalaland.chimney.{PartialTransformer, Transformer}
import sttp.tapir.Schema
import nl.dpes.savedsearch.domain.SavedSearch as DomainSavedSearch

import java.util.UUID

case class SavedSearch(
  id: SavedSearchId,
  recruiterId: RecruiterId,
  name: SavedSearchName,
  frequency: Frequency,
  searchFilters: SearchFilters
)

object SavedSearch {
  given Encoder[SavedSearch] = deriveEncoder
  given Decoder[SavedSearch] = deriveDecoder
  given Schema[SavedSearch] = Schema.derived[SavedSearch]
  given Transformer[DomainSavedSearch, SavedSearch] = Transformer
    .define[DomainSavedSearch, SavedSearch]
    .withFieldRenamed(_.filters, _.searchFilters)
    .buildTransformer
  given PartialTransformer[SavedSearch, DomainSavedSearch] = PartialTransformer
    .define[SavedSearch, DomainSavedSearch]
    .withFieldRenamed(_.searchFilters, _.filters)
    .buildTransformer
}
