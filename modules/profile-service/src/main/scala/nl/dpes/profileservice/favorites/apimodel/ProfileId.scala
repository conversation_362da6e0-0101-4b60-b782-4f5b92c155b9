package nl.dpes.profileservice.favorites.apimodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.recruiterfavorites.service.domainmodel.ProfileId as DomainProfileId
import nl.dpes.profilesearch.service.model.profilesearch.ProfileId as SearchProfileId
import sttp.tapir.{Codec, CodecFormat}

case class ProfileId(value: String)

object ProfileId {
  given Encoder[ProfileId] = Encoder.encodeString.contramap(_.value)
  given Decoder[ProfileId] = Decoder.decodeString.emap(ProfileId(_))
  given Transformer[ProfileId, DomainProfileId] = id => DomainProfileId.apply(id.value)
  given Transformer[DomainProfileId, ProfileId] = id => new ProfileId(id.value)
  given Transformer[DomainProfileId, SearchProfileId] = id => SearchProfileId.unsafe(id.value)
  given Codec[String, ProfileId, CodecFormat.TextPlain] = Codec.string.mapEither(ProfileId(_))(_.value)

  def apply(profileId: String): Either[String, ProfileId] = {
    val trimmerProfileId = profileId.trim
    if (trimmerProfileId.length == 36)
      Right(new ProfileId(trimmerProfileId))
    else
      Left(s"ProfileId is a 36-character identifier. You have provided '${trimmerProfileId.length}' characters.")
  }
}
