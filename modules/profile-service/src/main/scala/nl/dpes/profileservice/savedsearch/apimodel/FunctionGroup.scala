package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.domain.FunctionGroup as DomainFunctionGroup
import sttp.tapir.Schema

opaque type FunctionGroup = String

object FunctionGroup {
  given Encoder[FunctionGroup] = Encoder.encodeString
  given Decoder[FunctionGroup] = Decoder.decodeString
  given Schema[FunctionGroup] = Schema.string
  given Transformer[DomainFunctionGroup, FunctionGroup] = (functionGroup: DomainFunctionGroup) => FunctionGroup(functionGroup.value)
  given Transformer[FunctionGroup, DomainFunctionGroup] = (functionGroup: FunctionGroup) => DomainFunctionGroup(functionGroup)

  def apply(functionGroup: String): FunctionGroup = functionGroup
}
