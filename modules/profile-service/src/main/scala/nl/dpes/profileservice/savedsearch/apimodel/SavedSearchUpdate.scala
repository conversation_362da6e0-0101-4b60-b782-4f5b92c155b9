package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.generic.semiauto.*
import io.scalaland.chimney.Transformer
import sttp.tapir.Schema.annotations.description

case class SavedSearchUpdate(
  @description(
    s"Represents the search frequency. When updating a saved search, it supports these values: ${Frequency.supportedValues.mkString(", ")}."
  )
  frequency: Frequency
)

object SavedSearchUpdate {
  given Encoder[SavedSearchUpdate] = deriveEncoder
  given Decoder[SavedSearchUpdate] = deriveDecoder

  object SwaggerDoc {
    val example: SavedSearchUpdate = SavedSearchUpdate(Frequency.unsafeApply("<PERSON><PERSON><PERSON>jks"))
  }
}
