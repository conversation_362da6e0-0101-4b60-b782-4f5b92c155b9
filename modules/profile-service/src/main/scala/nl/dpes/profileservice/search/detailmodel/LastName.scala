package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type LastName = String
object LastName {
  def apply(name: String): LastName = name

  given Encoder[LastName] = Encoder.encodeString
  given Decoder[LastName] = Decoder.decodeString
  given Schema[LastName] = Schema.schemaForString

  given Transformer[detailpage.LastName, LastName] = (lastName: detailpage.LastName) => LastName(lastName.value)
}
