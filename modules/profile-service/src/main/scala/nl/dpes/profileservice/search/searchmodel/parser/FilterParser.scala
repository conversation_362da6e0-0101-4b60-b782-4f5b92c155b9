package nl.dpes.profileservice
package search.searchmodel.parser

import cats.data.*
import nl.dpes.profilesearch.domain.*
import nl.dpes.profilesearch.domain.filter.*
import nl.dpes.profilesearch.utils.SimpleSearchQueryTranslator
import nl.dpes.profileservice.search.searchmodel.{ProfileSearchFilters, SearchTerm}
import nl.dpes.profileservice.search.searchmodel.filter
import nl.dpes.profileservice.search.searchmodel.filter.GeoDistance

import scala.util.Try

object FilterParser {
  def parseFilters(
    profileSearchFilters: ProfileSearchFilters
  ): ValidatedNel[Throwable, List[Filter]] = {
    val findableFilter = getFindableFilter
    val validatedCareerLevels = getCareerLevelFilters(profileSearchFilters.careerLevels)
    val cityFilter = Validated.Valid(profileSearchFilters.location.map(l => CityFilter(City(l.value))).toList).toValidatedNel
    val geoDistanceFilter = profileSearchFilters.geoDistance
      .map(getGeoDistance(_, profileSearchFilters.commuteDistance))
      .getOrElse(Validated.validNel(List.empty[Filter]))
    val provinceFilter = getProvinceFilter(profileSearchFilters.provinces)
    val updatedDateFilter = getUpdateDateFilter(profileSearchFilters.updatedDate)
    val functionGroupFilter = getFunctionGroupFilters(profileSearchFilters.functionGroups)
    val workLevelFilter = getWorkLevelFilters(profileSearchFilters.workLevels)
    val workingHourFilter = getWorkingHourFilters(profileSearchFilters.workingHours)
    val availabilityFilter = getAvailabilityFilters(profileSearchFilters.availabilities)
    val languageFilter = getLanguageFilters(profileSearchFilters.languages)
    val driversLicenseFilter = getDriversLicenseFilters(profileSearchFilters.driverLicenses)
    val requestedSalaryFilter = getRequestedSalaryFilters(profileSearchFilters.requestedSalaries)
    val searchFilter = getSearchTermFilter(profileSearchFilters.searchTerm)

    collectAllValidatedResults(
      findableFilter,
      availabilityFilter,
      validatedCareerLevels,
      cityFilter,
      geoDistanceFilter,
      provinceFilter,
      updatedDateFilter,
      functionGroupFilter,
      workLevelFilter,
      workingHourFilter,
      languageFilter,
      driversLicenseFilter,
      requestedSalaryFilter,
      searchFilter
    )
  }

  private def getGeoDistance(
    geoDistance: GeoDistance,
    maxCommuteDistance: Option[filter.CommuteDistance]
  ): ValidatedNel[Throwable, List[Filter]] =
    Validated
      .fromEither(GeoDistanceParser.parse(geoDistance.value, maxCommuteDistance))
      .map(List(_))
      .toValidatedNel

  private def getFindableFilter: ValidatedNel[Throwable, List[Filter]] =
    Validated.valid(List(FindableFilter))

  private def getProvinceFilter(provinces: List[filter.Province]): ValidatedNel[Throwable, List[Filter]] =
    Validated
      .valid(
        NonEmptyList
          .fromList(provinces.map(filter => Province(filter.value)))
          .map(ProvinceFilter.apply)
          .toList
      )
      .toValidatedNel

  private def getCareerLevelFilters(careerLevels: List[filter.CareerLevel]): ValidatedNel[Throwable, List[Filter]] =
    careerLevels
      .map(filter => Validated.Valid(CareerLevel(filter.value)))
      .traverse(_.toValidatedNel)
      .map(filters => NonEmptyList.fromList(filters).map(CareerLevelFilter.apply).toList)

  private def getUpdateDateFilter(updatedDate: Option[filter.UpdatedDate]): ValidatedNel[Throwable, List[Filter]] = updatedDate
    .map { date =>
      Validated
        .fromOption(
          UpdateDate.fromString(date.value),
          new Throwable(s"Invalid updated date: ${date.value}. It should be one of ${UpdateDate.values.mkString("Array(", ", ", ")")}")
        )
        .map(updateDate => List(UpdateDateFilter(updateDate)))
    }
    .map(_.toValidatedNel[Throwable, List[Filter]])
    .getOrElse(Validated.validNel(List.empty[Filter]))

  private def getFunctionGroupFilters(functionGroups: List[filter.FunctionGroup]): ValidatedNel[Throwable, List[Filter]] =
    functionGroups
      .map(filter => Validated.Valid(FunctionGroup(filter.value)))
      .traverse(_.toValidatedNel)
      .map(filters => NonEmptyList.fromList(filters).map(FunctionGroupFilter.apply).toList)

  private def getWorkLevelFilters(workLevels: List[filter.WorkLevel]): ValidatedNel[Throwable, List[Filter]] =
    workLevels
      .map(filter => Validated.valid(WorkLevel(filter.value)))
      .traverse(_.toValidatedNel)
      .map(filters => NonEmptyList.fromList(filters).map(WorkLevelFilter.apply).toList)

  private def getWorkingHourFilters(workingHours: List[filter.WorkingHour]): Validated[NonEmptyList[Throwable], List[Filter]] =
    workingHours
      .map(filter =>
        Validated.fromOption(
          nl.dpes.profilesearch.domain.WorkingHours.fromString(filter.value),
          new Throwable(s"Invalid working hours: ${filter.value}")
        )
      )
      .traverse(_.toValidatedNel)
      .map(filters => NonEmptyList.fromList(filters).map(WorkingHourFilter.apply).toList)

  private def getAvailabilityFilters(availabilityList: List[filter.Availability]): ValidatedNel[Throwable, List[Filter]] =
    availabilityList
      .map(filter =>
        Validated.fromOption(
          nl.dpes.profilesearch.domain.Availability.fromString(filter.value),
          new Throwable(s"Invalid availability: ${filter.value}")
        )
      )
      .traverse(_.toValidatedNel)
      .map(filters => NonEmptyList.fromList(filters).map(AvailabilityFilter.apply).toList)

  private def getLanguageFilters(languages: List[filter.Language]): ValidatedNel[Throwable, List[Filter]] =
    languages
      .map(filter => Validated.valid(Language(filter.value)))
      .traverse(_.toValidatedNel)
      .map(filters => NonEmptyList.fromList(filters).map(LanguageFilter.apply).toList)

  private def getDriversLicenseFilters(driversLicenses: List[filter.DriversLicense]): ValidatedNel[Throwable, List[Filter]] =
    driversLicenses
      .map(filter => Validated.valid(DriversLicense(filter.value)))
      .traverse(_.toValidatedNel)
      .map(filters => NonEmptyList.fromList(filters).map(DriversLicenseFilter.apply).toList)

  private def getRequestedSalaryFilters(salaries: List[filter.RequestedSalary]): ValidatedNel[Throwable, List[Filter]] =
    salaries
      .map(filter =>
        Validated.fromOption(
          RequestedSalary.fromString(filter.value),
          new Throwable(s"Invalid requested salary: ${filter.value}")
        )
      )
      .traverse(_.toValidatedNel)
      .map(filters => NonEmptyList.fromList(filters).map(RequestedSalaryFilter.apply).toList)

  private def getSearchTermFilter(searchTerm: Option[SearchTerm]): ValidatedNel[Throwable, List[Filter]] =
    searchTerm.map(getSearchTermFilter).getOrElse(Validated.validNel(List.empty[Filter]))

//  private def getCommuteDistanceFilter(commuteDistance: Option[filter.CommuteDistance]): ValidatedNel[Throwable, List[Filter]] =
//    commuteDistance.map {
//      case filter.CommuteDistance.Distance(value) => List(CommuteDistanceFilter(CommuteDistance.Distance(value))).valid
//      case filter.CommuteDistance.WholeCountry    => List().valid
//    } getOrElse List().valid

  private def getSearchTermFilter(searchTerm: SearchTerm): ValidatedNel[Throwable, List[Filter]] = {
    val parsedSearchTerm = nl.dpes.profilesearch.domain.SearchTerm
      .fromString(searchTerm.value)
      .toRight(new Throwable(s"Invalid search term: ${searchTerm.value}"))

    Validated
      .fromEither(parsedSearchTerm)
      .map(term => List(SearchTermFilter(term)))
      .toValidatedNel
  }

  // It collects either all errors or all filters from validated values
  private def collectAllValidatedResults[E](filters: ValidatedNel[E, List[Filter]]*): ValidatedNel[E, List[Filter]] =
    filters.toList.reduce((_, _).mapN(_ ++ _))

}
