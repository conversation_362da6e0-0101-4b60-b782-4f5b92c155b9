package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type RequestedSalary = String
object RequestedSalary {
  def apply(salary: String): RequestedSalary = salary

  extension (desiredSalary: RequestedSalary) {
    def value: String = desiredSalary
  }
  given Encoder[RequestedSalary] = Encoder.encodeString
  given Decoder[RequestedSalary] = Decoder.decodeString
  given Schema[RequestedSalary] = Schema.schemaForString

  given Transformer[detailpage.RequestedSalary, RequestedSalary] = (requestedSalary: detailpage.RequestedSalary) =>
    RequestedSalary(requestedSalary.value)
}
