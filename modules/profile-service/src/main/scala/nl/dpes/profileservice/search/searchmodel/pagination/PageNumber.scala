package nl.dpes.profileservice.search.searchmodel.pagination

import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch.pagination
import sttp.tapir.Codec
import sttp.tapir.CodecFormat.TextPlain

case class PageNumber private (value: Int) extends AnyVal

object PageNumber {
  val firstPage = new PageNumber(1)

  def apply(value: Int): Either[String, PageNumber] =
    if (value < 1) Left(s"Page number ($value) must be positive")
    else Right(new PageNumber(value))

  given Codec[String, PageNumber, TextPlain] = Codec.int.mapEither(apply)(_.value)
  given Transformer[PageNumber, pagination.PageNumber] = (pageNumber: PageNumber) => pagination.PageNumber.unsafe(pageNumber.value)
}
