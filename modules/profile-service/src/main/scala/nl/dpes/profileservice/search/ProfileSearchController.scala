package nl.dpes.profileservice
package search

import cats.data.{EitherT, Validated}
import cats.effect.Async
import io.circe.generic.auto.*
import nl.dpes.profileservice.tapir.ErrorMessage.*
import nl.dpes.profilesearch.domain.filter.Filter
import nl.dpes.profileservice.search.detailmodel.Profile
import nl.dpes.profileservice.search.searchmodel.*
import nl.dpes.profileservice.search.searchmodel.filter.CommuteDistance
import nl.dpes.profileservice.search.searchmodel.pagination.*
import nl.dpes.profileservice.search.searchmodel.parser.FilterParser
import nl.dpes.profileservice.search.searchmodel.sort.*
import nl.dpes.profileservice.search.suggestions.{City as SuggestedCity, CityPrefix}
import nl.dpes.profileservice.tapir.ErrorMessage
import nl.dpes.profileservice.tapir.jsonvalidation.ValidatedJson.validatedJsonBody
import sttp.model.StatusCode
import sttp.tapir.*
import sttp.tapir.generic.auto.*
import sttp.tapir.json.circe.jsonBody
import sttp.tapir.server.ServerEndpoint

trait ProfileSearchController[F[_]] {
  def getCitySuggestions: F[ServerEndpoint[Any, F]]
  def getProfiles: F[ServerEndpoint[Any, F]]
  def getFavorites: F[ServerEndpoint[Any, F]]
  def getProfile: F[ServerEndpoint[Any, F]]
  def endpoints: F[List[ServerEndpoint[Any, F]]]
}

object ProfileSearchController {

  def impl[F[_]: Async](profileSearchService: ProfileSearchControllerService[F]): ProfileSearchController[F] =
    new ProfileSearchController[F] {

      override def getCitySuggestions: F[ServerEndpoint[Any, F]] =
        Async[F].delay {
          endpoint.get
            .description("Returns autocompleted city name suggestions")
            .in("profiles" / "autocomplete" / "cities")
            .in(query[CityPrefix]("cityPrefix").description("The city prefix to search for"))
            .out(jsonBody[List[SuggestedCity]])
            .errorOut(statusCode(StatusCode.BadRequest).and(jsonBody[UnknownError]))
            .serverLogic(profileSearchService.getCitySuggestions)
        }

      override def getProfiles: F[ServerEndpoint[Any, F]] = Async[F].delay {
        endpoint.get
          .description("Returns the profiles sorted in descending order by 'updatedDate'")
          .in("profiles")
          .in(query[PageNumber]("pageNumber").description("This represents the page number").default(PageNumber.firstPage))
          .in(
            query[PageSize]("pageSize")
              .description("This represents the maximum number of profiles to be shown per page")
              .default(PageSize.default)
          )
          .mapIn((pn, pz) => Pagination(pn, pz))(p => (p.pageNumber, p.pageSize))
          .in(
            query[Option[SearchTerm]]("query")
              .description("The query to search for")
          )
          .in(
            query[Option[filter.Location]]("city")
              .description("The city to search at")
              .map(filter.Location.cleanupLocation)(identity)
          )
          .in(
            query[Option[filter.GeoDistance]]("geoDistance")
              .description("The latitude and longitude for the geo search, comma separated (e.g. '52.3676,4.9041')")
          )
          .in(
            query[Option[filter.CommuteDistance]]("commuteDistance")
              .description("The maximum distance from the location to search, can be a positive amount of km or 'Heel Nederland'")
          )
          .in(
            query[Option[filter.Provinces]]("provinces")
              .description("The list of provinces to filter by. It's a comma-separated list.")
              .map(filter.Provinces.extractProvincesList)(filter.Provinces.createStringifiedProvinces)
          )
          .in(
            query[Option[filter.UpdatedDate]]("updatedDate")
              .description("The last modification date for profiles.")
              .map(filter.UpdatedDate.cleanupUpdatedDate)(identity)
          )
          .in(
            query[Option[filter.FunctionGroups]]("functionGroups")
              .description("The list of function groups to filter by. It's a comma-separated list.")
              .map(filter.FunctionGroups.extractFunctionGroupsList)(filter.FunctionGroups.createStringifiedFunctionGroups)
          )
          .in(
            query[Option[filter.WorkLevels]]("workLevels")
              .description("The list of work levels to filter by. It's a comma-separated list.")
              .map(filter.WorkLevels.extractWorkLevelsList)(filter.WorkLevels.createStringifiedWorkLevels)
          )
          .in(
            query[Option[filter.WorkingHours]]("workingHours")
              .description("The list of preferred working hours to filter by. It's a comma-separated list.")
              .map(filter.WorkingHours.extractWorkingHoursList)(filter.WorkingHours.createStringifiedWorkingHours)
          )
          .in(
            query[Option[filter.CareerLevels]]("careerLevels")
              .description("The career levels to filter by. It's a comma-separated list.")
              .map(filter.CareerLevels.extractCareerLevelsList)(filter.CareerLevels.createStringifiedCareerLevels)
          )
          .in(
            query[Option[filter.RequestedSalaries]]("requestedSalaries")
              .description("The requested salaries to filter by. It's a comma-separated list.")
              .map(filter.RequestedSalaries.extractRequestedSalariesList)(filter.RequestedSalaries.createStringifiedRequestedSalaries)
          )
          .in(
            query[Option[filter.Availabilities]]("availabilities")
              .description("The availabilities to filter by. It's a comma-separated list.")
              .map(filter.Availabilities.extractAvailabilitiesList)(filter.Availabilities.createStringifiedAvailabilities)
          )
          .in(
            query[Option[filter.DriverLicenses]]("driverLicenses")
              .description("The driver licences to filter by. It's a comma-separated list.")
              .map(filter.DriverLicenses.extractDriverLicensesList)(filter.DriverLicenses.createStringifiedDriverLicenses)
          )
          .in(
            query[Option[filter.Languages]]("languages")
              .description("The languages to filter by. It's a comma-separated list.")
              .map(filter.Languages.extractLanguagesList)(filter.Languages.createStringifiedLanguages)
          )
          .in(
            query[Option[SortField]]("sortField")
              .default(SortField.relevance.some)
              .description(
                "SortField can be either 'relevance' or 'updatedDate', if not provided relevance will be used"
              )
          )
          .in(
            query[Option[SortOrder]]("sortOrder")
              .default(SortOrder.desc.some)
              .description("SortOrder can be either 'asc' or 'desc', if not provided the default for the sort field will be used")
          )
          .in(
            query[Boolean]("withoutAggregations")
              .default(false)
              .description("Exclude aggregations from the response, default is false")
          )
          .in(header[Option[RecruiterId]]("X-Recruiter-ID"))
          .out(jsonBody[SearchResultResponse])
          .errorOut(
            oneOf(
              oneOfVariant[BadRequest](statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest])),
              oneOfVariant[UnknownError](statusCode(StatusCode.InternalServerError).and(jsonBody[UnknownError]))
            )
          )
          .serverLogic[F](searchProfiles)
      }

      private def searchProfiles(
        pagination: Pagination,
        searchTerm: Option[SearchTerm] = None,
        location: Option[filter.Location] = None,
        geoDistance: Option[filter.GeoDistance] = None,
        commuteDistance: Option[filter.CommuteDistance] = None,
        provinces: List[filter.Province] = List.empty,
        updatedDate: Option[filter.UpdatedDate] = None,
        functionGroups: List[filter.FunctionGroup] = List.empty,
        workLevels: List[filter.WorkLevel] = List.empty,
        workingHours: List[filter.WorkingHour] = List.empty,
        careerLevels: List[filter.CareerLevel] = List.empty,
        requestedSalaries: List[filter.RequestedSalary] = List.empty,
        availabilities: List[filter.Availability] = List.empty,
        driverLicenses: List[filter.DriversLicense] = List.empty,
        languages: List[filter.Language] = List.empty,
        sortField: Option[SortField] = None,
        sortOrder: Option[SortOrder] = None,
        withoutAggregations: Boolean = false,
        recruiterId: Option[RecruiterId]
      ): F[Either[ErrorMessage, SearchResultResponse]] = {
        val profileSearchFilters = ProfileSearchFilters(
          searchTerm,
          location,
          geoDistance,
          commuteDistance,
          provinces,
          updatedDate,
          functionGroups,
          workLevels,
          workingHours,
          careerLevels,
          requestedSalaries,
          availabilities,
          driverLicenses,
          languages
        )

        (for {
          filters <- EitherT(parseFilters(profileSearchFilters))
          result <- EitherT(
            profileSearchService
              .getProfiles(
                pagination,
                filters,
                sortField,
                sortOrder,
                withoutAggregations,
                recruiterId
              )
              .map(_.map(SearchResultResponse.fromSearchResult))
          )
        } yield result).value

      }

      private def parseFilters(profileSearchFilters: ProfileSearchFilters): F[Either[UnknownError, List[Filter]]] =
        FilterParser.parseFilters(profileSearchFilters) match {
          case Validated.Invalid(errors) =>
            UnknownError(errors.toList.map(t => s"Invalid filter: ${t.getMessage}").mkString(", ")).asLeft[List[Filter]].pure[F]
          case Validated.Valid(filters) => Right(filters).pure[F]
        }

      override def getFavorites: F[ServerEndpoint[Any, F]] = Async[F].delay {
        endpoint.post
          .description("Returns the profiles stored as favorite")
          .in("profiles")
          .in(validatedJsonBody[FavoritesRequest])
          .in(header[Option[RecruiterId]]("X-Recruiter-ID"))
          .out(jsonBody[SearchResultResponse])
          .errorOut(statusCode(StatusCode.InternalServerError).and(jsonBody[UnknownError]))
          .serverLogic[F](getProfilesById)
      }

      def getProfilesById(
        favoritesRequest: FavoritesRequest,
        recruiterId: Option[RecruiterId]
      ): F[Either[UnknownError, SearchResultResponse]] =
        profileSearchService
          .getProfilesById(favoritesRequest.ids, recruiterId)
          .map(_.map(SearchResultResponse.fromSearchResult))

      override def getProfile: F[ServerEndpoint[Any, F]] = Async[F].delay {
        endpoint.get
          .description("Returns the profile with the given id")
          .in(header[RecruiterId]("X-Recruiter-ID"))
          .in("profile" / path[ProfileId]("id").description("The id of the profile"))
          .out(jsonBody[Profile])
          .errorOut(
            oneOf(
              oneOfVariant[BadRequest](statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest])),
              oneOfVariant[NotFound](statusCode(StatusCode.NotFound).and(jsonBody[NotFound])),
              oneOfVariant[Forbidden](statusCode(StatusCode.Forbidden).and(jsonBody[Forbidden])),
              oneOfVariant[TooManyRequests](statusCode(StatusCode.TooManyRequests).and(jsonBody[TooManyRequests])),
              oneOfVariant[Timeout](statusCode(StatusCode.GatewayTimeout).and(jsonBody[Timeout]))
            )
          )
          .serverLogic(profileSearchService.getProfile)
      }

      override def endpoints: F[List[ServerEndpoint[Any, F]]] = List(getCitySuggestions, getProfiles, getProfile, getFavorites).sequence
    }
}
