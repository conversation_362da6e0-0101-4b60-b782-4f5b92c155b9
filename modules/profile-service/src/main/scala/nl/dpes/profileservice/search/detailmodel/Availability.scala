package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.*

opaque type Availability = String
object Availability {
  def apply(value: String): Availability = value

  given Encoder[Availability] = Encoder.encodeString
  given Decoder[Availability] = Decoder.decodeString
  given Schema[Availability] = Schema.schemaForString

  given Transformer[detailpage.Availability, Availability] = (availability: detailpage.Availability) => Availability(availability.value)
}
