package nl.dpes.profileservice.search.detailmodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type AttachmentId = String
object AttachmentId {
  def apply(field: String): AttachmentId = field

  given Encoder[AttachmentId] = Encoder.encodeString
  given Decoder[AttachmentId] = Decoder.decodeString
  given Schema[AttachmentId] = Schema.schemaForString

  given Transformer[detailpage.AttachmentId, AttachmentId] = (id: detailpage.AttachmentId) => AttachmentId(id.value)
}
