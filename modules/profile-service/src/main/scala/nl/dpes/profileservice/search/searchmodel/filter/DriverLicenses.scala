package nl.dpes.profileservice.search.searchmodel.filter

import nl.dpes.profilesearch.utils.CommaSeparatedStrings
import sttp.tapir.{Codec, CodecFormat}

case class DriverLicenses(value: String)

object DriverLicenses {
  given Codec[String, DriverLicenses, CodecFormat.TextPlain] = Codec.string.map(DriverLicenses(_))(_.value)

  def createStringifiedDriverLicenses: List[DriversLicense] => Option[DriverLicenses] =
    CommaSeparatedStrings.stringify(_)(_.value, DriverLicenses.apply)

  def extractDriverLicensesList: Option[DriverLicenses] => List[DriversLicense] =
    CommaSeparatedStrings.extractList(_)(_.value, DriversLicense.apply)
}
