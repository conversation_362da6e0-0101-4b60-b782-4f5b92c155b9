package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.Transformer
import sttp.tapir.Schema
import nl.dpes.profilesearch.domain.Province as DomainProvince

opaque type Province = String

object Province {
  given Encoder[Province] = Encoder.encodeString
  given Decoder[Province] = Decoder.decodeString
  given Schema[Province] = Schema.string
  given Transformer[DomainProvince, Province] = (province: DomainProvince) => Province(province.value)
  given Transformer[Province, DomainProvince] = (province: Province) => DomainProvince(province)

  def apply(province: String): Province = province
}
