package nl.dpes.profileservice.search.searchmodel.parser

import cats.data.{Validated, ValidatedNel}
import nl.dpes.profilesearch.domain
import nl.dpes.profilesearch.domain.filter.{Filter, GeoDistanceFilter}
import nl.dpes.profileservice.search.searchmodel.filter.{CommuteDistance, GeoDistance}

import scala.util.Try

object GeoDistanceParser {
  private def coordinateParsingError(value: String): Throwable =
    new Throwable(s"Invalid format for coordinates: $value. It's expected to be a Double.")

  def parse(value: String, maxCommuteDistance: Option[CommuteDistance]): Either[Throwable, Filter] = {
    val searchRadius: Option[domain.CommuteDistance.Distance] = maxCommuteDistance.flatMap {
      case CommuteDistance.Distance(distance) => Some(domain.CommuteDistance.Distance(distance))
      case _                                  => None
    }

    val distance = for {
      values <- value.split(",").toList.map(_.trim) match {
        case lon :: lat :: Nil => Right((lon, lat))
        case _                 => Left(new Throwable(s"Invalid geo distance format: $value. Expected format: 'longitude,latitude'"))
      }
      longitude <- Try(values._1.toDouble).toEither.left.map(_ => coordinateParsingError(values._1))
      latitude  <- Try(values._2.toDouble).toEither.left.map(_ => coordinateParsingError(values._2))
    } yield GeoDistanceFilter(longitude, latitude, searchRadius)

    distance
  }
}
