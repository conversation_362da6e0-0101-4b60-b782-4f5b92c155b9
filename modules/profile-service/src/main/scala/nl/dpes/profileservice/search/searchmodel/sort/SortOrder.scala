package nl.dpes.profileservice
package search.searchmodel.sort

import cats.ApplicativeThrow
import io.circe.{Decoder, Encoder}
import sttp.tapir.{Codec, CodecFormat, Schema}
import io.scalaland.chimney.{PartialTransformer, Transformer}
import io.scalaland.chimney.partial
import nl.dpes.profilesearch.service.model.profilesearch.sort.SortOrder as SearchSortOrder

import scala.util.Try
import scala.util.control.NoStackTrace

opaque type SortOrder = String

object SortOrder {
  val desc: SortOrder = "desc"
  val asc: SortOrder = "asc"

  case class InvalidSortOrder(order: String) extends Throwable(s"Invalid sort order: '$order''") with NoStackTrace

  def apply[F[_]: ApplicativeThrow](sortOrder: String): F[SortOrder] =
    if (List(desc, asc).contains(sortOrder)) sortOrder.pure
    else InvalidSortOrder(sortOrder).raiseError

  extension (sortOrder: SortOrder) def order: String = sortOrder

  given Encoder[SortOrder] = Encoder.encodeString
  given Decoder[SortOrder] = Decoder.decodeString.emapTry(apply[Try])
  given Codec[String, SortOrder, CodecFormat.TextPlain] = Codec.string.mapEither(apply[Try](_).toEither.leftMap(_.getMessage))(_.order)

  given PartialTransformer[SortOrder, SearchSortOrder] =
    PartialTransformer[SortOrder, SearchSortOrder] { sortOrder =>
      partial.Result.fromTry(SearchSortOrder[Try](sortOrder))
    }

  given Transformer[SortOrder, SearchSortOrder] = (sortOrder: SortOrder) => SearchSortOrder.unsafe(sortOrder)
}
