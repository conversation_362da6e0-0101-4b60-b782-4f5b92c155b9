package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type EducationDescription = String
object EducationDescription {
  def apply(description: String): EducationDescription = description

  given Encoder[EducationDescription] = Encoder.encodeString
  given Decoder[EducationDescription] = Decoder.decodeString
  given Schema[EducationDescription] = Schema.schemaForString

  given Transformer[detailpage.EducationDescription, EducationDescription] =
    (description: detailpage.EducationDescription) => EducationDescription(description.value)
}
