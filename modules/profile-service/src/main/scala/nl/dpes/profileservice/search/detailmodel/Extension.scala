package nl.dpes.profileservice.search.detailmodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type Extension = String
object Extension {
  def apply(field: String): Extension = field

  given Encoder[Extension] = Encoder.encodeString
  given Decoder[Extension] = Decoder.decodeString
  given Schema[Extension] = Schema.schemaForString

  given Transformer[detailpage.Extension, Extension] = (extension: detailpage.Extension) => Extension(extension.value)
}
