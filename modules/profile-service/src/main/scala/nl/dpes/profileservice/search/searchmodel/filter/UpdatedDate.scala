package nl.dpes.profileservice.search.searchmodel.filter

import nl.dpes.profilesearch.utils.OptionalString
import sttp.tapir.{Codec, CodecFormat}

case class UpdatedDate(value: String)

object UpdatedDate {
  given Codec[String, UpdatedDate, CodecFormat.TextPlain] = Codec.string.map(UpdatedDate(_))(_.value)

  def cleanupUpdatedDate: Option[UpdatedDate] => Option[UpdatedDate] =
    OptionalString.cleanup(_)(_.value, UpdatedDate.apply)
}
