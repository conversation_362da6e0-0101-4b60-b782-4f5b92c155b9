package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type TrainingDescription = String
object TrainingDescription {
  def apply(description: String): TrainingDescription = description

  given Encoder[TrainingDescription] = Encoder.encodeString
  given Decoder[TrainingDescription] = Decoder.decodeString
  given Schema[TrainingDescription] = Schema.schemaForString

  given Transformer[detailpage.TrainingDescription, TrainingDescription] =
    (description: detailpage.TrainingDescription) => TrainingDescription(description.value)
}
