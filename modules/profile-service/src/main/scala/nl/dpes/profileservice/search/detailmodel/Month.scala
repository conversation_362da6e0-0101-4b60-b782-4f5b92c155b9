package nl.dpes.profileservice.search.detailmodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type Month = Int
object Month {
  def apply(month: Int): Month = month

  given Encoder[Month] = Encoder.encodeInt
  given Decoder[Month] = Decoder.decodeInt
  given Schema[Month] = Schema.schemaForInt

  given Transformer[detailpage.Month, Month] = (month: detailpage.Month) => Month(month.value)
}
