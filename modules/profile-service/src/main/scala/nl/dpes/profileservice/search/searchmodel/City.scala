package nl.dpes.profileservice.search.searchmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch
import sttp.tapir.Schema

opaque type City = String

object City {
  def apply(value: String): City = value
  extension (city: City) {
    def value: String = city
  }
  given Encoder[City] = Encoder.encodeString
  given Decoder[City] = Decoder.decodeString
  given Schema[City] = Schema.schemaForString

  given Transformer[profilesearch.City, City] = (city: profilesearch.City) => City(city.value)
}
