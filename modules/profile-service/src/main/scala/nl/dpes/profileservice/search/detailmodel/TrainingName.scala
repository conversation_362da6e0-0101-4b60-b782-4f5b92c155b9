package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type TrainingName = String
object TrainingName {
  def apply(name: String): TrainingName = name

  given Encoder[TrainingName] = Encoder.encodeString
  given Decoder[TrainingName] = Decoder.decodeString
  given Schema[TrainingName] = Schema.schemaForString

  given Transformer[detailpage.TrainingName, TrainingName] = (name: detailpage.TrainingName) => TrainingName(name.value)
}
