package nl.dpes.profileservice.tracing

import cats.effect.*
import nl.dpes.profileservice.tapir.CustomDecodeFailureHandler
import org.typelevel.log4cats.SelfAwareStructuredLogger
import sttp.tapir.model.ServerRequest
import sttp.tapir.server.http4s.Http4sServerOptions
import sttp.tapir.server.model.ServerResponse

object HttpServerOptions {

  def serverOptions[F[_]: Async](logger: SelfAwareStructuredLogger[F]): Http4sServerOptions[F] = {

    val serverLog = CustomServerLog(
      doLogWhenReceived = req => logger.info(req),
      doLogWhenHandled = (req, thr) =>
        thr match {
          case Some(_: Throwable) if req.contains("\"statusCode\":\"400\"") => logger.warn(req)
          case Some(_: Throwable)                                           => logger.error(req)
          case None                                                         => logger.info(req)
        },
      doLogAllDecodeFailures = (msg, _) => logger.error(msg),
      doLogExceptions = (msg, thr) => logger.error(s"$msg; Exception: ${thr.getMessage}, Stacktrace: ${thr.getStackTrace.mkString("\n")}"),
      noLog = Async[F].unit,
      showRequest = parseRequest,
      showResponse = parseResponse
    )

    Http4sServerOptions.customiseInterceptors
      .serverLog(serverLog)
      .decodeFailureHandler(CustomDecodeFailureHandler.handler[F])
      .options
  }

  private def parseRequest(req: ServerRequest): String =
    s""""method": "${req.method.method}", "path": "${req.pathSegments
        .mkString(" / ")}", "recruiterId": ${req.headers
        .find(_.name == "X-Recruiter-ID")
        .map(id => s"\"${id.value}\"")
        .getOrElse("\"None\"")}"""

  private def parseResponse(resp: ServerResponse[?]): String =
    s""""statusCode": "${resp.code.code.toString}", "response": "${resp.source.map(_.value).toString}""""
}
