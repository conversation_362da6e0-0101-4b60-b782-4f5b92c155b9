package nl.dpes.profileservice.tapir

import cats.effect.Async
import io.circe.*
import io.circe.parser.*
import nl.dpes.profileservice.tapir.ErrorMessage.BadRequest
import nl.dpes.profileservice.tapir.ErrorMessage.BadRequest.ValidationErrorDetail
import nl.dpes.profileservice.tapir.jsonvalidation.ValidatedJson.ValidationError
import sttp.model.StatusCode
import sttp.tapir.*
import sttp.tapir.DecodeResult.InvalidValue
import sttp.tapir.json.circe.*
import sttp.tapir.server.interceptor.decodefailure.DecodeFailureHandler
import sttp.tapir.server.model.ValuedEndpointOutput
import sttp.tapir.server.interceptor.decodefailure.DefaultDecodeFailureHandler.ValidationMessages.invalidValueMessage

object CustomDecodeFailureHandler {

  case class Input(
    parameterType: String,
    name: String
  ) derives Encoder,
        Decoder,
        Schema

  object Input {
    // when other field types need to be handled, add them here
    def apply(input: EndpointInput[?]): Input = input match {
      case EndpointInput.Query(name, _, _, _)    => Input("queryparameter", name)
      case EndpointInput.PathCapture(name, _, _) => Input("pathsegment", name.getOrElse("unnamed"))
      case EndpointIO.Header(name, _, _)         => Input("header", name)
      case EndpointIO.Body(bodyType, _, _)       => Input("body", "")
      case _                                     => Input("unknown", input.show)
    }
  }

  case class CodedDecodeException(code: String, message: String) extends Exception(message)

  def handler[F[_]: Async]: DecodeFailureHandler[F] = DecodeFailureHandler[F] { ctx =>

    def toDetail(failingInput: EndpointInput[?], failure: DecodeResult.Error | DecodeResult.InvalidValue): List[ValidationErrorDetail] = {
      val input = Input(failingInput)

      failure match {
        case DecodeResult.Error(_, CodedDecodeException(code, msg)) =>
          List(ValidationErrorDetail(input.parameterType, input.name, Some(code), msg))

        case DecodeResult.Error(x, other) =>
          List(ValidationErrorDetail(input.parameterType, input.name, None, other.getMessage))

        case DecodeResult.InvalidValue(errors) =>
          errors.map { tapirValidationError =>
            val validationError = decode[ValidationError](tapirValidationError.customMessage.getOrElse("")) match {
              case Left(_)      => ValidationError(input.name, invalidValueMessage(tapirValidationError, input.name))
              case Right(value) => value
            }

            ValidationErrorDetail(
              parameterType = input.parameterType,
              inputName = validationError.path,
              code = None,
              message = validationError.message
            )
          }
      }
    }

    def constructErrorResponse(
      failingInput: EndpointInput[?],
      failure: DecodeResult.Error | DecodeResult.InvalidValue
    ): Option[ValuedEndpointOutput[BadRequest]] =
      Some(
        ValuedEndpointOutput(
          statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]),
          BadRequest(toDetail(failingInput, failure))
        )
      )

    Async[F].pure {
      ctx.failure match {
        case e: DecodeResult.Error        => constructErrorResponse(ctx.failingInput, e)
        case e: DecodeResult.InvalidValue => constructErrorResponse(ctx.failingInput, e)
        case _                            => None
      }
    }
  }

}
