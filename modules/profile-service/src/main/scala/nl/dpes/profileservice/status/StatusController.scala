package nl.dpes.profileservice.status

import cats.effect.Async
import cats.implicits.toSemigroupKOps
import sttp.tapir.*
import cats.syntax.applicative.*
import cats.syntax.flatMap.*
import cats.syntax.functor.*
import org.http4s.HttpRoutes
import sttp.model.StatusCode
import sttp.tapir.server.http4s.Http4sServerInterpreter
import sttp.tapir.{endpoint, statusCode, PublicEndpoint}
import sttp.tapir.swagger.SwaggerUIOptions
import sttp.tapir.swagger.bundle.SwaggerInterpreter

trait StatusController[F[_]] {
  def showStatus: F[PublicEndpoint[Unit, Unit, Unit, Any]]
  def routes: F[HttpRoutes[F]]
}

object StatusController {

  val version = "v1"

  def impl[F[_]: Async]: StatusController[F] = new StatusController[F] {

    override def showStatus: F[PublicEndpoint[Unit, Unit, Unit, Any]] =
      endpoint
        .in("status")
        .out(statusCode(StatusCode.Ok))
        .pure

    override def routes: F[HttpRoutes[F]] = for {
      status <- showStatus
      logic  <- Async[F].delay(status.serverLogicSuccess(_ => ().pure))
      swaggerRoutes <- Async[F].delay(
        Http4sServerInterpreter[F]()
          .toRoutes(
            SwaggerInterpreter(swaggerUIOptions =
              SwaggerUIOptions(
                List("docs", "status", version),
                "docs.yaml",
                Nil,
                useRelativePaths = false,
                showExtensions = true,
                None,
                None
              )
            )
              .fromServerEndpoints[F](List(logic), "status", version)
          )
      )
      routes <- Http4sServerInterpreter[F]().toRoutes(logic).pure
    } yield swaggerRoutes <+> routes
  }
}
