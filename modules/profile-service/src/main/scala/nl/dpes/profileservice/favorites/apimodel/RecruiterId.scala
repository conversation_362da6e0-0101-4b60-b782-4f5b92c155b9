package nl.dpes.profileservice.favorites.apimodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.recruiterfavorites.service.domainmodel.RecruiterId as DomainRecruiterId
import nl.dpes.profilesearch.service.model.profilesearch.RecruiterId as AppRecruiterId
import sttp.tapir.{Codec, CodecFormat}

case class RecruiterId private (value: String)

object RecruiterId {
  given Encoder[RecruiterId] = Encoder.encodeString.contramap(_.value)
  given Decoder[RecruiterId] = Decoder.decodeString.emap(RecruiterId(_))
  given Transformer[RecruiterId, DomainRecruiterId] = id => DomainRecruiterId(id.value)
  given Transformer[DomainRecruiterId, RecruiterId] = id => new RecruiterId(id.value)
  given Transformer[AppRecruiterId, RecruiterId] = id => new RecruiterId(id.value)
  given Transformer[RecruiterId, AppRecruiterId] = id => AppRecruiterId(id.value)
  given Codec[String, RecruiterId, CodecFormat.TextPlain] = Codec.string.mapEither(RecruiterId(_))(_.value)

  def apply(recruiterId: String): Either[String, RecruiterId] = {
    val trimmerRecruiterId = recruiterId.trim
    if (trimmerRecruiterId.length == 18)
      Right(new RecruiterId(trimmerRecruiterId))
    else
      Left(s"RecruiterId is a 18-character identifier. You have provided '${trimmerRecruiterId.length}' characters.")
  }
}
