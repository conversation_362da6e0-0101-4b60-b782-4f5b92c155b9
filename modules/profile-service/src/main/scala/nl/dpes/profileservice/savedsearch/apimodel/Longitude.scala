package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.Transformer
import sttp.tapir.Schema

opaque type Longitude = String

object Longitude {
  given Encoder[Longitude] = Encoder.encodeString
  given Decoder[Longitude] = Decoder.decodeString
  given Schema[Longitude] = Schema.string
  given Transformer[Double, Longitude] = (longitude: Double) => Longitude(longitude.toString)
  given Transformer[Longitude, Double] = (longitude: Longitude) => longitude.toDouble

  def apply(longitude: String): Longitude = longitude
}
