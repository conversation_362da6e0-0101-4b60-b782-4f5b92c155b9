package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type SiteName = String
object SiteName {
  def apply(name: String): SiteName = name

  given Encoder[SiteName] = Encoder.encodeString
  given Decoder[SiteName] = Decoder.decodeString
  given Schema[SiteName] = Schema.schemaForString

  given Transformer[detailpage.SiteName, SiteName] = (siteName: detailpage.SiteName) => SiteName(siteName.value)
}
