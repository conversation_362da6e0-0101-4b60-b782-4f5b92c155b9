package nl.dpes.profileservice
package search.searchmodel.sort

import cats.ApplicativeThrow
import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.{PartialTransformer, Transformer}
import io.scalaland.chimney.partial
import nl.dpes.profilesearch.service.model.profilesearch.sort.SortField as SearchSortField
import sttp.tapir.{Codec, CodecFormat, Schema}

import scala.util.Try
import scala.util.control.NoStackTrace

opaque type SortField = String

object SortField {
  val relevance: SortField = "relevance"
  val updatedDate: SortField = "updatedDate"

  private val allowedFields: List[SortField] = List(relevance, updatedDate)
  case class InvalidSortField(sortField: String) extends Throwable(s"Invalid sort field: '$sortField''") with NoStackTrace

  def apply[F[_]: ApplicativeThrow](sortField: String): F[SortField] =
    if (allowedFields.contains(sortField)) sortField.pure
    else InvalidSortField(sortField).raiseError

  extension (sortField: SortField) def name: String = sortField

  given Encoder[SortField] = Encoder.encodeString
  given Decoder[SortField] = Decoder.decodeString.emapTry(apply[Try])
  given Codec[String, SortField, CodecFormat.TextPlain] = Codec.string.mapEither(apply[Try](_).toEither.leftMap(_.getMessage))(_.name)
  given Schema[SortField] = Schema.schemaForString

  given Transformer[SortField, SearchSortField] = (sortField: SortField) => SearchSortField.unsafe(sortField)
}
