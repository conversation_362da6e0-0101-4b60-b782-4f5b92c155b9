package nl.dpes.profileservice.search.searchmodel.filter

import nl.dpes.profilesearch.utils.CommaSeparatedStrings
import sttp.tapir.{Codec, CodecFormat}

case class CareerLevels(value: String)

object CareerLevels {
  given Codec[String, CareerLevels, CodecFormat.TextPlain] = Codec.string.map(CareerLevels(_))(_.value)

  def createStringifiedCareerLevels: List[CareerLevel] => Option[CareerLevels] =
    CommaSeparatedStrings.stringify(_)(_.value, CareerLevels.apply)

  def extractCareerLevelsList: Option[CareerLevels] => List[CareerLevel] =
    CommaSeparatedStrings.extractList(_)(_.value, CareerLevel.apply)
}
