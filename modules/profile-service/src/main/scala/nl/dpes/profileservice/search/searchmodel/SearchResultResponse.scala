package nl.dpes.profileservice.search.searchmodel

import io.circe.Encoder

case class SearchResultResponse(
  // todo: Use it's own profile search result type
  profiles: List[ProfileSearchResult],
  aggregations: Map[String, List[AggregationBucket]],
  totalNumberOfProfiles: Long = 0
)

object SearchResultResponse {
  given Encoder[SearchResultResponse] = io.circe.generic.semiauto.deriveEncoder[SearchResultResponse]

  def fromSearchResult(
    searchResult: SearchResult
  ): SearchResultResponse =
    SearchResultResponse(
      profiles = searchResult.profiles,
      aggregations = searchResult.aggregations.map { case (k, v) => k.value -> v.map(b => AggregationBucket(b.`key`, b.`doc_count`)) },
      totalNumberOfProfiles = searchResult.totalNumberOfProfiles
    )
}
