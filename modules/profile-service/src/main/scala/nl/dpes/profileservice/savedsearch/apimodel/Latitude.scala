package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.Transformer
import sttp.tapir.Schema

opaque type Latitude = String

object Latitude {
  given Encoder[Latitude] = Encoder.encodeString
  given Decoder[Latitude] = Decoder.decodeString
  given Schema[Latitude] = Schema.string
  given Transformer[Double, Latitude] = (latitude: Double) => Latitude(latitude.toString)
  given Transformer[Latitude, Double] = (latitude: Latitude) => latitude.toDouble

  def apply(latitude: String): Latitude = latitude
}
