package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.*

opaque type City = String
object City {
  def apply(value: String): City = value

  given Encoder[City] = Encoder.encodeString
  given Decoder[City] = Decoder.decodeString
  given Schema[City] = Schema.schemaForString

  given Transformer[detailpage.City, City] = (city: detailpage.City) => City(city.value)
}
