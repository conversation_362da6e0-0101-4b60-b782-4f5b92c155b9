package nl.dpes.profileservice
package savedsearch.apimodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.{partial, PartialTransformer, Transformer}
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.domain.filter.*
import nl.dpes.profilesearch.domain.{City as DomainCity, SearchTerm as DomainSearchTerm, UpdateDate as DomainUpdateDate}
import nl.dpes.profileservice.savedsearch.apimodel
import sttp.tapir.Schema

opaque type SingleFilter[A] = A
object SingleFilter {
  def apply[A](a: A): SingleFilter[A] = a
  extension [A](singleFilter: SingleFilter[A]) def value: A = singleFilter

  given [A: Encoder]: Encoder[SingleFilter[A]] = Encoder[A]
  given [A: Decoder]: Decoder[SingleFilter[A]] = Decoder[A]
  given [A: Schema]: Schema[SingleFilter[A]] = summon[Schema[A]]

  given PartialTransformer[SingleFilter[SearchTerm], SearchTermFilter] =
    PartialTransformer[SingleFilter[SearchTerm], SearchTermFilter] { (singleFilter: SingleFilter[SearchTerm]) =>
      singleFilter
        .intoPartial[DomainSearchTerm]
        .transform
        .map(SearchTermFilter(_))
    }

  given Transformer[SearchTermFilter, SingleFilter[SearchTerm]] =
    (searchTermFilter: SearchTermFilter) => SingleFilter(searchTermFilter.query.into[SearchTerm].transform)

  given Transformer[SingleFilter[City], CityFilter] = (singleFilter: SingleFilter[City]) =>
    CityFilter(singleFilter.value.into[DomainCity].transform)

  given Transformer[CityFilter, SingleFilter[City]] = (cityFilter: CityFilter) => cityFilter.city.into[City].transform

  given Transformer[GeoDistanceFilter, SingleFilter[GeoDistance]] = GeoDistance.toApiTransformer

  given Transformer[SingleFilter[GeoDistance], GeoDistanceFilter] = GeoDistance.todomainTransformer

  given PartialTransformer[SingleFilter[UpdatedDate], UpdateDateFilter] =
    PartialTransformer[SingleFilter[UpdatedDate], UpdateDateFilter] { (singleFilter: SingleFilter[UpdatedDate]) =>
      singleFilter.value.intoPartial[DomainUpdateDate].transform.map(UpdateDateFilter(_))
    }

  given Transformer[UpdateDateFilter, SingleFilter[UpdatedDate]] = (updateDateFilter: UpdateDateFilter) =>
    SingleFilter(updateDateFilter.updateDate.into[UpdatedDate].transform)

}
