package nl.dpes.profileservice.search.searchmodel.filter

import nl.dpes.profilesearch.utils.OptionalString
import sttp.tapir.{Codec, CodecFormat}

case class Location(value: String)

object Location {
  given Codec[String, Location, CodecFormat.TextPlain] = Codec.string.map(Location(_))(_.value)

  def cleanupLocation: Option[Location] => Option[Location] =
    OptionalString.cleanup(_)(_.value, Location.apply)
}
