package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type FieldOfStudy = String

object FieldOfStudy {
  def apply(field: String): FieldOfStudy = field

  given Encoder[FieldOfStudy] = Encoder.encodeString
  given Decoder[FieldOfStudy] = Decoder.decodeString
  given Schema[FieldOfStudy] = Schema.schemaForString

  given Transformer[detailpage.FieldOfStudy, FieldOfStudy] = (field: detailpage.FieldOfStudy) => FieldOfStudy(field.value)
}
