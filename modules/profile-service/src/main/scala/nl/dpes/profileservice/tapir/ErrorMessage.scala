package nl.dpes.profileservice.tapir

import io.circe.{Decoder, Encoder}
import ErrorMessage.BadRequest.ValidationErrorDetail
import sttp.tapir.Schema

sealed abstract class ErrorMessage(message: String) extends Throwable(message)

object ErrorMessage {

  case class BadRequest(errors: List[ValidationErrorDetail]) extends ErrorMessage(s"BadRequest") derives Decoder, Schema
  case class Conflict(message: String) extends ErrorMessage(s"Conflict: $message")
  case class NotFound(message: String) extends ErrorMessage(s"Not found: $message")
  case class Forbidden(message: String) extends ErrorMessage(s"Forbidden: $message")
  case class TooManyRequests(message: String) extends ErrorMessage(s"Too Many Requests: $message")
  case class Timeout(message: String) extends ErrorMessage(s"Timeout: $message")
  case class UnknownError(message: String) extends ErrorMessage(s"Unknown error: $message")

  object BadRequest {
    case class ValidationErrorDetail(parameterType: String, inputName: String, code: Option[String], message: String)
        derives Encoder,
          Decoder,
          Schema

    given Encoder[BadRequest] = Encoder.derived[BadRequest].mapJson(_.deepDropNullValues)
  }
}
