package nl.dpes.profileservice.tracing

import sttp.tapir.{AnyEndpoint, DecodeResult}
import sttp.tapir.model.ServerRequest
import sttp.tapir.server.interceptor.{DecodeFailureContext, DecodeSuccessContext, SecurityFailureContext}
import sttp.tapir.server.interceptor.log.{ExceptionContext, ServerLog}
import sttp.tapir.server.model.ServerResponse

import java.time.Clock

case class CustomServerLog[F[_]](
  doLogWhenReceived: String => F[Unit],
  doLogWhenHandled: (String, Option[Throwable]) => F[Unit],
  doLogAllDecodeFailures: (String, Option[Throwable]) => F[Unit],
  doLogExceptions: (String, Throwable) => F[Unit],
  noLog: F[Unit],
  logWhenReceived: Boolean = false,
  logWhenHandled: Boolean = true,
  logAllDecodeFailures: Boolean = false,
  logLogicExceptions: Boolean = true,
  showEndpoint: AnyEndpoint => String = _.showShort,
  showRequest: ServerRequest => String = _.showShort,
  showResponse: ServerResponse[?] => String = _.showShort,
  includeTiming: Boolean = true,
  clock: Clock = Clock.systemUTC(),
  override val ignoreEndpoints: Set[AnyEndpoint] = Set.empty
) extends ServerLog[F] {

  def doLogWhenReceived(f: String => F[Unit]): CustomServerLog[F] = copy(doLogWhenReceived = f)
  def doLogWhenHandled(f: (String, Option[Throwable]) => F[Unit]): CustomServerLog[F] = copy(doLogWhenHandled = f)
  def doLogAllDecodeFailures(f: (String, Option[Throwable]) => F[Unit]): CustomServerLog[F] = copy(doLogAllDecodeFailures = f)
  def doLogExceptions(f: (String, Throwable) => F[Unit]): CustomServerLog[F] = copy(doLogExceptions = f)
  def noLog(f: F[Unit]): CustomServerLog[F] = copy(noLog = f)
  def logWhenReceived(doLog: Boolean): CustomServerLog[F] = copy(logWhenReceived = doLog)
  def logWhenHandled(doLog: Boolean): CustomServerLog[F] = copy(logWhenHandled = doLog)
  def logAllDecodeFailures(doLog: Boolean): CustomServerLog[F] = copy(logAllDecodeFailures = doLog)
  def logLogicExceptions(doLog: Boolean): CustomServerLog[F] = copy(logLogicExceptions = doLog)
  def showEndpoint(s: AnyEndpoint => String): CustomServerLog[F] = copy(showEndpoint = s)
  def showRequest(s: ServerRequest => String): CustomServerLog[F] = copy(showRequest = s)
  def showResponse(s: ServerResponse[?] => String): CustomServerLog[F] = copy(showResponse = s)
  def includeTiming(doInclude: Boolean): CustomServerLog[F] = copy(includeTiming = doInclude)
  def clock(c: Clock): CustomServerLog[F] = copy(clock = c)
  def ignoreEndpoints(es: Seq[AnyEndpoint]): CustomServerLog[F] = copy(ignoreEndpoints = es.toSet)

  override type TOKEN = Long

  override def requestToken: TOKEN = if (includeTiming) now() else 0

  override def requestReceived(request: ServerRequest, token: TOKEN): F[Unit] =
    if (logWhenReceived) doLogWhenReceived(s"{${showRequest(request)}}") else noLog

  override def decodeFailureNotHandled(ctx: DecodeFailureContext, token: TOKEN): F[Unit] =
    if (logAllDecodeFailures)
      doLogAllDecodeFailures(
        s"{${showRequest(ctx.request)}, \"handledBy\": \"${showEndpoint(ctx.endpoint)}\", ${took(
            token
          )}, \"failure\": \"${ctx.failure}\", \"input\": \"${ctx.failingInput.show.format}\"}",
        exception(ctx)
      )
    else noLog

  override def decodeFailureHandled(ctx: DecodeFailureContext, response: ServerResponse[?], token: TOKEN): F[Unit] =
    if (logWhenHandled)
      doLogWhenHandled(
        s"{${showRequest(ctx.request)}, \"handledBy\": \"${showEndpoint(ctx.endpoint)}\",${took(
            token
          )}, \"input\": \"${ctx.failingInput.show.format}\", ${showResponse(response)}}",
        exception(ctx)
      )
    else noLog

  override def securityFailureHandled(ctx: SecurityFailureContext[F, ?], response: ServerResponse[?], token: TOKEN): F[Unit] =
    if (logWhenHandled)
      doLogWhenHandled(
        s"{${showRequest(ctx.request)}, \"handledBy\": \"${showEndpoint(ctx.endpoint)}\", ${took(
            token
          )}, \"securityErrorResponse\": ${showResponse(response)}}",
        None
      )
    else noLog

  override def requestHandled(ctx: DecodeSuccessContext[F, ?, ?, ?], response: ServerResponse[?], token: TOKEN): F[Unit] =
    if (logWhenHandled)
      doLogWhenHandled(
        s"{${showRequest(ctx.request)}, \"handledBy\": \"${showEndpoint(ctx.endpoint)}\", ${took(
            token
          )}, ${showResponse(response)}}",
        None
      )
    else noLog

  override def exception(ctx: ExceptionContext[?, ?], ex: Throwable, token: TOKEN): F[Unit] =
    if (logLogicExceptions)
      doLogExceptions(
        s"{${showRequest(ctx.request)}, ${took(token)}, \"Exception when handling request, caused by\": \"${showEndpoint(ctx.endpoint)}\"}",
        ex
      )
    else noLog

  private def now() = clock.instant().toEpochMilli

  private def took(token: TOKEN): String = if (includeTiming) s""""took": "${now() - token}ms"""" else "\"\""

  private def exception(ctx: DecodeFailureContext): Option[Throwable] =
    ctx.failure match {
      case DecodeResult.Error(_, error) => Some(error)
      case _                            => None
    }

  extension (str: String) def format: String = str.replaceAll("\\{", "").replaceAll("}", "")
}
