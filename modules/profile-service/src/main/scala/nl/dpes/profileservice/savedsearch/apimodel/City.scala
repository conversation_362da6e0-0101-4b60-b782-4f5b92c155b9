package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import sttp.tapir.Schema
import nl.dpes.profilesearch.domain.City as DomainCity

opaque type City = String

object City {
  given Encoder[City] = Encoder.encodeString
  given Decoder[City] = Decoder.decodeString
  given Schema[City] = Schema.string
  given Transformer[DomainCity, City] = (city: DomainCity) => City(city.value)
  given Transformer[City, DomainCity] = (city: City) => DomainCity(city)

  def apply(city: String): City = city
  extension (city: City) def value: String = city
}
