package nl.dpes.profileservice
package savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.domain.CommuteDistance as DomainCommuteDistance
import sttp.tapir.Schema

enum CommuteDistance {
  case Distance(value: Int)
  case WholeCountry
}

object CommuteDistance {
  sealed abstract class CommuteDistanceError(message: String) extends Throwable(message)
  case class NegativeDistance(distance: Int) extends CommuteDistanceError(s"Distance cannot be negative, got '$distance'")
  case class CannotParseDistance(distance: String) extends CommuteDistanceError(s"Cannot parse distance, got '$distance'")

  def fromString(distance: String): Either[CommuteDistanceError, CommuteDistance] =
    if (distance == "Heel Nederland") CommuteDistance.WholeCountry.asRight
    else
      distance.replace("km", "").toIntOption match {
        case Some(value) if value >= 0 => CommuteDistance.Distance(value).asRight
        case Some(value)               => NegativeDistance(value).asLeft
        case None                      => CannotParseDistance(distance).asLeft
      }

  given Encoder[CommuteDistance] = Encoder.encodeString.contramap {
    case CommuteDistance.Distance(value) => s"${value.toString}km"
    case CommuteDistance.WholeCountry    => "Heel Nederland"
  }

  given Decoder[CommuteDistance] = Decoder.decodeString.emap(fromString(_).leftMap(_.getMessage))

  given Schema[CommuteDistance] = Schema.string

  given fromDomain: Transformer[DomainCommuteDistance, CommuteDistance] = Transformer.derive
  given toDomain: Transformer[CommuteDistance, DomainCommuteDistance] = Transformer.derive
}
