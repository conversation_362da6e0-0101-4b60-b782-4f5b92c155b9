package nl.dpes.profileservice.search

import io.scalaland.chimney.Transformer
import sttp.tapir.Codec
import sttp.tapir.CodecFormat.TextPlain
import nl.dpes.profilesearch.service.model.profilesearch.RecruiterId as SearchRecruiterId
import nl.dpes.recruiterfavorites.service.domainmodel.RecruiterId as FavoritesRecruiterId

case class RecruiterId private (value: String)

object RecruiterId {

  def apply(recruiterId: String): Either[String, RecruiterId] =
    if (recruiterId.trim.length == 18)
      Right(new RecruiterId(recruiterId.trim))
    else
      Left(s"RecruiterId is a 18-character identifier. You have provided '${recruiterId.trim.length}' characters.")

  def unsafeApply(value: String): RecruiterId = new RecruiterId(value)

  given Codec[String, RecruiterId, TextPlain] = Codec.string.mapEither(RecruiterId(_))(_.value)

  given Transformer[RecruiterId, SearchRecruiterId] = (recruiterId: RecruiterId) => SearchRecruiterId(recruiterId.value)

  given Transformer[RecruiterId, FavoritesRecruiterId] = (recruiterId: RecruiterId) => FavoritesRecruiterId.apply(recruiterId.value)
}
