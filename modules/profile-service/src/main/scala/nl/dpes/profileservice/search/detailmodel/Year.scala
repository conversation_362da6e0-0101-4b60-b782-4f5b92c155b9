package nl.dpes.profileservice.search.detailmodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type Year = Int
object Year {
  def apply(month: Int): Year = month

  given Encoder[Year] = Encoder.encodeInt
  given Decoder[Year] = Decoder.decodeInt
  given Schema[Year] = Schema.schemaForInt

  given Transformer[detailpage.Year, Year] = (year: detailpage.Year) => Year(year.value)
}
