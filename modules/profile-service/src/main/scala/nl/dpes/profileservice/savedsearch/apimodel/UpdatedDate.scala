package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.{partial, PartialTransformer, Transformer}
import nl.dpes.profilesearch.domain.UpdateDate as DomainUpdateDate
import sttp.tapir.Schema

opaque type UpdatedDate = String

object UpdatedDate {
  given Encoder[UpdatedDate] = Encoder.encodeString
  given Decoder[UpdatedDate] = Decoder.decodeString
  given Schema[UpdatedDate] = Schema.schemaForString
  given Transformer[DomainUpdateDate, UpdatedDate] = (updateDate: DomainUpdateDate) => UpdatedDate(updateDate.entryName)
  given PartialTransformer[UpdatedDate, DomainUpdateDate] = PartialTransformer[UpdatedDate, DomainUpdateDate] { (updatedDate: UpdatedDate) =>
    partial.Result.fromOption(DomainUpdateDate.fromString(updatedDate))
  }

  def apply(updatedDate: String): UpdatedDate = updatedDate
}
