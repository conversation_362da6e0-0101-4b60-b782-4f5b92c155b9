package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type PreferredJob = String
object PreferredJob {
  def apply(occupation: String): PreferredJob = occupation

  given Encoder[PreferredJob] = Encoder.encodeString
  given Decoder[PreferredJob] = Decoder.decodeString
  given Schema[PreferredJob] = Schema.schemaForString

  given Transformer[detailpage.PreferredJob, PreferredJob] = (preferredJob: detailpage.PreferredJob) => PreferredJob(preferredJob.value)
}
