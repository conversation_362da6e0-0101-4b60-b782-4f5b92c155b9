package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.Transformer
import sttp.tapir.Schema

case class Frequency private (value: String)

object Frequency {

  val supportedValues: List[String] = List("<PERSON><PERSON>i<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Nooit")

  given Encoder[Frequency] = Encoder.encodeString.contramap(_.value)
  given Decoder[Frequency] = Decoder.decodeString.emap(Frequency(_))
  given Schema[Frequency] = Schema.string
  given toDomainTransformer: Transformer[Frequency, nl.dpes.savedsearch.domain.Frequency] = frequency =>
    frequency.value match {
      case "Dagelijks" => nl.dpes.savedsearch.domain.Frequency.Daily
      case "Wekelijks" => nl.dpes.savedsearch.domain.Frequency.Weekly
      case "Nooit"     => nl.dpes.savedsearch.domain.Frequency.Never
    }

  given fromDomainTransformer: Transformer[nl.dpes.savedsearch.domain.Frequency, Frequency] = {
    case nl.dpes.savedsearch.domain.Frequency.Daily  => new Frequency("Dagelijks")
    case nl.dpes.savedsearch.domain.Frequency.Weekly => new Frequency("Wekelijks")
    case nl.dpes.savedsearch.domain.Frequency.Never  => new Frequency("Nooit")
  }

  def formatSupportedValues(values: List[String]): String = values.splitAt(values.length - 1) match {
    case (Nil, List(last))  => s"The possible value is '$last'"
    case (init, List(last)) => s"The possible values are ${init.mkString("'", "', '", "'")} and '$last'"
    case _                  => values.mkString("")
  }

  def apply(frequency: String): Either[String, Frequency] =
    if (supportedValues.contains(frequency)) Right(new Frequency(frequency))
    else Left(s"The provided frequency '$frequency' is not supported. ${formatSupportedValues(supportedValues)}")

  def unsafeApply(frequency: String) = new Frequency(frequency)
}
