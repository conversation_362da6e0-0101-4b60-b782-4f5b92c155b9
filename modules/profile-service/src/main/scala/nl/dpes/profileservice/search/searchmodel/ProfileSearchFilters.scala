package nl.dpes.profileservice.search.searchmodel

import nl.dpes.profileservice.search.searchmodel.filter.*

case class ProfileSearchFilters(
  searchTerm: Option[SearchTerm] = None,
  location: Option[Location] = None,
  geoDistance: Option[GeoDistance] = None,
  commuteDistance: Option[CommuteDistance] = None,
  provinces: List[Province] = List.empty,
  updatedDate: Option[UpdatedDate] = None,
  functionGroups: List[FunctionGroup] = List.empty,
  workLevels: List[WorkLevel] = List.empty,
  workingHours: List[WorkingHour] = List.empty,
  careerLevels: List[CareerLevel] = List.empty,
  requestedSalaries: List[RequestedSalary] = List.empty,
  availabilities: List[Availability] = List.empty,
  driverLicenses: List[DriversLicense] = List.empty,
  languages: List[Language] = List.empty
)
