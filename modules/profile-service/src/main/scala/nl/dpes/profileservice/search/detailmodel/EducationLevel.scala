package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type EducationLevel = String
object EducationLevel {
  def apply(level: String): EducationLevel = level

  given Encoder[EducationLevel] = Encoder.encodeString
  given Decoder[EducationLevel] = Decoder.decodeString
  given Schema[EducationLevel] = Schema.schemaForString

  given Transformer[detailpage.EducationLevel, EducationLevel] = (level: detailpage.EducationLevel) => EducationLevel(level.value)
}
