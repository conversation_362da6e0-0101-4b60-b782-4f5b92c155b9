package nl.dpes.profileservice.search.searchmodel.filter

import nl.dpes.profilesearch.utils.CommaSeparatedStrings
import sttp.tapir.{Codec, CodecFormat}

case class WorkLevels(value: String)

object WorkLevels {
  given Codec[String, WorkLevels, CodecFormat.TextPlain] = Codec.string.map(WorkLevels(_))(_.value)

  def createStringifiedWorkLevels: List[WorkLevel] => Option[WorkLevels] =
    CommaSeparatedStrings.stringify(_)(_.value, WorkLevels.apply)

  def extractWorkLevelsList: Option[WorkLevels] => List[WorkLevel] =
    CommaSeparatedStrings.extractList(_)(_.value, WorkLevel.apply)
}
