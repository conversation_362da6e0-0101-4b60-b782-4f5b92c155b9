package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.generic.semiauto.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.domain.filter.GeoDistanceFilter
import sttp.tapir.Schema

case class GeoDistance(latitude: Latitude, longitude: Longitude, maxDistance: Option[CommuteDistance])

object GeoDistance {
  given Encoder[GeoDistance] = deriveEncoder
  given Decoder[GeoDistance] = deriveDecoder
  given Schema[GeoDistance] = Schema.derived[GeoDistance]

  given toApiTransformer: Transformer[GeoDistanceFilter, GeoDistance] = Transformer.derive

  given todomainTransformer: Transformer[GeoDistance, GeoDistanceFilter] = Transformer.derive
}
