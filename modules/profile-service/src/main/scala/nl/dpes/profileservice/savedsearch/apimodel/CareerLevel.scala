package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.domain.CareerLevel as DomainCareerLevel
import sttp.tapir.Schema

opaque type CareerLevel = String

object CareerLevel {
  given Encoder[CareerLevel] = Encoder.encodeString
  given Decoder[CareerLevel] = Decoder.decodeString
  given Schema[CareerLevel] = Schema.string
  given Transformer[DomainCareerLevel, CareerLevel] = (careerLevel: DomainCareerLevel) => CareerLevel(careerLevel.value)
  given Transformer[CareerLevel, DomainCareerLevel] = (careerLevel: CareerLevel) => DomainCareerLevel(careerLevel)

  def apply(careerLevel: String): CareerLevel = careerLevel
}
