package nl.dpes.profileservice.search.detailmodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type FileName = String
object FileName {
  def apply(field: String): FileName = field

  given Encoder[FileName] = Encoder.encodeString
  given Decoder[FileName] = Decoder.decodeString
  given Schema[FileName] = Schema.schemaForString

  given Transformer[detailpage.FileName, FileName] = (fileName: detailpage.FileName) => FileName(fileName.value)
}
