package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.*

opaque type FirstName = String
object FirstName {
  def apply(name: String): FirstName = name

  given Encoder[FirstName] = Encoder.encodeString
  given Decoder[FirstName] = Decoder.decodeString
  given Schema[FirstName] = Schema.schemaForString

  given Transformer[detailpage.FirstName, FirstName] = (firstName: detailpage.FirstName) => FirstName(firstName.value)
}
