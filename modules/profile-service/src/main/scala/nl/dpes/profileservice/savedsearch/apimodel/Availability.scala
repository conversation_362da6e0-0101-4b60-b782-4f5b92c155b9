package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.{partial, PartialTransformer, Transformer}
import nl.dpes.profilesearch.domain.Availability as DomainAvailability
import sttp.tapir.Schema

opaque type Availability = String

object Availability {
  given Encoder[Availability] = Encoder.encodeString
  given Decoder[Availability] = Decoder.decodeString
  given Schema[Availability] = Schema.string
  given Transformer[DomainAvailability, Availability] = (availability: DomainAvailability) => Availability(availability.entryName)
  given PartialTransformer[Availability, DomainAvailability] =
    PartialTransformer[Availability, DomainAvailability] { (availability: Availability) =>
      partial.Result.fromOption(DomainAvailability.fromString(availability))
    }

  def apply(availability: String): Availability = availability
}
