package nl.dpes.profileservice.search.searchmodel.filter

import nl.dpes.profilesearch.utils.CommaSeparatedStrings
import sttp.tapir.{Codec, CodecFormat}

case class FunctionGroups(value: String)

object FunctionGroups {
  given Codec[String, FunctionGroups, CodecFormat.TextPlain] = Codec.string.map(FunctionGroups(_))(_.value)

  def createStringifiedFunctionGroups: List[FunctionGroup] => Option[FunctionGroups] =
    CommaSeparatedStrings.stringify(_)(_.value, FunctionGroups.apply)

  def extractFunctionGroupsList: Option[FunctionGroups] => List[FunctionGroup] =
    CommaSeparatedStrings.extractList(_)(_.value, FunctionGroup.apply)
}
