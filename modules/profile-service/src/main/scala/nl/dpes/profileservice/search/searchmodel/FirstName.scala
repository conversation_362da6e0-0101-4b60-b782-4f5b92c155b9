package nl.dpes.profileservice.search.searchmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch
import sttp.tapir.*

opaque type FirstName = String
object FirstName {
  def apply(name: String): FirstName = name
  given Encoder[FirstName] = Encoder.encodeString
  given Decoder[FirstName] = Decoder.decodeString
  given Schema[FirstName] = Schema.schemaForString

  given Transformer[profilesearch.FirstName, FirstName] = (firstName: profilesearch.FirstName) => FirstName(firstName.value)
}
