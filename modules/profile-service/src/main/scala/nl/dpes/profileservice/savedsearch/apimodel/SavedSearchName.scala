package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.Transformer
import sttp.tapir.Schema

opaque type SavedSearchName = String

object SavedSearchName {
  given Encoder[SavedSearchName] = Encoder.encodeString
  given Decoder[SavedSearchName] = Decoder.decodeString
  given Schema[SavedSearchName] = Schema.string
  given toDomainTransformer: Transformer[SavedSearchName, nl.dpes.savedsearch.domain.Name] =
    name => nl.dpes.savedsearch.domain.Name(name)
  given fromDomainTransformer: Transformer[nl.dpes.savedsearch.domain.Name, SavedSearchName] =
    name => name.value

  def apply(savedSearchName: String): SavedSearchName = savedSearchName
}
