package nl.dpes.profileservice.search.searchmodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch
import sttp.tapir.Schema

opaque type LastViewedDate = Long

object LastViewedDate {

  def apply(timestamp: Long): LastViewedDate = timestamp

  extension (lastViewedDate: LastViewedDate) {
    def value: Long = lastViewedDate
  }

  given Encoder[LastViewedDate] = Encoder.encodeLong
  given Decoder[LastViewedDate] = Decoder.decodeLong
  given Schema[LastViewedDate] = Schema.schemaForLong

  given Transformer[profilesearch.LastViewedDate, LastViewedDate] = (city: profilesearch.LastViewedDate) => LastViewedDate(city.timestamp)
}
