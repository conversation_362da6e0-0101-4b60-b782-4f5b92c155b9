package nl.dpes.profileservice.favorites

import cats.effect.kernel.{MonadCancelThrow, Resource}
import cats.implicits.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profileservice.tapir.ErrorMessage.BadRequest
import nl.dpes.profilesearch.service.model.profilesearch.{ProfileId as SearchProfileId, RecruiterId as SearchRecruiterId}
import nl.dpes.profilesearch.service.ProfileSearchService
import nl.dpes.profileservice.favorites.apimodel.{FavoriteResponse, ProfileId as ApiProfileId, RecruiterId as ApiRecruiterId}
import nl.dpes.profileservice.favorites.apimodel.ProfileId.given
import nl.dpes.profileservice.favorites.apimodel.RecruiterId.given
import nl.dpes.profileservice.search.searchmodel.SearchResult
import nl.dpes.recruiterfavorites.service.FavoritesService as DomainFavoritesService
import nl.dpes.recruiterfavorites.service.domainmodel.*
import org.typelevel.log4cats.LoggerFactory

trait FavoritesControllerService[F[_]] {
  def saveFavorite(recruiterId: ApiRecruiterId, profileId: ApiProfileId): F[Either[BadRequest, Unit]]

  def deleteFavorite(recruiterId: ApiRecruiterId, profileId: ApiProfileId): F[Either[BadRequest, Unit]]

  def deletePotentialFavorite(profileId: ApiProfileId): F[Either[BadRequest, Unit]]

  def getFavorites(recruiterId: ApiRecruiterId, page: Long = 1, limit: Long = 10): F[Either[BadRequest, FavoriteResponse]]
}

object FavoritesControllerService {

  def impl[F[_]: {MonadCancelThrow, LoggerFactory}](
    favoritesService: DomainFavoritesService[F],
    profileSearchService: ProfileSearchService[F]
  ): FavoritesControllerService[F] =
    new FavoritesControllerService[F] {

      override def saveFavorite(recruiterId: ApiRecruiterId, profileId: ApiProfileId): F[Either[BadRequest, Unit]] =
        favoritesService
          .saveFavorite(recruiterId.transformInto[RecruiterId], profileId.transformInto[ProfileId])
          .map(_.asRight[BadRequest])

      override def deleteFavorite(recruiterId: ApiRecruiterId, profileId: ApiProfileId): F[Either[BadRequest, Unit]] =
        favoritesService
          .deleteFavorite(recruiterId.transformInto[RecruiterId], profileId.transformInto[ProfileId])
          .map(_.asRight[BadRequest])

      override def deletePotentialFavorite(profileId: ApiProfileId): F[Either[BadRequest, Unit]] =
        favoritesService
          .deleteFavorite(profileId.transformInto[ProfileId])
          .map(_.asRight[BadRequest])

      override def getFavorites(recruiterId: ApiRecruiterId, page: Long = 1, limit: Long = 10): F[Either[BadRequest, FavoriteResponse]] =
        for {
          favoriteResult <- favoritesService.getFavoritesForRecruiter(recruiterId.transformInto[RecruiterId], page, limit)
          searchResult <- profileSearchService
            .getProfilesById(
              favoriteResult._1.map(_.transformInto[SearchProfileId]),
              Some(recruiterId.transformInto[SearchRecruiterId])
            )
            .map(_.into[nl.dpes.profileservice.search.searchmodel.SearchResult].transform)
          totalNumberOfProfiles = favoriteResult._2
        } yield FavoriteResponse(
          profiles = searchResult.profiles.map(_.setAsFavorite),
          totalNumberOfProfiles = totalNumberOfProfiles
        ).asRight[BadRequest]
    }

  def resource[F[_]: {MonadCancelThrow, LoggerFactory}](
    favoritesService: DomainFavoritesService[F],
    profileSearchService: ProfileSearchService[F]
  ): Resource[F, FavoritesControllerService[F]] =
    Resource.pure(FavoritesControllerService.impl(favoritesService, profileSearchService))
}
