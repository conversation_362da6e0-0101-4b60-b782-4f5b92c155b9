package nl.dpes.profileservice.search.searchmodel.filter

import nl.dpes.profilesearch.utils.CommaSeparatedStrings
import sttp.tapir.{Codec, CodecFormat}

case class Availabilities(value: String)

object Availabilities {
  given Codec[String, Availabilities, CodecFormat.TextPlain] = Codec.string.map(Availabilities(_))(_.value)

  def createStringifiedAvailabilities: List[Availability] => Option[Availabilities] =
    CommaSeparatedStrings.stringify(_)(_.value, Availabilities.apply)

  def extractAvailabilitiesList: Option[Availabilities] => List[Availability] =
    CommaSeparatedStrings.extractList(_)(_.value, Availability.apply)
}
