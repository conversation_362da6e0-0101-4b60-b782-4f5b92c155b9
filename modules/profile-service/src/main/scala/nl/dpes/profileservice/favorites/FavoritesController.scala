package nl.dpes.profileservice.favorites

import cats.effect.Async
import cats.implicits.*
import io.circe.generic.auto.{deriveDecoder, deriveEncoder}
import nl.dpes.profileservice.tapir.ErrorMessage.BadRequest
import nl.dpes.profileservice.favorites.apimodel.{FavoriteResponse, ProfileId, RecruiterId}
import sttp.model.StatusCode
import sttp.tapir.*
import sttp.tapir.generic.auto.*
import sttp.tapir.json.circe.jsonBody
import sttp.tapir.server.ServerEndpoint

trait FavoritesController[F[_]] {
  def saveFavorite: F[ServerEndpoint[Any, F]]

  def deleteFavorite: F[ServerEndpoint[Any, F]]

  def deletePotentialFavorite: F[ServerEndpoint[Any, F]]

  def getFavorites: F[ServerEndpoint[Any, F]]

  def endpoints: F[List[ServerEndpoint[Any, F]]]
}

object FavoritesController {

  val version = "v1"

  def impl[F[_]: Async](favoritesService: FavoritesControllerService[F]): FavoritesController[F] = new FavoritesController[F] {

    override def saveFavorite: F[ServerEndpoint[Any, F]] = Async[F].delay {
      endpoint.post
        .description("Saves a favorite profile")
        .in(header[RecruiterId]("X-Recruiter-ID"))
        .in("favorites" / path[ProfileId]("id").description("The id of the favorite profile"))
        .out(statusCode(StatusCode.Ok))
        .errorOut(statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]))
        .serverLogic(favoritesService.saveFavorite)
    }

    override def deleteFavorite: F[ServerEndpoint[Any, F]] = Async[F].delay {
      endpoint.delete
        .description("Deletes a favorite profile")
        .in(header[RecruiterId]("X-Recruiter-ID"))
        .in("favorites" / path[ProfileId]("id").description("The id of the favorite profile"))
        .out(statusCode(StatusCode.Ok))
        .errorOut(statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]))
        .serverLogic(favoritesService.deleteFavorite)
    }

    override def deletePotentialFavorite: F[ServerEndpoint[Any, F]] = Async[F].delay {
      endpoint.delete
        .description(
          "This endpoint is an internal one and should not be exposed to the UI. It is invoked by privacy-service to delete potential favorite profiles in profile-service, whenever a real profile gets deleted by privacy-service."
        )
        .in("internal" / "favorites" / path[ProfileId]("id").description("The id of the favorite profile"))
        .out(statusCode(StatusCode.Ok))
        .errorOut(statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]))
        .serverLogic(favoritesService.deletePotentialFavorite)
    }

    override def getFavorites: F[ServerEndpoint[Any, F]] = Async[F].delay {
      endpoint.get
        .description("Retrieves all favorite profiles for a recruiter")
        .in(header[RecruiterId]("X-Recruiter-ID"))
        .in(query[Long]("page").description("Page number").default(1L))
        .in(query[Long]("limit").description("Number of profiles per page").default(10L))
        .in("favorites")
        .out(jsonBody[FavoriteResponse])
        .errorOut(statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]))
        .serverLogic(favoritesService.getFavorites)
    }

    override def endpoints: F[List[ServerEndpoint[Any, F]]] =
      List(saveFavorite, deleteFavorite, getFavorites, deletePotentialFavorite).sequence
  }
}
