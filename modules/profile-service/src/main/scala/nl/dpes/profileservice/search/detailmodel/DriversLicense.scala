package nl.dpes.profileservice.search.detailmodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type DriversLicense = String
object DriversLicense {
  def apply(`type`: String): DriversLicense = `type`

  given Encoder[DriversLicense] = Encoder.encodeString
  given Decoder[DriversLicense] = Decoder.decodeString
  given Schema[DriversLicense] = Schema.schemaForString

  given Transformer[detailpage.DriversLicense, DriversLicense] = (license: detailpage.DriversLicense) => DriversLicense(license.value)
}
