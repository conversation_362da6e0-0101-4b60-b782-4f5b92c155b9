package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.*

opaque type EmailAddress = String
object EmailAddress {
  def apply(value: String): EmailAddress = value

  given Encoder[EmailAddress] = Encoder.encodeString
  given Decoder[EmailAddress] = Decoder.decodeString
  given Schema[EmailAddress] = Schema.schemaForString

  given Transformer[detailpage.EmailAddress, EmailAddress] = (email: detailpage.EmailAddress) => EmailAddress(email.value)
}
