package nl.dpes.profileservice.search.searchmodel

import io.circe.generic.semiauto
import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.*
import nl.dpes.profilesearch.service.model.profilesearch.ProfileSearchResult as SearchResult
import nl.dpes.profileservice.search.ProfileId
import sttp.tapir.Schema

case class ProfileSearchResult(
  id: ProfileId,
  name: Option[Name],
  updatedDate: UpdatedDate,
  workingHours: Option[WorkingHours],
  workLevels: List[WorkLevel],
  experiences: List[Experience],
  preferredJobs: List[PreferredJob],
  photo: Option[Photo],
  city: Option[City],
  isFavorite: Boolean,
  lastViewedDate: Option[LastViewedDate]
) derives Decoder {
  def setAsFavorite: ProfileSearchResult = this.copy(isFavorite = true)
}

object ProfileSearchResult {
  given Encoder[ProfileSearchResult] = semiauto.deriveEncoder[ProfileSearchResult].mapJson(_.deepDropNullValues)
  given Schema[ProfileSearchResult] = Schema
    .derived[ProfileSearchResult]
    .modify(_.workLevels)(_.copy(isOptional = false))
    .modify(_.experiences)(_.copy(isOptional = false))
    .modify(_.preferredJobs)(_.copy(isOptional = false))

  extension (results: List[ProfileSearchResult]) {
    def sortByIds(ids: List[ProfileId]): List[ProfileSearchResult] =
      ProfileSearchResult.sortResultsByIds(results, ids)
  }

  private def sortResultsByIds(results: List[ProfileSearchResult], ids: List[ProfileId]): List[ProfileSearchResult] =
    ids.flatMap(id => results.find(_.id == id))

  given Transformer[SearchResult, ProfileSearchResult] =
    Transformer.define[SearchResult, ProfileSearchResult].withFieldConst(_.isFavorite, false).buildTransformer
}
