package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type PhoneNumber = String
object PhoneNumber {
  def apply(value: String): PhoneNumber = value

  given Encoder[PhoneNumber] = Encoder.encodeString
  given Decoder[PhoneNumber] = Decoder.decodeString
  given Schema[PhoneNumber] = Schema.schemaForString

  given Transformer[detailpage.PhoneNumber, PhoneNumber] = (phoneNumber: detailpage.PhoneNumber) => PhoneNumber(phoneNumber.value)
}
