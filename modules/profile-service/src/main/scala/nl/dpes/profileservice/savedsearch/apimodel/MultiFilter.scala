package nl.dpes.profileservice
package savedsearch.apimodel

import cats.data.*
import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.dsl.*
import io.scalaland.chimney.{partial, PartialTransformer, Transformer}
import nl.dpes.profilesearch.domain.filter.*
import nl.dpes.profilesearch.domain.{
  Availability as DomainAvailability,
  CareerLevel as DomainCareerLevel,
  DriversLicense as DomainDriversLicense,
  FunctionGroup as DomainFunctionGroup,
  Language as DomainLanguage,
  Province as DomainProvince,
  RequestedSalary as DomainRequestedSalary,
  WorkLevel as DomainWorkLevel,
  WorkingHours as DomainWorkingHours
}
import sttp.tapir.Schema

opaque type MultiFilter[A] = Seq[A]
object MultiFilter {
  def apply[A](a: Seq[A]): MultiFilter[A] = a
  extension [A](mf: MultiFilter[A]) private def asList: List[A] = mf.toList

  given [A: Encoder]: Encoder[MultiFilter[A]] = Encoder[Seq[A]]
  given [A: Decoder]: Decoder[MultiFilter[A]] = Decoder[Seq[A]]
  given [A: Schema]: Schema[MultiFilter[A]] = summon[Schema[Seq[A]]]

  given PartialTransformer[MultiFilter[WorkLevel], WorkLevelFilter] =
    PartialTransformer[MultiFilter[WorkLevel], WorkLevelFilter] { (mf: MultiFilter[WorkLevel]) =>
      partial.Result.fromOption(
        NonEmptyList
          .fromList(mf.asList.map(_.into[DomainWorkLevel].transform))
          .map(WorkLevelFilter(_))
      )
    }

  given Transformer[WorkLevelFilter, MultiFilter[WorkLevel]] =
    (filter: WorkLevelFilter) => filter.workLevels.toList.map(_.into[WorkLevel].transform)

  given PartialTransformer[MultiFilter[Province], ProvinceFilter] =
    PartialTransformer[MultiFilter[Province], ProvinceFilter] { (mf: MultiFilter[Province]) =>
      partial.Result.fromOption(
        NonEmptyList
          .fromList(mf.asList.map(_.into[DomainProvince].transform))
          .map(ProvinceFilter(_))
      )
    }

  given Transformer[ProvinceFilter, MultiFilter[Province]] =
    (filter: ProvinceFilter) => filter.provinces.toList.map(_.into[Province].transform)

  given PartialTransformer[MultiFilter[FunctionGroup], FunctionGroupFilter] =
    PartialTransformer[MultiFilter[FunctionGroup], FunctionGroupFilter] { (mf: MultiFilter[FunctionGroup]) =>
      partial.Result.fromOption(
        NonEmptyList
          .fromList(mf.asList.map(_.into[DomainFunctionGroup].transform))
          .map(FunctionGroupFilter(_))
      )
    }

  given Transformer[FunctionGroupFilter, MultiFilter[FunctionGroup]] =
    (filter: FunctionGroupFilter) => filter.functionGroups.toList.map(_.into[FunctionGroup].transform)

  given PartialTransformer[MultiFilter[CareerLevel], CareerLevelFilter] =
    PartialTransformer[MultiFilter[CareerLevel], CareerLevelFilter] { (mf: MultiFilter[CareerLevel]) =>
      partial.Result.fromOption(
        NonEmptyList
          .fromList(mf.asList.map(_.into[DomainCareerLevel].transform))
          .map(CareerLevelFilter(_))
      )
    }

  given Transformer[CareerLevelFilter, MultiFilter[CareerLevel]] =
    (filter: CareerLevelFilter) => filter.values.toList.map(_.into[CareerLevel].transform)

  given PartialTransformer[MultiFilter[RequestedSalary], RequestedSalaryFilter] =
    PartialTransformer[MultiFilter[RequestedSalary], RequestedSalaryFilter] { (mf: MultiFilter[RequestedSalary]) =>
      partial.Result.fromOption(
        NonEmptyList
          .fromList(
            mf.asList
              .map(_.intoPartial[DomainRequestedSalary].transform)
              .collect { case partial.Result.Value(requestedSalary) => requestedSalary }
          )
          .map(RequestedSalaryFilter(_))
      )
    }

  given Transformer[RequestedSalaryFilter, MultiFilter[RequestedSalary]] =
    (filter: RequestedSalaryFilter) => filter.salaries.toList.map(_.into[RequestedSalary].transform)

  given PartialTransformer[MultiFilter[DriverLicense], DriversLicenseFilter] =
    PartialTransformer[MultiFilter[DriverLicense], DriversLicenseFilter] { (mf: MultiFilter[DriverLicense]) =>
      partial.Result.fromOption(
        NonEmptyList
          .fromList(mf.asList.map(_.into[DomainDriversLicense].transform))
          .map(DriversLicenseFilter(_))
      )
    }

  given Transformer[DriversLicenseFilter, MultiFilter[DriverLicense]] =
    (filter: DriversLicenseFilter) => filter.driversLicenses.toList.map(_.into[DriverLicense].transform)

  given PartialTransformer[MultiFilter[WorkingHour], WorkingHourFilter] =
    PartialTransformer[MultiFilter[WorkingHour], WorkingHourFilter] { (mf: MultiFilter[WorkingHour]) =>
      partial.Result.fromOption(
        NonEmptyList
          .fromList(
            mf.asList
              .map(_.intoPartial[DomainWorkingHours].transform)
              .collect { case partial.Result.Value(workingHours) => workingHours }
          )
          .map(WorkingHourFilter(_))
      )
    }

  given Transformer[WorkingHourFilter, MultiFilter[WorkingHour]] =
    (filter: WorkingHourFilter) => filter.workingHours.toList.map(_.into[WorkingHour].transform)

  given PartialTransformer[MultiFilter[Availability], AvailabilityFilter] =
    PartialTransformer[MultiFilter[Availability], AvailabilityFilter] { (mf: MultiFilter[Availability]) =>
      partial.Result.fromOption(
        NonEmptyList
          .fromList(
            mf.asList
              .map(_.intoPartial[DomainAvailability].transform)
              .collect { case partial.Result.Value(availability) => availability }
          )
          .map(AvailabilityFilter(_))
      )
    }

  given Transformer[AvailabilityFilter, MultiFilter[Availability]] =
    (filter: AvailabilityFilter) => filter.values.toList.map(_.into[Availability].transform)

  given PartialTransformer[MultiFilter[Language], LanguageFilter] =
    PartialTransformer[MultiFilter[Language], LanguageFilter] { (mf: MultiFilter[Language]) =>
      partial.Result.fromOption(
        NonEmptyList
          .fromList(mf.asList.map(_.into[DomainLanguage].transform))
          .map(LanguageFilter(_))
      )
    }

  given Transformer[LanguageFilter, MultiFilter[Language]] =
    (filter: LanguageFilter) => filter.languages.toList.map(_.into[Language].transform)
}
