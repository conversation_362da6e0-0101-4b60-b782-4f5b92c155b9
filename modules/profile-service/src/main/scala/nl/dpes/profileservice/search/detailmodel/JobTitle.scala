package nl.dpes.profileservice.search.detailmodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type JobTitle = String
object JobTitle {
  def apply(jobTitle: String): JobTitle = jobTitle

  given Encoder[JobTitle] = Encoder.encodeString
  given Decoder[JobTitle] = Decoder.decodeString
  given Schema[JobTitle] = Schema.schemaForString

  given Transformer[detailpage.JobTitle, JobTitle] = (jobTitle: detailpage.JobTitle) => JobTitle(jobTitle.value)
}
