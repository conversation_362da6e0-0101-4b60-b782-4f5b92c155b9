package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.generic.semiauto.*
import sttp.tapir.Schema.annotations.description

case class SavedSearchCreation(
  name: SavedSearchName,
  @description("Represents the search frequency. When creating a saved search, it supports these values: '<PERSON><PERSON><PERSON><PERSON><PERSON>' and '<PERSON>keli<PERSON><PERSON>'.")
  frequency: Frequency,
  searchFilters: SearchFilters
)

object SavedSearchCreation {
  given Encoder[SavedSearchCreation] = deriveEncoder
  given Decoder[SavedSearchCreation] = deriveDecoder

  object SwaggerDoc {
    val example: SavedSearchCreation = SavedSearchCreation(
      name = SavedSearchName("Scala developer"),
      frequency = Frequency.unsafeApply("Dagelijks"),
      searchFilters = SearchFilters(
        searchTerm = Some(SingleFilter(SearchTerm("Scala developer"))),
        city = Some(SingleFilter(City("Amsterdam"))),
        geoDistance = Some(SingleFilter(GeoDistance(Latitude("52.377956"), Longitude("4.897070"), Some(CommuteDistance.Distance(30))))),
        provinces = Some(MultiFilter(Seq(Province("Noord-Holland")))),
        updatedDate = Some(SingleFilter(UpdatedDate("Alles"))),
        functionGroups = Some(MultiFilter(Seq(FunctionGroup("AdministratiefSecretarieel"), FunctionGroup("FinancieelAccounting")))),
        workLevels = Some(MultiFilter(Seq(WorkLevel("HAVO"), WorkLevel("Postdoctoraal"), WorkLevel("Lagere school")))),
        workingHours = Some(MultiFilter(Seq(WorkingHour("16 tot 24 uur"), WorkingHour("24 tot 32 uur")))),
        careerLevels = Some(MultiFilter(Seq(CareerLevel("Starter"), CareerLevel("Ervaren"), CareerLevel("Leidinggevend")))),
        requestedSalaries = Some(MultiFilter(Seq(RequestedSalary("2.500 - 3.500"), RequestedSalary("3.500 - 5.000")))),
        availabilities = Some(MultiFilter(Seq(Availability("Per direct"), Availability("In overleg")))),
        driversLicenses = Some(MultiFilter(Seq(DriverLicense("B - personenauto")))),
        languages = Some(MultiFilter(Seq(Language("Arabisch"), Language("Engles"), Language("Nederlands"))))
      )
    )
  }
}
