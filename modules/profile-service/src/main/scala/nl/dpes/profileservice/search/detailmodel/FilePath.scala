package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type FilePath = String
object FilePath {
  def apply(name: String): FilePath = name

  given Encoder[FilePath] = Encoder.encodeString
  given Decoder[FilePath] = Decoder.decodeString
  given Schema[FilePath] = Schema.schemaForString

  given Transformer[detailpage.FilePath, FilePath] = (filePath: detailpage.FilePath) => FilePath(filePath.value)
}
