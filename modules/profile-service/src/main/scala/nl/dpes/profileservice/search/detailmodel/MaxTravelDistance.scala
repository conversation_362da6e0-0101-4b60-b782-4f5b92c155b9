package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type MaxTravelDistance = Int
object MaxTravelDistance {
  def apply(value: Int): MaxTravelDistance = value

  given Encoder[MaxTravelDistance] = Encoder.encodeInt
  given Decoder[MaxTravelDistance] = Decoder.decodeInt
  given Schema[MaxTravelDistance] = Schema.schemaForInt

  given Transformer[detailpage.MaxTravelDistance, MaxTravelDistance] = (maxTravelDistance: detailpage.MaxTravelDistance) =>
    MaxTravelDistance(maxTravelDistance.value)
}
