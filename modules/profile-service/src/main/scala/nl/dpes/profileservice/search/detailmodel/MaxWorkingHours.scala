package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type MaxWorkingHours = Int
object MaxWorkingHours {
  def apply(hours: Int): MaxWorkingHours = hours

  given Encoder[MaxWorkingHours] = Encoder.encodeInt
  given Decoder[MaxWorkingHours] = Decoder.decodeInt
  given Schema[MaxWorkingHours] = Schema.schemaForInt

  given Transformer[detailpage.MaxWorkingHours, MaxWorkingHours] = (maxWorkingHours: detailpage.MaxWorkingHours) =>
    MaxWorkingHours(maxWorkingHours.value)
}
