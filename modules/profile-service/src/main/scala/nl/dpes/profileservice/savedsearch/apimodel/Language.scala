package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.domain.Language as DomainLanguage
import sttp.tapir.Schema

opaque type Language = String

object Language {
  given Encoder[Language] = Encoder.encodeString
  given Decoder[Language] = Decoder.decodeString
  given Schema[Language] = Schema.string
  given Transformer[DomainLanguage, Language] = (domainLanguage: DomainLanguage) => Language(domainLanguage.value)
  given Transformer[Language, DomainLanguage] = (language: Language) => DomainLanguage(language)

  def apply(language: String): Language = language
}
