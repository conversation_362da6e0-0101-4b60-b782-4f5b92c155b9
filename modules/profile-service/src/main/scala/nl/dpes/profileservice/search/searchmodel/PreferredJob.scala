package nl.dpes.profileservice.search.searchmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch
import sttp.tapir.Schema

type PreferredJob = String
object PreferredJob {
  def apply(job: String) = job
  given Encoder[PreferredJob] = Encoder.encodeString
  given Decoder[PreferredJob] = Decoder.decodeString
  given Schema[PreferredJob] = Schema.schemaForString

  given Transformer[profilesearch.PreferredJob, PreferredJob] = (job: profilesearch.PreferredJob) => PreferredJob(job)
}
