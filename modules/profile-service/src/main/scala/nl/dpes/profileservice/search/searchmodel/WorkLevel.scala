package nl.dpes.profileservice.search.searchmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch
import sttp.tapir.Schema

opaque type WorkLevel = String
object WorkLevel {
  def apply(level: String): WorkLevel = level

  given Encoder[WorkLevel] = Encoder.encodeString
  given Decoder[WorkLevel] = Decoder.decodeString
  given Schema[WorkLevel] = Schema.schemaForString

  given Transformer[profilesearch.WorkLevel, WorkLevel] = (workLevel: profilesearch.WorkLevel) => WorkLevel(workLevel.value)
}
