package nl.dpes.profileservice.tapir.jsonvalidation

import cats.data.*
import cats.data.Validated.{Invalid, Valid}
import io.circe.*
import io.circe.Decoder.AccumulatingResult
import io.circe.syntax.*
import sttp.tapir
import sttp.tapir.*
import sttp.tapir.json.circe.*

object ValidatedJson {
  case class ValidationError(path: String, message: String) derives Encoder, Decoder

  private def formatParsingErrors(errs: NonEmptyList[DecodingFailure]) =
    errs.toList.map(df => s"|- ${df.pathToRootString.getOrElse("")}: ${df.message}").mkString("\n")

  private def accumulatingResultMapping[T: {Decoder, Encoder}]: Mapping[Json, AccumulatingResult[T]] =
    Mapping.from[Json, AccumulatingResult[T]](json => implicitly[Decoder[T]].decodeAccumulating(json.hcursor)) {
      case Valid(value) => value.asJson
      case Invalid(errs) =>
        throw new Exception(
          s"""Trying to encode an invalid AccumulatingResult
             ||This should never happen because all objects should be encodable.
             ||Errors:
             ||${formatParsingErrors(errs)}
             |""".stripMargin
        )
    }

  private def accumulatingResultValidator[T: Decoder]: Validator[AccumulatingResult[T]] =
    Validator.custom[AccumulatingResult[T]] {
      case Valid(_)      => ValidationResult.Valid
      case Invalid(errs) =>
        // Map Circe errors to Tapir ValidationErrors
        ValidationResult.Invalid(
          errs.toList.map(df => ValidationError(s"${df.pathToRootString.getOrElse("")}", s"${df.message}").asJson.noSpaces)
        )
    }

  private def decodeAccummulatedResult[T: Decoder](result: AccumulatingResult[T]): T =
    result match {
      case Valid(value) => value
      case Invalid(errs) =>
        throw new Exception(
          s"""Trying to decode an invalid AccumulatingResult
           ||This should never happen because the validator should have caught this state already.
           ||Errors:
           ||${formatParsingErrors(errs)}
           |""".stripMargin
        )
    }

  private def encodeAccummulatedResult[T: Encoder](value: T): AccumulatingResult[T] =
    Valid(value)

  /** This method will act like jsonBody[A], but will accumulate all errors and converts them to a list of ValidationError which can be
    * returned to the client in a BadRequest. The validationError will contain the path to the invalid field and the error message for that
    * field.
    *
    * To achieve this the json will be decoded to an AccumulatingResult[A] first, which is then validated to check if it is valid or
    * invalid. If it is valid the A will be extracted and returned, if it is invalid the list of errors will be extracted and returned as a
    * BadRequest.
    *
    * The code to convert the AccumulatingResult[A] to A also needs to handle the Invalid case, but this should never happen because the
    * validator should have caught this already. Tapira also needs to convert all types back to the input type, which means that we also
    * need to provide a method to convert AccumulatingResult[A] to JSON. Here we see the same issue, the Invalid case should never happen
    * because we should only be encoding valid objects.
    */
  def validatedJsonBody[A: {Decoder, Encoder}]: EndpointIO.Body[String, A] =
    jsonBody[Json]
      .map(accumulatingResultMapping)
      .mapValidate(accumulatingResultValidator[A])(decodeAccummulatedResult)(encodeAccummulatedResult)
}
