package nl.dpes.profileservice.search.detailmodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type CompanyName = String
object CompanyName {
  def apply(name: String): CompanyName = name

  given Encoder[CompanyName] = Encoder.encodeString
  given Decoder[CompanyName] = Decoder.decodeString
  given Schema[CompanyName] = Schema.schemaForString

  given Transformer[detailpage.CompanyName, CompanyName] = (companyName: detailpage.CompanyName) => CompanyName(companyName.value)
}
