package nl.dpes.profileservice.search.searchmodel.sort

case class Sorting(field: SortField, order: SortOrder)

object Sorting {
  private val defaultSorting = new Sorting(SortField.relevance, SortOrder.desc)

  private val defaultSortings: Map[SortField, SortOrder] = Map(
    SortField.relevance   -> SortOrder.desc,
    SortField.updatedDate -> SortOrder.desc
  )

  def apply(field: Option[SortField], order: Option[SortOrder]): Sorting = {
    val fieldValue = field.getOrElse(defaultSorting.field)
    val orderValue = order.getOrElse(defaultSortings(fieldValue))
    new Sorting(fieldValue, orderValue)
  }
}
