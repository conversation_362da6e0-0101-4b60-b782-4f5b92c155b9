package nl.dpes.profileservice.search.searchmodel.filter

import nl.dpes.profilesearch.utils.CommaSeparatedStrings
import sttp.tapir.{Codec, CodecFormat}

case class Languages(value: String)

object Languages {
  given Codec[String, Languages, CodecFormat.TextPlain] = Codec.string.map(Languages(_))(_.value)

  def createStringifiedLanguages: List[Language] => Option[Languages] =
    CommaSeparatedStrings.stringify(_)(_.value, Languages.apply)

  def extractLanguagesList: Option[Languages] => List[Language] =
    CommaSeparatedStrings.extractList(_)(_.value, Language.apply)
}
