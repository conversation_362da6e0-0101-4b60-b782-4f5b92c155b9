package nl.dpes.profileservice.search.detailmodel

import io.circe.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage
import sttp.tapir.Schema

opaque type PhotoPath = String
object PhotoPath {
  def apply(value: String): PhotoPath = value

  given Encoder[PhotoPath] = Encoder.encodeString
  given Decoder[PhotoPath] = Decoder.decodeString
  given Schema[PhotoPath] = Schema.schemaForString

  given Transformer[detailpage.PhotoPath, PhotoPath] = (photoPath: detailpage.PhotoPath) => PhotoPath(photoPath.value)
}
