package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.scalaland.chimney.{partial, PartialTransformer, Transformer}
import sttp.tapir.Schema
import nl.dpes.profilesearch.domain.RequestedSalary as DomainRequestedSalary

opaque type RequestedSalary = String

object RequestedSalary {
  given Encoder[RequestedSalary] = Encoder.encodeString
  given Decoder[RequestedSalary] = Decoder.decodeString
  given Schema[RequestedSalary] = Schema.string
  given Transformer[DomainRequestedSalary, RequestedSalary] = (requestedSalary: DomainRequestedSalary) =>
    RequestedSalary(requestedSalary.value)
  given PartialTransformer[RequestedSalary, DomainRequestedSalary] =
    PartialTransformer[RequestedSalary, DomainRequestedSalary] { (requestedSalary: RequestedSalary) =>
      partial.Result.fromOption(DomainRequestedSalary.fromString(requestedSalary))
    }
  def apply(requestedSalary: String): RequestedSalary = requestedSalary
}
