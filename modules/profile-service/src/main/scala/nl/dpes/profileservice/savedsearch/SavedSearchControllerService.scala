package nl.dpes.profileservice
package savedsearch

import cats.Monad
import cats.data.EitherT
import cats.effect.{Async, Resource}
import cats.implicits.*
import io.scalaland.chimney.*
import io.scalaland.chimney.cats.*
import io.scalaland.chimney.dsl.*
import io.scalaland.chimney.partial.Result
import nl.dpes.profileservice.savedsearch.apimodel.*
import nl.dpes.profileservice.tapir.ErrorMessage
import nl.dpes.profileservice.tapir.ErrorMessage.*
import nl.dpes.savedsearch
import nl.dpes.savedsearch.service.SavedSearchService.SavedSearchNotFoundException
import org.typelevel.log4cats.{Logger, LoggerFactory, LoggerName}

trait SavedSearchControllerService[F[_]] {

  def getById(recruiterId: RecruiterId, id: SavedSearchId): F[Either[ErrorMessage, SavedSearch]]

  def create(recruiterId: RecruiterId, searchFilters: SavedSearchCreation): F[Either[ErrorMessage, SavedSearchId]]

  def update(recruiterId: RecruiterId, id: SavedSearchId, update: SavedSearchUpdate): F[Either[ErrorMessage, Unit]]

  def delete(recruiterId: RecruiterId, id: SavedSearchId): F[Either[ErrorMessage, Unit]]

  def getAll(recruiterId: RecruiterId): F[Either[UnknownError, List[SavedSearch]]]
}

object SavedSearchControllerService {
  def impl[F[_]: Async: LoggerFactory](savedSearchService: savedsearch.service.SavedSearchService[F]): SavedSearchControllerService[F] =
    new SavedSearchControllerService[F] {
      private val logger: Logger[F] = LoggerFactory[F].getLogger(using LoggerName("Profile search service"))

      override def getById(recruiterId: RecruiterId, id: SavedSearchId): F[Either[ErrorMessage, SavedSearch]] = savedSearchService
        .getById(
          recruiterId.into[savedsearch.domain.RecruiterId].transform,
          id.into[savedsearch.domain.SavedSearchId].transform
        )
        .map {
          case Some(savedSearch) =>
            savedSearch.intoPartial[SavedSearch].transform.asEither match {
              case Right(savedSearchResponse) => Right(savedSearchResponse)
              case Left(errors)               => Left(UnknownError(s"Invalid saved search: ${errors.toList.mkString(", ")}"))
            }
          case None => Left(NotFound(s"Saved search with id '${id.value}' not found for recruiter '${recruiterId.value}'"))
        }
        .recoverWith { case e =>
          UnknownError(s"Failed to retrieve saved search for recruiter ${recruiterId.value}: ${e.getMessage}").asLeft.pure
        }

      override def create(recruiterId: RecruiterId, searchFilters: SavedSearchCreation): F[Either[ErrorMessage, SavedSearchId]] =
        (for {
          savedSearchFilters <- EitherT(
            Async[F].pure(searchFilters.searchFilters.intoPartial[savedsearch.domain.SavedSearchFilters].transform.asEither.leftMap { e =>
              UnknownError(s"Invalid search filters: ${e.errors.mkString(", ")}")
            })
          )
          frequency <- EitherT(
            Async[F].pure(searchFilters.frequency.intoPartial[savedsearch.domain.Frequency].transform.asEither.leftMap { e =>
              UnknownError(s"Invalid frequency: ${e.errors.mkString(", ")}")
            })
          )
          result <- EitherT[F, ErrorMessage, SavedSearchId](
            savedSearchService
              .create(
                recruiterId.into[savedsearch.domain.RecruiterId].transform,
                searchFilters.name.into[savedsearch.domain.Name].transform,
                frequency,
                savedSearchFilters
              )
              .map(savedSearchId => Right(SavedSearchId(savedSearchId.toString)))
              .handleErrorWith {
                case savedsearch.service.SavedSearchService.SavedSearchAlreadyExistsException(msg) => Monad[F].pure(Left(Conflict(msg)))
                case e =>
                  Monad[F].pure(Left(UnknownError("An error occurred while creating the saved search")))
                  <* logger.error(s"An error occurred while creating the saved search due to: ${e.getMessage}")
              }
          )
        } yield result).value

      override def update(recruiterId: RecruiterId, id: SavedSearchId, update: SavedSearchUpdate): F[Either[ErrorMessage, Unit]] =
        (for {
          frequency <- EitherT(
            Async[F].pure(update.frequency.intoPartial[savedsearch.domain.Frequency].transform.asEither.leftMap { e =>
              UnknownError(s"Invalid frequency: ${e.errors.mkString(", ")}")
            })
          )
          _ <- EitherT[F, ErrorMessage, Unit](
            savedSearchService
              .updateFrequency(
                recruiterId.into[savedsearch.domain.RecruiterId].transform,
                id.into[savedsearch.domain.SavedSearchId].transform,
                frequency
              )
              .map(_ => Right(()))
              .handleErrorWith {
                case e: SavedSearchNotFoundException =>
                  Monad[F].pure(Left(ErrorMessage.NotFound("Failed to update saved search frequency")))
                case e =>
                  Monad[F].pure(Left(ErrorMessage.UnknownError("Failed to update saved search frequency")))
                  <* logger.error(s"Failed to update saved search frequency due to: ${e.getMessage}")
              }
          )
        } yield ()).value

      override def delete(recruiterId: RecruiterId, id: SavedSearchId): F[Either[ErrorMessage, Unit]] =
        EitherT[F, ErrorMessage, Unit](
          savedSearchService
            .delete(
              recruiterId.into[savedsearch.domain.RecruiterId].transform,
              id.into[savedsearch.domain.SavedSearchId].transform
            )
            .map(_.asRight)
            .handleError(_ =>
              Left(ErrorMessage.UnknownError(s"Failed to delete the saved search '${id.value}' for recruiter '${recruiterId.value}'"))
            )
        ).value

      override def getAll(recruiterId: RecruiterId): F[Either[UnknownError, List[SavedSearch]]] =
        savedSearchService
          .getAllForRecruiter(recruiterId.into[savedsearch.domain.RecruiterId].transform)
          .map(searches =>
            searches.intoPartial[List[SavedSearch]].transform.asEither match {
              case Right(savedSearches) => Right(savedSearches)
              case Left(errors)         => Left(UnknownError(s"Invalid saved searches: ${errors.toList.mkString(", ")}"))
            }
          )
          .recoverWith { case e =>
            UnknownError(s"Failed to retrieve saved searches for recruiter ${recruiterId.value}: ${e.getMessage}").asLeft.pure
          }
    }

  def resource[F[_]: Async: LoggerFactory](
    savedSearchService: savedsearch.service.SavedSearchService[F]
  ): Resource[F, SavedSearchControllerService[F]] =
    Resource.pure(SavedSearchControllerService.impl(savedSearchService))
}
