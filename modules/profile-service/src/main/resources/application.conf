blaze-server-config {
  host = "0.0.0.0"
  port = "14200"
}

elasticsearch-config {
  host-scheme = "http"
  host-scheme = ${?ELASTICSEARCH_HOST_SCHEME}
  host = "elasticsearch"
  host = ${?ELASTICSEARCH_CLUSTER_HOST}
  port = "9200"
  port = ${?ELASTICSEARCH_CLUSTER_PORT}
  index = "cv_database"
  index = ${?ELASTICSEARCH_CV_DATABASE_INDEX}
}

database-config {
  url = ${?DATABASE_CONNECTION_STRING}
  thread-count = 10
  username = ${?DATABASE_USER}
  password = ${?DATABASE_PASSWORD}
}

visited-profiles-config {
  table-name = "visited_profiles"
  view-limit = ${?VISITED_PROFILES_VIEW_LIMIT}
}

unlocked-profile-config {
  table-name = "unlocked_profiles"
  access-duration-in-days = ${?UNLOCKED_PROFILES_ACCESS_DURATION_IN_DAYS}
}

credit-service-config {
  host = ${?CREDIT_SERVICE_SERVICE_HOST}
  port = ${?CREDIT_SERVICE_SERVICE_PORT}
}

favorite-profiles-config {
  table-name = "favorite_profiles"
}

viewed-profiles-config {
  table-name = "viewed_profiles"
  retention-threshold-in-days = ${?VIEWED_PROFILES_RETENTION_THRESHOLD_IN_DAYS}
}

lock-config {
  maximum-concurrent-locks = 8  // make sure this is lower than the thread count in database-config
  lock-timeout = 15 seconds     // maximum time for process to run in lock
}

saved-search-config {
  table-name = "saved_search"
}
