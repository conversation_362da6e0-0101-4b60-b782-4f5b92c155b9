<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Set root logger to WARN to reduce noise -->
    <root level="WARN">
        <appender-ref ref="STDOUT" />
    </root>

    <!-- Only show ERROR level for these noisy loggers -->
    <logger name="org.testcontainers" level="ERROR" />
    <logger name="com.zaxxer.hikari" level="ERROR" />
    <logger name="tc." level="ERROR" />
    
    <!-- Keep test-specific loggers at INFO if needed -->
    <logger name="nl.dpes" level="WARN" />
    
    <!-- Completely silence the PROFILE ACCESS LOG during tests -->
    <logger name="PROFILE ACCESS LOG" level="OFF" />
</configuration>
