package nl.dpes.profileservice.tapir.jsonvalidation.datastructure

import io.circe.{Decoder, Encoder}
import sttp.tapir.Schema

opaque type Baz = String

object Baz {
  def apply(baz: String): Either[String, Baz] =
    if (baz == "baz") Right(baz)
    else Left(s"Bar can only be 'baz', got '$baz'")

  given Decoder[Baz] = Decoder.decodeString.emap(Baz.apply)
  given Encoder[Baz] = Encoder.encodeString.contramap(identity)
  given Schema[Baz] = Schema.string
}
