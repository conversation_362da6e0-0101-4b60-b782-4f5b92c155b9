package nl.dpes.profileservice.search.searchmodel.sort

import weaver.SimpleIOSuite

object SortFieldSpec extends SimpleIOSuite {
  test("Invalid fields are not allowed") {
    for {
      sortField <- SortField("invalid").attempt
    } yield expect(sortField == Left(SortField.InvalidSortField("invalid")))
  }

  test("Valid fields are allowed") {
    for {
      relevance   <- SortField("relevance")
      updatedDate <- SortField("updatedDate")
    } yield expect(relevance == SortField.relevance) and expect(updatedDate == SortField.updatedDate)
  }
}
