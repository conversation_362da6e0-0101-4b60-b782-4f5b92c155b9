package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.Language
import weaver.FunSuite

object LanguageSpec extends FunSuite {

  test("Language should be json encoded as a string") {
    val language = Language("A language")
    val json = language.asJson

    expect(json == "A language".asJson)
  }

  test("Language should be encoded and decoded correctly") {
    val language = Language("A language")

    val roundTrippedLanguage = decode[Language](language.asJson.noSpaces)

    expect(roundTrippedLanguage == Right(language))
  }
}
