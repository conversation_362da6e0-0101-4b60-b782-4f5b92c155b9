package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object LastNameSpec extends FunSuite {
  test("LastName should be json encoded as a string") {
    val lastName = LastName("Doe")
    val json = lastName.asJson

    expect(json == "Doe".asJson)
  }

  test("LastName should be encoded and decoded correctly") {
    val lastName = LastName("Doe")

    val roundTrippedLastName = decode[LastName](lastName.asJson.noSpaces)

    expect(roundTrippedLastName == Right(lastName))
  }

  test("LastName should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[LastName]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("LastName should transform from detailpage.LastName") {
    val detailPageLastName = detailpage.LastName("Doe")
    val lastName = LastName(detailPageLastName.value)

    val transformedLastName = detailPageLastName.into[LastName].transform

    expect(transformedLastName == lastName)
  }
}
