package nl.dpes.profileservice.tracing

import cats.effect.IO
import io.circe.*
import io.circe.generic.auto.*
import io.circe.parser.*
import nl.dpes.profileservice.tapir.ErrorMessage.UnknownError
import nl.dpes.profileservice.search.{ProfileId, RecruiterId}
import nl.dpes.profileservice.search.searchmodel.*
import nl.dpes.profileservice.tracing.HttpServerOptions
import org.http4s.*
import org.http4s.Method.*
import org.http4s.client.Client
import org.http4s.implicits.*
import org.typelevel.log4cats.SelfAwareStructuredLogger
import org.typelevel.log4cats.slf4j.Slf4jLogger
import sttp.model.StatusCode
import sttp.tapir.{path, *}
import sttp.tapir.generic.auto.*
import sttp.tapir.json.circe.jsonBody
import sttp.tapir.server.ServerEndpoint.Full
import sttp.tapir.server.http4s.Http4sServerInterpreter
import weaver.{Expectations, SimpleIOSuite}
import org.http4s.headers.Accept
import org.typelevel.ci.*

object HttpServerOptionsSpec extends SimpleIOSuite {
  val validQuery = "with(matching)"
  val invalidQuery = "not(matching"
  val validRecruiterId = "valid-recruiter-id"
  val invalidRecruiterId = "invalid-recruiter-id"
  val validProfileId = "123e4567-e89b-12d3-a456-426614174000"
  val invalidProfileId = "invalid-profile-id"

  // this setup is to test that the interceptors do not change the response JSON
  // while also extracting the correct information
  val logger: SelfAwareStructuredLogger[IO] = Slf4jLogger.getLogger[IO]

  // --- logic ---
  val profilesLogic: ((ProfileId, RecruiterId, Option[SearchTerm])) => IO[Either[UnknownError, SearchResultResponse]] =
    (_, _, _) => IO(Left(UnknownError("This is a dummy error for testing purposes.")))

  // --- define an endpoint ---
  val profilesEndpoint: Full[Unit, Unit, (ProfileId, RecruiterId, Option[SearchTerm]), UnknownError, SearchResultResponse, Any, IO] =
    endpoint.get
      .in("profiles" / path[ProfileId]("id")) // must be the same path as the second endpoint to test that both work
      .in(header[RecruiterId]("X-Recruiter-ID").description("The id of the recruiter"))
      .in(query[Option[SearchTerm]]("query").description("Search term for profiles"))
      .out(jsonBody[SearchResultResponse])
      .errorOut(statusCode(StatusCode.InternalServerError).and(jsonBody[UnknownError]))
      .serverLogic(profilesLogic)

  val secondEndpoint: Full[Unit, Unit, (ProfileId, RecruiterId, Option[SearchTerm]), UnknownError, SearchResultResponse, Any, IO] =
    endpoint.put
      .in("profiles" / path[ProfileId]("id")) // must be the same path as the first endpoint to test that both work
      .in(header[RecruiterId]("X-Recruiter-ID").description("The id of the recruiter"))
      .in(query[Option[SearchTerm]]("query").description("Search term for profiles"))
      .out(jsonBody[SearchResultResponse])
      .errorOut(statusCode(StatusCode.InternalServerError).and(jsonBody[UnknownError]))
      .serverLogic(profilesLogic)

  // --- interpreter with serveroptions that should handle the error ---
  val routes: HttpRoutes[IO] = Http4sServerInterpreter[IO](HttpServerOptions.serverOptions(logger))
    .toRoutes(profilesEndpoint :: secondEndpoint :: Nil)

  val client: Client[IO] = Client.fromHttpApp(routes.orNotFound)

  // --- helper: compare JSON strings structurally ---
  def expectJsonEquals(actual: String, expected: String): Expectations = {
    val parsedActual = parse(actual).fold(throw _, identity)
    val parsedExpected = parse(expected).fold(throw _, identity)
    expect(clue(parsedActual) == parsedExpected)
  }

  // --- the tests ---
  test("Invalid query parameter will give a BadRequest with a specific error message") {
    val expectedJson = s"""{
                         |  "errors" : [
                         |    {
                         |      "parameterType" : "queryparameter",
                         |      "inputName" : "query",
                         |      "code" : "UnmatchedQuoteOrBracket",
                         |      "message" : "Unmatched quotes or brackets in the search string: $invalidQuery"
                         |    }
                         |  ]
                         |}
                         |""".stripMargin

    val request = Request[IO](GET, Uri.unsafeFromString(s"/profiles/$validProfileId?query=$invalidQuery")).putHeaders(
      Accept(MediaType.application.json),
      Header.Raw(ci"X-Recruiter-ID", validRecruiterId)
    )

    client.run(request).use { response =>
      response.as[String].map { body =>
        expectJsonEquals(body, expectedJson) and expect(clue(response.status) == Status.BadRequest)
      }
    }
  }

  test("Invalid header will give a BadRequest with a specific error message") {
    val expectedJson = """{
                         |  "errors" : [
                         |    {
                         |        "parameterType" : "header",
                         |        "inputName" : "X-Recruiter-ID",
                         |        "message" : "RecruiterId is a 18-character identifier. You have provided '20' characters."
                         |    }
                         |  ]
                         |}
                         |""".stripMargin

    val request = Request[IO](GET, Uri.unsafeFromString(s"/profiles/$validProfileId?query=$validQuery")).putHeaders(
      Accept(MediaType.application.json),
      Header.Raw(ci"X-Recruiter-ID", invalidRecruiterId)
    )

    client.run(request).use { response =>
      response.as[String].map { body =>
        expectJsonEquals(body, expectedJson) and expect(clue(response.status) == Status.BadRequest)
      }
    }
  }

  test("Invalid path segment will give a BadRequest with a specific error message") {
    val expectedJson = """{
                         |  "errors" : [
                         |    {
                         |        "parameterType" : "pathsegment",
                         |        "inputName" : "id",
                         |        "message" : "ProfileId is a 36-character identifier. You have provided '18' characters."
                         |    }
                         |  ]
                         |}
                         |""".stripMargin

    val request = Request[IO](GET, Uri.unsafeFromString(s"/profiles/$invalidProfileId?query=$validQuery")).putHeaders(
      Accept(MediaType.application.json),
      Header.Raw(ci"X-Recruiter-ID", validRecruiterId)
    )

    client.run(request).use { response =>
      response.as[String].map { body =>
        expectJsonEquals(body, expectedJson) and expect(clue(response.status) == Status.BadRequest)
      }
    }
  }

  test("When a UnknownError is returned without a parameter then the field is als not available in the response") {
    val expectedJson = """{
                         |    "message" : "This is a dummy error for testing purposes."
                         |  }""".stripMargin

    val request = Request[IO](GET, Uri.unsafeFromString(s"/profiles/$validProfileId?query=$validQuery")).putHeaders(
      Accept(MediaType.application.json),
      Header.Raw(ci"X-Recruiter-ID", validRecruiterId)
    )

    client.run(request).use { response =>
      response.as[String].map { body =>
        expectJsonEquals(body, expectedJson) and expect(clue(response.status) == Status.InternalServerError)
      }
    }
  }

  test("Second endpoint - on same url - also works with valid inputs") {
    val expectedJson = """{    "message" : "This is a dummy error for testing purposes."
                         |  }""".stripMargin
    val request = Request[IO](PUT, Uri.unsafeFromString(s"/profiles/$validProfileId?query=$validQuery")).putHeaders(
      Accept(MediaType.application.json),
      Header.Raw(ci"X-Recruiter-ID", validRecruiterId)
    )

    client.run(request).use { response =>
      response.as[String].map { body =>
        expectJsonEquals(body, expectedJson) and expect(clue(response.status) == Status.InternalServerError)
      }
    }
  }
}
