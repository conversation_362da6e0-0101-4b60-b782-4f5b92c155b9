package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.SavedSearchName
import weaver.FunSuite

object SavedSearchNameSpec extends FunSuite {

  test("SavedSearchName should be json encoded as a string") {
    val savedSearchName = SavedSearchName("Saved search 1")
    val json = savedSearchName.asJson

    expect(json == "Saved search 1".asJson)
  }

  test("SavedSearchName should be encoded and decoded correctly") {
    val savedSearchName = SavedSearchName("Saved search 1")

    val roundTrippedSavedSearchName = decode[SavedSearchName](savedSearchName.asJson.noSpaces)

    expect(roundTrippedSavedSearchName == Right(savedSearchName))
  }
}
