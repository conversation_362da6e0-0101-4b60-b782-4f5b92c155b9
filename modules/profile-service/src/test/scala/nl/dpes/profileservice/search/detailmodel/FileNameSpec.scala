package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object FileNameSpec extends FunSuite {
  test("FileName should be json encoded as a string") {
    val fileName = FileName("document.pdf")
    val json = fileName.asJson

    expect(json == "document.pdf".asJson)
  }

  test("FileName should be encoded and decoded correctly") {
    val fileName = FileName("document.pdf")

    val roundTrippedFileName = decode[FileName](fileName.asJson.noSpaces)

    expect(roundTrippedFileName == Right(fileName))
  }

  test("FileName should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[FileName]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("FileName should transform from detailpage.FileName") {
    val detailPageFileName = detailpage.FileName("document.pdf")
    val fileName = FileName(detailPageFileName.value)

    val transformedFileName = detailPageFileName.into[FileName].transform

    expect(transformedFileName == fileName)
  }
}
