package nl.dpes.profileservice.tapir

import cats.effect.IO
import nl.dpes.profileservice.tracing.CustomServerLog
import sttp.model.*
import sttp.tapir.DecodeResult.{Error, Missing}
import sttp.tapir.model.{ConnectionInfo, ServerRequest}
import sttp.tapir.server.interceptor.log.ExceptionContext
import sttp.tapir.server.interceptor.{DecodeFailureContext, DecodeSuccessContext, SecurityFailureContext}
import sttp.tapir.server.model.ServerResponse
import sttp.tapir.{endpoint as TapirEndpoint, path, AttributeKey}
import weaver.*

object CustomServerLogSpec extends SimpleIOSuite {

  val recruiterIdHeader = new Header("X-Recruiter-ID", "123456789123456789")

  case class TestServerRequest(method: Method, path: String) extends ServerRequest {
    override def protocol: String = "HTTP/1.1"
    override def connectionInfo: ConnectionInfo = ???
    override def underlying: Any = this
    override def pathSegments: List[String] = path.split("/").toList.filter(_.nonEmpty)
    override def queryParameters: QueryParams = QueryParams.apply()
    override def uri: Uri = Uri.unsafeParse(s"http://localhost$path")
    override def headers: Seq[Header] = Seq(recruiterIdHeader)
    override def showShort: String = s"$method $path"
    override def attribute[T](k: AttributeKey[T]): Option[T] = ???
    override def attribute[T](k: AttributeKey[T], v: T): ServerRequest = ???
    override def withUnderlying(underlying: Any): ServerRequest = ???
  }

  // Testing 'requestReceived'

  test("The 'requestReceived' method should create JSON log when logWhenReceived is true") {
    for {
      loggedMessage <- IO.ref(Option.empty[String])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (_, _) => IO.unit,
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logWhenReceived = true
      )

      testRequest = TestServerRequest(Method.GET, "/test")
      token = 123L

      _      <- serverLog.requestReceived(testRequest, token)
      result <- loggedMessage.get

    } yield expect(result.isDefined) and
      expect(result.get.contains("GET /test")) and
      expect(result.get.startsWith("{")) and
      expect(result.get.endsWith("}"))
  }

  test("The 'requestReceived' method should not log when logWhenReceived is false") {
    for {
      loggedMessage <- IO.ref(Option.empty[String])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (_, _) => IO.unit,
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logWhenReceived = false
      )

      testRequest = TestServerRequest(Method.POST, "/api/users")
      token = 123L

      _      <- serverLog.requestReceived(testRequest, token)
      result <- loggedMessage.get

    } yield expect(result.isEmpty)
  }

  // Testing 'decodeFailureNotHandled'

  test("The 'decodeFailureNotHandled' method should create JSON log when logAllDecodeFailures is true") {
    for {
      loggedMessage   <- IO.ref(Option.empty[String])
      loggedException <- IO.ref(Option.empty[Throwable])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (_, _) => IO.unit,
        doLogAllDecodeFailures = (msg, ex) => loggedMessage.set(Some(msg)) >> IO.whenA(ex.isDefined)(loggedException.set(ex)),
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logAllDecodeFailures = true
      )

      testRequest = TestServerRequest(Method.POST, "/api/test")
      endpoint = TapirEndpoint.post.in(path[String]("test"))
      failure = Missing
      failingInput = path[String]("test")

      ctx = DecodeFailureContext(
        endpoint = endpoint,
        request = testRequest,
        failure = failure,
        failingInput = failingInput
      )
      token = 123L

      _      <- serverLog.decodeFailureNotHandled(ctx, token)
      result <- loggedMessage.get

    } yield expect(result.isDefined) and
      expect(result.get.contains("POST /api/test")) and
      expect(result.get.contains("handledBy")) and
      expect(result.get.contains("failure")) and
      expect(result.get.contains("input")) and
      expect(result.get.startsWith("{")) and
      expect(result.get.endsWith("}"))
  }

  test("The 'decodeFailureNotHandled' method should not log when logAllDecodeFailures is false") {
    for {
      loggedMessage <- IO.ref(Option.empty[String])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (_, _) => IO.unit,
        doLogAllDecodeFailures = (msg, _) => loggedMessage.set(Some(msg)),
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logAllDecodeFailures = false
      )

      testRequest = TestServerRequest(Method.GET, "/api/test")
      endpoint = TapirEndpoint.get.in(path[String]("test"))
      failure = Missing
      failingInput = path[String]("test")

      ctx = DecodeFailureContext(
        endpoint = endpoint,
        request = testRequest,
        failure = failure,
        failingInput = failingInput
      )
      token = 123L

      _      <- serverLog.decodeFailureNotHandled(ctx, token)
      result <- loggedMessage.get

    } yield expect(result.isEmpty)
  }

  test("The 'decodeFailureNotHandled' method should log exception when DecodeResult.Error contains throwable") {
    for {
      loggedMessage   <- IO.ref(Option.empty[String])
      loggedException <- IO.ref(Option.empty[Throwable])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (_, _) => IO.unit,
        doLogAllDecodeFailures = (msg, ex) => loggedMessage.set(Some(msg)) >> IO.whenA(ex.isDefined)(loggedException.set(ex)),
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logAllDecodeFailures = true
      )

      testRequest = TestServerRequest(Method.PUT, "/api/test")
      endpoint = TapirEndpoint.put.in(path[String]("test"))
      testException = new RuntimeException("Test decode error")
      failure = Error("decode error", testException)
      failingInput = path[String]("test")

      ctx = DecodeFailureContext(
        endpoint = endpoint,
        request = testRequest,
        failure = failure,
        failingInput = failingInput
      )
      token = 123L

      _         <- serverLog.decodeFailureNotHandled(ctx, token)
      result    <- loggedMessage.get
      exception <- loggedException.get

    } yield expect(result.isDefined) and
      expect(exception.isDefined) and
      expect(exception.get.getMessage == "Test decode error")
  }

  // Testing 'decodeFailureHandled'

  test("The 'decodeFailureHandled' method should create JSON log when logWhenHandled is true") {
    for {
      loggedMessage   <- IO.ref(Option.empty[String])
      loggedException <- IO.ref(Option.empty[Throwable])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (msg, ex) => loggedMessage.set(Some(msg)) >> IO.whenA(ex.isDefined)(loggedException.set(ex)),
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logWhenHandled = true
      )

      testRequest = TestServerRequest(Method.POST, "/api/test")
      endpoint = TapirEndpoint.post.in(path[String]("test"))
      failure = Missing
      failingInput = path[String]("test")
      response = ServerResponse(StatusCode.Ok, Seq.empty, None, None)

      ctx = DecodeFailureContext(
        endpoint = endpoint,
        request = testRequest,
        failure = failure,
        failingInput = failingInput
      )
      token = 123L

      _      <- serverLog.decodeFailureHandled(ctx, response, token)
      result <- loggedMessage.get

    } yield expect(result.isDefined) and
      expect(result.get.contains("POST /api/test")) and
      expect(result.get.contains("handledBy")) and
      expect(result.get.contains("input")) and
      expect(result.get.startsWith("{")) and
      expect(result.get.endsWith("}"))
  }

  test("The 'decodeFailureHandled' method should not log when logWhenHandled is false") {
    for {
      loggedMessage <- IO.ref(Option.empty[String])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (msg, _) => loggedMessage.set(Some(msg)),
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logWhenHandled = false
      )

      testRequest = TestServerRequest(Method.GET, "/api/test")
      endpoint = TapirEndpoint.get.in(path[String]("test"))
      failure = Missing
      failingInput = path[String]("test")
      response = ServerResponse(StatusCode.Ok, Seq.empty, None, None)

      ctx = DecodeFailureContext(
        endpoint = endpoint,
        request = testRequest,
        failure = failure,
        failingInput = failingInput
      )
      token = 123L

      _      <- serverLog.decodeFailureHandled(ctx, response, token)
      result <- loggedMessage.get

    } yield expect(result.isEmpty)
  }

  test("The 'decodeFailureHandled' method should log exception when DecodeResult.Error contains throwable") {
    for {
      loggedMessage   <- IO.ref(Option.empty[String])
      loggedException <- IO.ref(Option.empty[Throwable])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (msg, ex) => loggedMessage.set(Some(msg)) >> IO.whenA(ex.isDefined)(loggedException.set(ex)),
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logWhenHandled = true
      )

      testRequest = TestServerRequest(Method.PUT, "/api/test")
      endpoint = TapirEndpoint.put.in(path[String]("test"))
      testException = new RuntimeException("Test decode error")
      failure = Error("decode error", testException)
      failingInput = path[String]("test")
      response = ServerResponse(StatusCode.BadRequest, Seq.empty, Some("Invalid input"), None)

      ctx = DecodeFailureContext(
        endpoint = endpoint,
        request = testRequest,
        failure = failure,
        failingInput = failingInput
      )
      token = 123L

      _         <- serverLog.decodeFailureHandled(ctx, response, token)
      result    <- loggedMessage.get
      exception <- loggedException.get

    } yield expect(result.isDefined) and
      expect(result.get.contains("PUT /api/test")) and
      expect(exception.isDefined) and
      expect(exception.get.getMessage == "Test decode error")
  }

  // Testing 'securityFailureHandled'

  test("The 'securityFailureHandled' method should create JSON log when logWhenHandled is true") {
    for {
      loggedMessage   <- IO.ref(Option.empty[String])
      loggedException <- IO.ref(Option.empty[Throwable])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (msg, ex) => loggedMessage.set(Some(msg)) >> IO.whenA(ex.isDefined)(loggedException.set(ex)),
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logWhenHandled = true
      )

      testRequest = TestServerRequest(Method.POST, "/api/secure")
      endpoint = TapirEndpoint.post.in(path[String]("secure"))
      serverEndpoint = endpoint.serverLogic(_ => IO.pure(Right("success")))
      response = ServerResponse(StatusCode.Unauthorized, Seq.empty, None, None)

      ctx = SecurityFailureContext(
        serverEndpoint = serverEndpoint,
        securityInput = "test-input",
        request = testRequest
      )
      token = 123L

      _         <- serverLog.securityFailureHandled(ctx, response, token)
      result    <- loggedMessage.get
      exception <- loggedException.get

    } yield expect(result.isDefined) and
      expect(result.get.contains("POST /api/secure")) and
      expect(result.get.contains("handledBy")) and
      expect(result.get.contains("securityErrorResponse")) and
      expect(exception.isEmpty) and
      expect(result.get.startsWith("{")) and
      expect(result.get.endsWith("}"))
  }

  test("The 'securityFailureHandled' method should not log when logWhenHandled is false") {
    for {
      loggedMessage <- IO.ref(Option.empty[String])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (msg, _) => loggedMessage.set(Some(msg)),
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logWhenHandled = false
      )

      testRequest = TestServerRequest(Method.GET, "/api/secure")
      endpoint = TapirEndpoint.get.in(path[String]("secure"))
      serverEndpoint = endpoint.serverLogic(_ => IO.pure(Right("success")))
      response = ServerResponse(StatusCode.Forbidden, Seq.empty, None, None)

      ctx = SecurityFailureContext(
        serverEndpoint = serverEndpoint,
        securityInput = "test-input",
        request = testRequest
      )
      token = 123L

      _      <- serverLog.securityFailureHandled(ctx, response, token)
      result <- loggedMessage.get

    } yield expect(result.isEmpty)
  }

  // Testing 'requestHandled'

  test("The 'requestHandled' method should create JSON log when logWhenHandled is true") {
    for {
      loggedMessage   <- IO.ref(Option.empty[String])
      loggedException <- IO.ref(Option.empty[Throwable])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (msg, ex) => loggedMessage.set(Some(msg)) >> IO.whenA(ex.isDefined)(loggedException.set(ex)),
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logWhenHandled = true
      )

      testRequest = TestServerRequest(Method.GET, "/api/users")
      endpoint = TapirEndpoint.get.in(path[String]("users"))
      serverEndpoint = endpoint.serverLogic(_ => IO.pure(Right("success")))
      response = ServerResponse(StatusCode.Ok, Seq.empty, None, None)

      ctx = DecodeSuccessContext(
        serverEndpoint = serverEndpoint,
        securityInput = "security-input",
        principal = "user-principal",
        input = "request-input",
        request = testRequest
      )
      token = 123L

      _         <- serverLog.requestHandled(ctx, response, token)
      result    <- loggedMessage.get
      exception <- loggedException.get

    } yield expect(result.isDefined) and
      expect(result.get.contains("GET /api/users")) and
      expect(result.get.contains("handledBy")) and
      expect(result.get.contains("took")) and
      expect(exception.isEmpty) and
      expect(result.get.startsWith("{")) and
      expect(result.get.endsWith("}"))
  }

  test("The 'requestHandled' method should not log when logWhenHandled is false") {
    for {
      loggedMessage <- IO.ref(Option.empty[String])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (msg, _) => loggedMessage.set(Some(msg)),
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logWhenHandled = false
      )

      testRequest = TestServerRequest(Method.POST, "/api/users")
      endpoint = TapirEndpoint.post.in(path[String]("users"))
      serverEndpoint = endpoint.serverLogic(_ => IO.pure(Right("created")))
      response = ServerResponse(StatusCode.Created, Seq.empty, None, None)

      ctx = DecodeSuccessContext(
        serverEndpoint = serverEndpoint,
        securityInput = "security-input",
        principal = "user-principal",
        input = "request-input",
        request = testRequest
      )
      token = 123L

      _      <- serverLog.requestHandled(ctx, response, token)
      result <- loggedMessage.get

    } yield expect(result.isEmpty)
  }

  test("The 'requestHandled' method should include timing information when includeTiming is true") {
    for {
      loggedMessage <- IO.ref(Option.empty[String])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (msg, _) => loggedMessage.set(Some(msg)),
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (_, _) => IO.unit,
        noLog = IO.unit,
        logWhenHandled = true,
        includeTiming = true
      )

      testRequest = TestServerRequest(Method.PUT, "/api/users")
      endpoint = TapirEndpoint.put.in(path[String]("users"))
      serverEndpoint = endpoint.serverLogic(_ => IO.pure(Right("updated")))
      response = ServerResponse(StatusCode.Ok, Seq.empty, None, None)

      ctx = DecodeSuccessContext(
        serverEndpoint = serverEndpoint,
        securityInput = "security-input",
        principal = "user-principal",
        input = "request-input",
        request = testRequest
      )
      token = System.currentTimeMillis() - 50 // simulate 50ms ago

      _      <- serverLog.requestHandled(ctx, response, token)
      result <- loggedMessage.get

    } yield expect(result.isDefined) and
      expect(result.get.contains("PUT /api/users")) and
      expect(result.get.contains("took")) and
      expect(result.get.contains("ms"))
  }

  // Testing 'exception'

  test("The 'exception' method should create JSON log when logLogicExceptions is true") {
    for {
      loggedMessage   <- IO.ref(Option.empty[String])
      loggedException <- IO.ref(Option.empty[Throwable])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (msg, ex) => loggedMessage.set(Some(msg)) >> IO.whenA(ex.isDefined)(loggedException.set(ex)),
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (msg, ex) => loggedMessage.set(Some(msg)) >> loggedException.set(Some(ex)),
        noLog = IO.unit,
        logLogicExceptions = true
      )

      testRequest = TestServerRequest(Method.POST, "/api/users")
      endpoint = TapirEndpoint.post.in(path[String]("users"))
      testException = new RuntimeException("Business logic error")

      ctx = ExceptionContext(
        endpoint = endpoint,
        securityInput = Some("security-input"),
        principal = Some("user-principal"),
        request = testRequest
      )
      token = 123L

      _         <- serverLog.exception(ctx, testException, token)
      result    <- loggedMessage.get
      exception <- loggedException.get

    } yield expect(result.isDefined) and
      expect(result.get.contains("POST /api/users")) and
      expect(result.get.contains("Exception when handling request, caused by")) and
      expect(result.get.contains("took")) and
      expect(exception.isDefined) and
      expect(exception.get.getMessage == "Business logic error") and
      expect(result.get.startsWith("{")) and
      expect(result.get.endsWith("}"))
  }

  test("The 'exception' method should not log when logLogicExceptions is false") {
    for {
      loggedMessage   <- IO.ref(Option.empty[String])
      loggedException <- IO.ref(Option.empty[Throwable])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (msg, ex) => loggedMessage.set(Some(msg)) >> IO.whenA(ex.isDefined)(loggedException.set(ex)),
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (msg, ex) => loggedMessage.set(Some(msg)) >> loggedException.set(Some(ex)),
        noLog = IO.unit,
        logLogicExceptions = false
      )

      testRequest = TestServerRequest(Method.GET, "/api/users")
      endpoint = TapirEndpoint.get.in(path[String]("users"))
      testException = new IllegalArgumentException("Invalid parameter")

      ctx = ExceptionContext(
        endpoint = endpoint,
        securityInput = None,
        principal = None,
        request = testRequest
      )
      token = 123L

      _         <- serverLog.exception(ctx, testException, token)
      result    <- loggedMessage.get
      exception <- loggedException.get

    } yield expect(result.isEmpty) and
      expect(exception.isEmpty)
  }

  test("The 'exception' method should include timing information when includeTiming is true") {
    for {
      loggedMessage   <- IO.ref(Option.empty[String])
      loggedException <- IO.ref(Option.empty[Throwable])

      serverLog = CustomServerLog[IO](
        doLogWhenReceived = msg => loggedMessage.set(Some(msg)),
        doLogWhenHandled = (msg, ex) => loggedMessage.set(Some(msg)) >> IO.whenA(ex.isDefined)(loggedException.set(ex)),
        doLogAllDecodeFailures = (_, _) => IO.unit,
        doLogExceptions = (msg, ex) => loggedMessage.set(Some(msg)) >> loggedException.set(Some(ex)),
        noLog = IO.unit,
        logLogicExceptions = true,
        includeTiming = true
      )

      testRequest = TestServerRequest(Method.DELETE, "/api/users")
      endpoint = TapirEndpoint.delete.in(path[String]("users"))
      testException = new Exception("Database connection failed")

      ctx = ExceptionContext(
        endpoint = endpoint,
        securityInput = Some("security-input"),
        principal = Some("user-principal"),
        request = testRequest
      )
      token = System.currentTimeMillis() - 200 // simulate 200ms ago

      _      <- serverLog.exception(ctx, testException, token)
      result <- loggedMessage.get

    } yield expect(result.isDefined) and
      expect(result.get.contains("DELETE /api/users")) and
      expect(result.get.contains("took")) and
      expect(result.get.contains("ms"))
  }
}
