package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.SearchTerm
import weaver.FunSuite

object SearchTermSpec extends FunSuite {

  test("SearchTerm should be json encoded as a string") {
    val searchTerm = SearchTerm("Scala developer")
    val json = searchTerm.asJson

    expect(json == "Scala developer".asJson)
  }

  test("SearchTerm should be encoded and decoded correctly") {
    val searchTerm = SearchTerm("Scala developer")

    val roundTrippedSearchTerm = decode[SearchTerm](searchTerm.asJson.noSpaces)

    expect(roundTrippedSearchTerm == Right(searchTerm))
  }
}
