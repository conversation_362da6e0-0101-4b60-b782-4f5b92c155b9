package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object TrainingNameSpec extends FunSuite {
  test("TrainingName should be json encoded as a string") {
    val trainingName = TrainingName("Scala Programming")
    val json = trainingName.asJson

    expect(json == "Scala Programming".asJson)
  }

  test("TrainingName should be encoded and decoded correctly") {
    val trainingName = TrainingName("Scala Programming")

    val roundTrippedTrainingName = decode[TrainingName](trainingName.asJson.noSpaces)

    expect(roundTrippedTrainingName == Right(trainingName))
  }

  test("TrainingName should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[TrainingName]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("TrainingName should transform from detailpage.TrainingName") {
    val detailPageTrainingName = detailpage.TrainingName("Scala Programming")
    val trainingName = TrainingName(detailPageTrainingName.value)

    val transformedTrainingName = detailPageTrainingName.into[TrainingName].transform

    expect(transformedTrainingName == trainingName)
  }
}
