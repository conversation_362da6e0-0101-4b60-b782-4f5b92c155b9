package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.WorkingHour
import weaver.FunSuite

object WorkingHourSpec extends FunSuite {

  test("WorkingHour should be json encoded as a string") {
    val workingHour = WorkingHour("10-40")
    val json = workingHour.asJson

    expect(json == "10-40".asJson)
  }

  test("WorkingHour should be encoded and decoded correctly") {
    val workingHour = WorkingHour("10-40")

    val roundTrippedWorkingHour = decode[WorkingHour](workingHour.asJson.noSpaces)

    expect(roundTrippedWorkingHour == Right(workingHour))
  }
}
