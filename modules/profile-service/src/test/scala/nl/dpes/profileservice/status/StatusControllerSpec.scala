package nl.dpes.profileservice.status

import cats.syntax.either.*
import cats.effect.IO
import sttp.client3.*
import cats.implicits.toSemigroupKOps
import cats.syntax.applicative.*
import sttp.client3.testing.SttpBackendStub
import sttp.tapir.server.stub.TapirStubInterpreter
import weaver.SimpleIOSuite
import sttp.model.StatusCode
import sttp.tapir.integ.cats.effect.CatsMonadError
import cats.effect.unsafe.implicits.global
import nl.dpes.profileservice.status.StatusController
import sttp.tapir.server.http4s.Http4sServerInterpreter
import sttp.tapir.swagger.SwaggerUIOptions
import sttp.tapir.swagger.bundle.SwaggerInterpreter

object StatusControllerSpec extends SimpleIOSuite {

  val unit: Unit = ()

  test("It should return OK when invoking the status endpoint") {
    for {
      controller <- StatusController.impl[IO].showStatus
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(controller.serverLogic(_ => IO(unit.asRight[Unit])))
          .thenRunLogic()
          .backend()
      )
      response <- IO(
        basicRequest
          .get(uri"http://localhost:14200/status")
          .send(backendStub)
      )
    } yield expect(response.unsafeRunSync().code == StatusCode.Ok)
  }

  test("It should return Not_Found when invoking an unknown endpoint") {
    for {
      controller <- StatusController.impl[IO].showStatus
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(controller.serverLogic(_ => IO(unit.asRight[Unit])))
          .thenRunLogic()
          .backend()
      )
      response <- IO(
        basicRequest
          .get(uri"http://localhost:14200/unknown")
          .send(backendStub)
      )
    } yield expect(response.unsafeRunSync().code == StatusCode.NotFound)
  }

  test("It should return all routes") {
    import org.http4s.*
    import org.http4s.implicits.*

    val request = Request[IO](Method.GET, uri"/status")
    for {
      actualRoutes <- StatusController.impl[IO].routes
      status       <- StatusController.impl[IO].showStatus.map(_.serverLogicSuccess(_ => ().pure))
      statusRoute  <- IO(Http4sServerInterpreter[IO]().toRoutes(status))

      swaggerRoutes <- IO.delay(
        Http4sServerInterpreter[IO]()
          .toRoutes(
            SwaggerInterpreter(swaggerUIOptions =
              SwaggerUIOptions(
                List("docs", "status", "v1"),
                "docs.yaml",
                Nil,
                useRelativePaths = false,
                showExtensions = true,
                None,
                None
              )
            )
              .fromServerEndpoints[IO](List(status), "status", "v1")
          )
      )
      expectedRoutes = swaggerRoutes <+> statusRoute

      actualResponse   <- actualRoutes.orNotFound.run(request)
      expectedResponse <- expectedRoutes.orNotFound.run(request)
    } yield expect(
      actualResponse.status == expectedResponse.status &&
        actualResponse.headers == expectedResponse.headers &&
        actualResponse.body == expectedResponse.body
    )
  }
}
