package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object AvailabilitySpec extends FunSuite {
  test("availability should be json encoded as a string") {
    val availability = Availability("direct")
    val json = availability.asJson

    expect(json == "direct".asJson)
  }

  test("Availability should be encoded and decoded correctly") {
    val availability = Availability("direct")

    val roundTrippedAvailability = decode[Availability](availability.asJson.noSpaces)

    expect(roundTrippedAvailability == Right(availability))
  }

  test("Availability should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[Availability]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("Availability should transform from detailpage.Availability") {
    val detailPageAvailability = detailpage.Availability("direct")
    val availability = Availability(detailPageAvailability.value)

    val transformedAvailability = detailPageAvailability.into[Availability].transform

    expect(transformedAvailability == availability)
  }
}
