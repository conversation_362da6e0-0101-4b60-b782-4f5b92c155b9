package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.CareerLevel
import weaver.FunSuite

object CareerLevelSpec extends FunSuite {

  test("CareerLevel should be json encoded as a string") {
    val careerLevel = CareerLevel("LEVEL")
    val json = careerLevel.asJson

    expect(json == "LEVEL".asJson)
  }

  test("CareerLevel should be encoded and decoded correctly") {
    val careerLevel = CareerLevel("LEVEL")

    val roundTrippedCareerLevel = decode[CareerLevel](careerLevel.asJson.noSpaces)

    expect(roundTrippedCareerLevel == Right(careerLevel))
  }
}
