package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object FilePathSpec extends FunSuite {
  test("FilePath should be json encoded as a string") {
    val filePath = FilePath("/path/to/document.pdf")
    val json = filePath.asJson

    expect(json == "/path/to/document.pdf".asJson)
  }

  test("FilePath should be encoded and decoded correctly") {
    val filePath = FilePath("/path/to/document.pdf")

    val roundTrippedFilePath = decode[FilePath](filePath.asJson.noSpaces)

    expect(roundTrippedFilePath == Right(filePath))
  }

  test("FilePath should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[FilePath]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("FilePath should transform from detailpage.FilePath") {
    val detailPageFilePath = detailpage.FilePath("/path/to/document.pdf")
    val filePath = FilePath(detailPageFilePath.value)

    val transformedFilePath = detailPageFilePath.into[FilePath].transform

    expect(transformedFilePath == filePath)
  }
}
