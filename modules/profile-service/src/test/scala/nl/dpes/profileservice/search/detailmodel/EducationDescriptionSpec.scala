package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object EducationDescriptionSpec extends FunSuite {
  test("EducationDescription should be json encoded as a string") {
    val educationDescription = EducationDescription("Bachelor of Science")
    val json = educationDescription.asJson

    expect(json == "Bachelor of Science".asJson)
  }

  test("EducationDescription should be encoded and decoded correctly") {
    val educationDescription = EducationDescription("Bachelor of Science")

    val roundTrippedEducationDescription = decode[EducationDescription](educationDescription.asJson.noSpaces)

    expect(roundTrippedEducationDescription == Right(educationDescription))
  }

  test("EducationDescription should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[EducationDescription]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("EducationDescription should transform from detailpage.EducationDescription") {
    val detailPageEducationDescription = detailpage.EducationDescription("Bachelor of Science")
    val educationDescription = EducationDescription(detailPageEducationDescription.value)

    val transformedEducationDescription = detailPageEducationDescription.into[EducationDescription].transform

    expect(transformedEducationDescription == educationDescription)
  }
}
