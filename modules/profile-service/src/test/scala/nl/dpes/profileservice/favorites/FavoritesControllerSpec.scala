package nl.dpes.profileservice.favorites

import cats.effect.IO
import nl.dpes.profileservice.tapir.ErrorMessage.*
import nl.dpes.profileservice.favorites.apimodel.{FavoriteResponse, ProfileId, RecruiterId}
import sttp.client3.testing.SttpBackendStub
import sttp.client3.{basicRequest, UriContext}
import sttp.model.StatusCode
import sttp.tapir.integ.cats.effect.CatsMonadError
import sttp.tapir.server.stub.TapirStubInterpreter
import weaver.SimpleIOSuite

object FavoritesControllerSpec extends SimpleIOSuite {

  val unit: Unit = ()
  val recruiterId: RecruiterId = RecruiterId("ABCDEFGHIJ12345678").toOption.get
  val profileId: ProfileId = ProfileId("550e8400-e29b-41d4-a716-446655440000").toOption.get

  def favoritesService(
    saveFavoriteFn: => IO[Either[BadRequest, Unit]] = IO(Right(unit)),
    deleteFavoriteFn: => IO[Either[BadRequest, Unit]] = IO(Right(unit)),
    deletePotentialFavoriteFn: => IO[Either[BadRequest, Unit]] = IO(Right(unit)),
    getFavoritesFn: => IO[Either[BadRequest, FavoriteResponse]] = IO(Right(FavoriteResponse(List(), 0)))
  ): FavoritesControllerService[IO] = new FavoritesControllerService[IO] {
    override def saveFavorite(recruiterId: RecruiterId, profileId: ProfileId): IO[Either[BadRequest, Unit]] = saveFavoriteFn

    override def deleteFavorite(recruiterId: RecruiterId, profileId: ProfileId): IO[Either[BadRequest, Unit]] = deleteFavoriteFn

    override def getFavorites(recruiterId: RecruiterId, page: Long, limit: Long): IO[Either[BadRequest, FavoriteResponse]] = getFavoritesFn

    override def deletePotentialFavorite(profileId: ProfileId): IO[Either[BadRequest, Unit]] = deletePotentialFavoriteFn
  }

  test("It should be able to save favorites") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.saveFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .post(uri"http://localhost:14200/favorites/${profileId.value}")
        .header("X-Recruiter-ID", recruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.Ok)
  }

  test("It should return BadRequest when the profile id is empty while trying to save a favorite") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.saveFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .post(uri"http://localhost:14200/favorites/     ")
        .header("X-Recruiter-ID", recruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should return BadRequest when the profile id has invalid length while trying to save a favorite") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.saveFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .post(uri"http://localhost:14200/favorites/550e8400-e29b-41d4-a716-446655440000-550e8400-e29b-41d4-a716-446655440000")
        .header("X-Recruiter-ID", recruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should return BadRequest when the recruiter id is empty while trying to save a favorite") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.saveFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .post(uri"http://localhost:14200/favorites/${profileId.value}")
        .header("X-Recruiter-ID", "    ")
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should return BadRequest when the recruiter id has an invalid length while trying to save a favorite") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.saveFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .post(uri"http://localhost:14200/favorites/${profileId.value}")
        .header("X-Recruiter-ID", "ABCDEFGHIJ123456789")
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should be able to delete favorites") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.deleteFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .delete(uri"http://localhost:14200/favorites/${profileId.value}")
        .header("X-Recruiter-ID", recruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.Ok)
  }

  test("It should return BadRequest when the profile id is empty while trying to delete a favorite") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.deleteFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .delete(uri"http://localhost:14200/favorites/     ")
        .header("X-Recruiter-ID", recruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should return BadRequest when the profile id has invalid length while trying to delete a favorite") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.deleteFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .delete(uri"http://localhost:14200/favorites/550e8400-e29b-41d4-a716-446655440000-550e8400-e29b-41d4-a716-446655440000")
        .header("X-Recruiter-ID", recruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should return BadRequest when the recruiter id is empty while trying to delete a favorite") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.deleteFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .delete(uri"http://localhost:14200/favorites/${profileId.value}")
        .header("X-Recruiter-ID", "    ")
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should return BadRequest when the recruiter id has an invalid length while trying to delete a favorite") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.deleteFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .delete(uri"http://localhost:14200/favorites/${profileId.value}")
        .header("X-Recruiter-ID", "ABCDEFGHIJ123456789")
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should return internal server error when an issue has occurred") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService(saveFavoriteFn = IO.raiseError(new Throwable("Error occurred")))))
      serverEndpoint <- controller.saveFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .post(uri"http://localhost:14200/favorites/${profileId.value}")
        .header("X-Recruiter-ID", recruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.InternalServerError)
  }

  test("It should return favorites for a recruiter") {
    import nl.dpes.profilesearch
    val favoriteResponse = FavoriteResponse(List.empty, 1)

    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService(getFavoritesFn = IO(Right(favoriteResponse)))))
      serverEndpoint <- controller.getFavorites
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/favorites")
        .header("X-Recruiter-ID", recruiterId.value)
        .send(backendStub)
    } yield expect(
      response.code == StatusCode.Ok && response.body == Right("""{"profiles":[],"totalNumberOfProfiles":1}"""),
      s"Check the response body: ${response.body.toString}"
    )
  }

  test("It should return BadRequest when the recruiter id is empty while trying to get favorites") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.getFavorites
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/favorites")
        .header("X-Recruiter-ID", "    ")
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should return BadRequest when the recruiter id has an invalid length while trying to get favorites") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.getFavorites
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/favorites")
        .header("X-Recruiter-ID", "ABCDEFGHIJ123456789")
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should return internal server error when an issue has occurred while getting favorites") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService(getFavoritesFn = IO.raiseError(new Throwable("Error occurred")))))
      serverEndpoint <- controller.getFavorites
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/favorites")
        .header("X-Recruiter-ID", recruiterId.value)
        .send(backendStub)
    } yield expect(response.code == StatusCode.InternalServerError)
  }

  test("It should be able to delete a favorite only by profile id") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.deletePotentialFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .delete(uri"http://localhost:14200/internal/favorites/${profileId.value}")
        .send(backendStub)
    } yield expect(response.code == StatusCode.Ok)
  }

  test("It should return BadRequest when the profile id is empty while trying to delete a favorite only by profile id") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.deletePotentialFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .delete(uri"http://localhost:14200/internal/favorites/     ")
        .send(backendStub)
    } yield expect(clue(response.code) == StatusCode.BadRequest)
  }

  test("It should return BadRequest when the profile id has invalid length while trying to delete a favorite only by profile id") {
    for {
      controller     <- IO(FavoritesController.impl[IO](favoritesService()))
      serverEndpoint <- controller.deletePotentialFavorite
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .delete(uri"http://localhost:14200/internal/favorites/550e8400-e29b-41d4-a716-446655440000-550e8400-e29b-41d4-a716-446655440000")
        .send(backendStub)
    } yield expect(clue(response.code) == StatusCode.BadRequest)
  }
}
