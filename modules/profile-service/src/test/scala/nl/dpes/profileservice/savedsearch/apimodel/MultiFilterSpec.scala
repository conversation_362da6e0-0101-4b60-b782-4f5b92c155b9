package nl.dpes.profileservice
package savedsearch.apimodel

import cats.data.NonEmptyList
import io.scalaland.chimney.dsl.*
import io.scalaland.chimney.partial
import io.scalaland.chimney.partial.ErrorMessage.EmptyValue
import nl.dpes.profilesearch.domain.filter.*
import nl.dpes.profilesearch.domain.{
  Availability as DomainAvailability,
  CareerLevel as DomainCareerLevel,
  DriversLicense as DomainDriversLicense,
  FunctionGroup as DomainFunctionGroup,
  Language as DomainLanguage,
  Province as DomainProvince,
  RequestedSalary as DomainRequestedSalary,
  WorkLevel as DomainWorkLevel,
  WorkingHours as DomainWorkingHours
}
import weaver.FunSuite

object MultiFilterSpec extends FunSuite {
  test("MultiFilter[Worklevel] should be able transform to and from WorkLevelFilter") {

    val apiValues: List[WorkLevel] = List(WorkLevel("value1"), WorkLevel("value2"), WorkLevel("value3"))
    val domainValues: NonEmptyList[DomainWorkLevel] =
      NonEmptyList.fromListUnsafe(List(DomainWorkLevel("value1"), DomainWorkLevel("value2"), DomainWorkLevel("value3")))

    val multiFilter: MultiFilter[WorkLevel] = MultiFilter(apiValues)
    val domainFilter: WorkLevelFilter = WorkLevelFilter(domainValues)

    expect.same(multiFilter.intoPartial[WorkLevelFilter].transform.asEither, Right(domainFilter)) and
    expect.same(domainFilter.into[MultiFilter[WorkLevel]].transform, multiFilter)
  }

  test("MultiFilter[Worklevel] should not be empty when transforming to WorkLevelFilter") {
    val emptyMultiFilter: MultiFilter[WorkLevel] = MultiFilter(Seq.empty)

    emptyMultiFilter.intoPartial[WorkLevelFilter].transform.asEither match {
      case Right(_) => failure("Expected an exception for empty MultiFilter")
      case Left(e)  => expect(clue(e.errors.toList) == List(partial.Error(EmptyValue)))
    }
  }

  test("MultiFilter[Provinces] should be able transform to and from ProvinceFilter") {
    val apiValues: List[Province] = List(Province("province1"), Province("province2"), Province("province3"))
    val domainValues: NonEmptyList[DomainProvince] =
      NonEmptyList.fromListUnsafe(List(DomainProvince("province1"), DomainProvince("province2"), DomainProvince("province3")))

    val multiFilter: MultiFilter[Province] = MultiFilter(apiValues)
    val domainFilter: ProvinceFilter = ProvinceFilter(domainValues)

    expect.same(multiFilter.intoPartial[ProvinceFilter].transform.asEither, Right(domainFilter)) and
    expect.same(domainFilter.into[MultiFilter[Province]].transform, multiFilter)
  }

  test("MultiFilter[Worklevel] should not be empty when transforming to WorkLevelFilter") {
    val emptyMultiFilter: MultiFilter[Province] = MultiFilter(Seq.empty)

    emptyMultiFilter.intoPartial[ProvinceFilter].transform.asEither match {
      case Right(_) => failure("Expected an exception for empty MultiFilter")
      case Left(e)  => expect(clue(e.errors.toList) == List(partial.Error(EmptyValue)))
    }
  }

  test("MultiFilter[FunctionGroup] should be able transform to and from FunctionGroupFilter") {
    val apiValues: List[FunctionGroup] = List(FunctionGroup("group1"), FunctionGroup("group2"), FunctionGroup("group3"))
    val domainValues: NonEmptyList[nl.dpes.profilesearch.domain.FunctionGroup] =
      NonEmptyList.fromListUnsafe(
        List(
          DomainFunctionGroup("group1"),
          DomainFunctionGroup("group2"),
          DomainFunctionGroup("group3")
        )
      )

    val multiFilter: MultiFilter[FunctionGroup] = MultiFilter(apiValues)
    val domainFilter: FunctionGroupFilter = FunctionGroupFilter(domainValues)

    expect.same(multiFilter.intoPartial[FunctionGroupFilter].transform.asEither, Right(domainFilter)) and
    expect.same(domainFilter.into[MultiFilter[FunctionGroup]].transform, multiFilter)
  }

  test("MultiFilter[FunctionGroup] should not be empty when transforming to FunctionGroupFilter") {
    val emptyMultiFilter: MultiFilter[FunctionGroup] = MultiFilter(Seq.empty)

    emptyMultiFilter.intoPartial[FunctionGroupFilter].transform.asEither match {
      case Right(_) => failure("Expected an exception for empty MultiFilter")
      case Left(e)  => expect(clue(e.errors.toList) == List(partial.Error(EmptyValue)))
    }
  }

  test("MultiFilter[CareerLevel] should be able transform to and from CareerLevelFilter") {
    val apiValues: List[CareerLevel] = List(CareerLevel("level1"), CareerLevel("level2"), CareerLevel("level3"))
    val domainValues: NonEmptyList[nl.dpes.profilesearch.domain.CareerLevel] =
      NonEmptyList.fromListUnsafe(
        List(
          DomainCareerLevel("level1"),
          DomainCareerLevel("level2"),
          DomainCareerLevel("level3")
        )
      )

    val multiFilter: MultiFilter[CareerLevel] = MultiFilter(apiValues)
    val domainFilter: CareerLevelFilter = CareerLevelFilter(domainValues)

    expect.same(multiFilter.intoPartial[CareerLevelFilter].transform.asEither, Right(domainFilter)) and
    expect.same(domainFilter.into[MultiFilter[CareerLevel]].transform, multiFilter)
  }

  test("MultiFilter[CareerLevel] should not be empty when transforming to CareerLevelFilter") {
    val emptyMultiFilter: MultiFilter[CareerLevel] = MultiFilter(Seq.empty)

    emptyMultiFilter.intoPartial[CareerLevelFilter].transform.asEither match {
      case Right(_) => failure("Expected an exception for empty MultiFilter")
      case Left(e)  => expect(clue(e.errors.toList) == List(partial.Error(EmptyValue)))
    }
  }

  test("MultiFilter[RequestedSalary] should be able transform to and from RequestedSalaryFilter") {
    val apiValues: List[RequestedSalary] = List(
      RequestedSalary("< 1.750"),
      RequestedSalary("3.500 - 5.000")
    )
    val domainValues: NonEmptyList[nl.dpes.profilesearch.domain.RequestedSalary] =
      NonEmptyList.fromListUnsafe(
        List(
          DomainRequestedSalary.fromString("< 1.750").get,
          DomainRequestedSalary.fromString("3.500 - 5.000").get
        )
      )

    val multiFilter: MultiFilter[RequestedSalary] = MultiFilter(apiValues)
    val domainFilter: RequestedSalaryFilter = RequestedSalaryFilter(domainValues)

    expect.same(multiFilter.intoPartial[RequestedSalaryFilter].transform.asEither, Right(domainFilter)) and
    expect.same(domainFilter.into[MultiFilter[RequestedSalary]].transform, multiFilter)
  }

  test("MultiFilter[RequestedSalary] should not be empty when transforming to RequestedSalaryFilter") {
    val emptyMultiFilter: MultiFilter[RequestedSalary] = MultiFilter(Seq.empty)

    emptyMultiFilter.intoPartial[RequestedSalaryFilter].transform.asEither match {
      case Right(_) => failure("Expected an exception for empty MultiFilter")
      case Left(e)  => expect(clue(e.errors.toList) == List(partial.Error(EmptyValue)))
    }
  }

  test("Unknown values for requested salary are ignored") {
    val apiValues: List[RequestedSalary] = List(
      RequestedSalary("< 1.750"),
      RequestedSalary("unknown value")
    )
    val domainValues: NonEmptyList[nl.dpes.profilesearch.domain.RequestedSalary] =
      NonEmptyList.fromListUnsafe(List(DomainRequestedSalary.fromString("< 1.750").get))

    val multiFilter: MultiFilter[RequestedSalary] = MultiFilter(apiValues)
    val domainFilter: RequestedSalaryFilter = RequestedSalaryFilter(domainValues)

    expect.same(multiFilter.intoPartial[RequestedSalaryFilter].transform.asEither, Right(domainFilter))
  }

  test("MultiFilter[DriversLicense] should be able transform to and from DriversLicenseFilter") {
    val apiValues: List[DriverLicense] = List(DriverLicense("B"), DriverLicense("C"))
    val domainValues: NonEmptyList[nl.dpes.profilesearch.domain.DriversLicense] =
      NonEmptyList.fromListUnsafe(List(DomainDriversLicense("B"), DomainDriversLicense("C")))

    val multiFilter: MultiFilter[DriverLicense] = MultiFilter(apiValues)
    val domainFilter: DriversLicenseFilter = DriversLicenseFilter(domainValues)

    expect.same(multiFilter.intoPartial[DriversLicenseFilter].transform.asEither, Right(domainFilter)) and
    expect.same(domainFilter.into[MultiFilter[DriverLicense]].transform, multiFilter)
  }

  test("MultiFilter[DriversLicense] should not be empty when transforming to DriversLicenseFilter") {
    val emptyMultiFilter: MultiFilter[DriverLicense] = MultiFilter(Seq.empty)

    emptyMultiFilter.intoPartial[DriversLicenseFilter].transform.asEither match {
      case Right(_) => failure("Expected an exception for empty MultiFilter")
      case Left(e)  => expect(clue(e.errors.toList) == List(partial.Error(EmptyValue)))
    }
  }

  test("MultiFilter[WorkingHours] should be able transform to and from WorkingHoursFilter") {
    val apiValues: List[WorkingHour] = List(WorkingHour("Tot 16 uur"), WorkingHour("16 tot 24 uur"))
    val domainValues: NonEmptyList[nl.dpes.profilesearch.domain.WorkingHours] =
      NonEmptyList.fromListUnsafe(List(DomainWorkingHours.fromString("Tot 16 uur").get, DomainWorkingHours.fromString("16 tot 24 uur").get))

    val multiFilter: MultiFilter[WorkingHour] = MultiFilter(apiValues)
    val domainFilter: WorkingHourFilter = WorkingHourFilter(domainValues)

    expect.same(multiFilter.intoPartial[WorkingHourFilter].transform.asEither, Right(domainFilter)) and
    expect.same(domainFilter.into[MultiFilter[WorkingHour]].transform, multiFilter)
  }

  test("MultiFilter[WorkingHours] should not be empty when transforming to WorkingHoursFilter") {
    val emptyMultiFilter: MultiFilter[WorkingHour] = MultiFilter(Seq.empty)

    emptyMultiFilter.intoPartial[WorkingHourFilter].transform.asEither match {
      case Right(_) => failure("Expected an exception for empty MultiFilter")
      case Left(e)  => expect(clue(e.errors.toList) == List(partial.Error(EmptyValue)))
    }
  }

  test("Unknown values for working hours are ignored") {
    val apiValues: List[WorkingHour] = List(WorkingHour("Tot 16 uur"), WorkingHour("unknown value"))
    val domainValues: NonEmptyList[nl.dpes.profilesearch.domain.WorkingHours] =
      NonEmptyList.fromListUnsafe(List(DomainWorkingHours.fromString("Tot 16 uur").get))

    val multiFilter: MultiFilter[WorkingHour] = MultiFilter(apiValues)
    val domainFilter: WorkingHourFilter = WorkingHourFilter(domainValues)

    expect.same(multiFilter.intoPartial[WorkingHourFilter].transform.asEither, Right(domainFilter))
  }

  test("MultiFilter[Availability] should be able transform to and from AvailabilityFilter") {
    val apiValues: List[Availability] = List(Availability("In overleg"), Availability("Per direct"))
    val domainValues: NonEmptyList[nl.dpes.profilesearch.domain.Availability] =
      NonEmptyList.fromListUnsafe(List(DomainAvailability.InOverleg, DomainAvailability.PerDirect))

    val multiFilter: MultiFilter[Availability] = MultiFilter(apiValues)
    val domainFilter: AvailabilityFilter = AvailabilityFilter(domainValues)

    expect.same(multiFilter.intoPartial[AvailabilityFilter].transform.asEither, Right(domainFilter)) and
    expect.same(domainFilter.into[MultiFilter[Availability]].transform, multiFilter)
  }

  test("MultiFilter[Availability] should not be empty when transforming to AvailabilityFilter") {
    val emptyMultiFilter: MultiFilter[Availability] = MultiFilter(Seq.empty)

    emptyMultiFilter.intoPartial[AvailabilityFilter].transform.asEither match {
      case Right(_) => failure("Expected an exception for empty MultiFilter")
      case Left(e)  => expect(clue(e.errors.toList) == List(partial.Error(EmptyValue)))
    }
  }

  test("Unknown values for availability are ignored") {
    val apiValues: List[Availability] = List(Availability("In overleg"), Availability("unknown value"))
    val domainValues: NonEmptyList[nl.dpes.profilesearch.domain.Availability] =
      NonEmptyList.fromListUnsafe(List(DomainAvailability.InOverleg))

    val multiFilter: MultiFilter[Availability] = MultiFilter(apiValues)
    val domainFilter: AvailabilityFilter = AvailabilityFilter(domainValues)

    expect.same(multiFilter.intoPartial[AvailabilityFilter].transform.asEither, Right(domainFilter))
  }

  test("MultiFilter[Language] should be able transform to and from LanguageFilter") {
    val apiValues: List[Language] = List(Language("Nederlands"), Language("Engels"))
    val domainValues: NonEmptyList[nl.dpes.profilesearch.domain.Language] =
      NonEmptyList.fromListUnsafe(List(DomainLanguage("Nederlands"), DomainLanguage("Engels")))

    val multiFilter: MultiFilter[Language] = MultiFilter(apiValues)
    val domainFilter: LanguageFilter = LanguageFilter(domainValues)

    expect.same(multiFilter.intoPartial[LanguageFilter].transform.asEither, Right(domainFilter)) and
    expect.same(domainFilter.into[MultiFilter[Language]].transform, multiFilter)
  }

  test("MultiFilter[Language] should not be empty when transforming to LanguageFilter") {
    val emptyMultiFilter: MultiFilter[Language] = MultiFilter(Seq.empty)

    emptyMultiFilter.intoPartial[LanguageFilter].transform.asEither match {
      case Right(_) => failure("Expected an exception for empty MultiFilter")
      case Left(e)  => expect(clue(e.errors.toList) == List(partial.Error(EmptyValue)))
    }
  }
}
