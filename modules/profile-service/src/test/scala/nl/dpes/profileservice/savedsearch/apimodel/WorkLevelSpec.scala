package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.WorkLevel
import weaver.FunSuite

object WorkLevelSpec extends FunSuite {

  test("WorkLevel should be json encoded as a string") {
    val workLevel = WorkLevel("Amsterdam")
    val json = workLevel.asJson

    expect(json == "Amsterdam".asJson)
  }

  test("WorkLevel should be encoded and decoded correctly") {
    val workLevel = WorkLevel("Amsterdam")

    val roundTrippedWorkLevel = decode[WorkLevel](workLevel.asJson.noSpaces)

    expect(roundTrippedWorkLevel == Right(workLevel))
  }
}
