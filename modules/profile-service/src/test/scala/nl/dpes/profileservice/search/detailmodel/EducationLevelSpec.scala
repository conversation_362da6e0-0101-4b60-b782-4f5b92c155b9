package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object EducationLevelSpec extends FunSuite {
  test("EducationLevel should be json encoded as a string") {
    val educationLevel = EducationLevel("Bachelor")
    val json = educationLevel.asJson

    expect(json == "Bachelor".asJson)
  }

  test("EducationLevel should be encoded and decoded correctly") {
    val educationLevel = EducationLevel("Bachelor")

    val roundTrippedEducationLevel = decode[EducationLevel](educationLevel.asJson.noSpaces)

    expect(roundTrippedEducationLevel == Right(educationLevel))
  }

  test("EducationLevel should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[EducationLevel]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("EducationLevel should transform from detailpage.EducationLevel") {
    val detailPageEducationLevel = detailpage.EducationLevel("Bachelor")
    val educationLevel = EducationLevel(detailPageEducationLevel.value)

    val transformedEducationLevel = detailPageEducationLevel.into[EducationLevel].transform

    expect(transformedEducationLevel == educationLevel)
  }
}
