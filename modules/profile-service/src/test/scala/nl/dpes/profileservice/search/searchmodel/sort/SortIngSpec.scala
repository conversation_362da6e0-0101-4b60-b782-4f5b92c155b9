package nl.dpes.profileservice
package search.searchmodel.sort

import cats.effect.IO
import weaver.SimpleIOSuite

object SortIngSpec extends SimpleIOSuite {
  test("When no sorting is provided 'relevance' is used as default") {
    IO(expect(Sorting(none, none) == Sorting(SortField.relevance, SortOrder.desc)))
  }

  test("When only sort order is provided, but no sort field then 'relevance' is used as default, but the order is not changed") {
    IO(expect(Sorting(none, SortOrder.asc.some) == Sorting(SortField.relevance, SortOrder.asc)))
  }

  test("When only sort field is provided, but no sort order then the default for the order is used, but the field is not changed") {
    IO(expect(Sorting(SortField.updatedDate.some, none) == Sorting(SortField.updatedDate, SortOrder.desc)))
  }

  test("When all data is provided it will be used") {
    IO(expect(Sorting(SortField.updatedDate.some, SortOrder.asc.some) == Sorting(SortField.updatedDate, SortOrder.asc)))
  }
}
