package nl.dpes.profileservice.favorites.apimodel

import io.scalaland.chimney.syntax.transformInto
import nl.dpes.recruiterfavorites.service.domainmodel.ProfileId as DomainProfileId
import weaver.FunSuite

object ProfileIdSpec extends FunSuite {

  test("It should create a ProfileId") {
    expect(ProfileId("  550e8400-e29b-41d4-a716-446655440000  ") == ProfileId("550e8400-e29b-41d4-a716-446655440000"))
  }

  test("It should check if a ProfileId is empty") {
    expect(ProfileId("") == Left("ProfileId is a 36-character identifier. You have provided '0' characters."))
  }

  test("It should check if a ProfileId contains only whitespaces") {
    expect(ProfileId("    ") == Left("ProfileId is a 36-character identifier. You have provided '0' characters."))
  }

  test("It should check if a ProfileId has an invalid number of characters") {
    expect(
      ProfileId("123456789123456789123456789123456789123456789") == Left(
        "ProfileId is a 36-character identifier. You have provided '45' characters."
      )
    )
  }

  test("It can be transformed into the domains ProfileId") {
    ProfileId("550e8400-e29b-41d4-a716-446655440000") match {
      case Left(value) => failure(s"Expected a valid ProfileId, but got an error: $value")
      case Right(profileId) =>
        val domainProfileId = profileId.transformInto[DomainProfileId]
        expect(domainProfileId.value == "550e8400-e29b-41d4-a716-446655440000")
    }
  }

  test("It can be transformed from the domains ProfileId") {
    val domainProfileId = DomainProfileId("550e8400-e29b-41d4-a716-446655440000")
    val profileId = domainProfileId.transformInto[ProfileId]
    expect(profileId.value == "550e8400-e29b-41d4-a716-446655440000")
  }
}
