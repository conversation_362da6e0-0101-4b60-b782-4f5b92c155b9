package nl.dpes.profileservice
package search

import cats.effect.IO
import nl.dpes.profilesearch.domain.CommuteDistance.Distance
import nl.dpes.profileservice.tapir.ErrorMessage.*
import nl.dpes.profilesearch.domain.filter.{Filter, GeoDistanceFilter}
import nl.dpes.profilesearch.service.*
import nl.dpes.profilesearch.service.model.dbmodel.FacetsResult.TermBucket
import nl.dpes.profileservice.search.detailmodel.Profile
import nl.dpes.profileservice.search.searchmodel.*
import nl.dpes.profileservice.search.searchmodel.filter.AggregationKeyName
import nl.dpes.profileservice.search.searchmodel.pagination.*
import nl.dpes.profileservice.search.searchmodel.sort.*
import nl.dpes.profileservice.search.suggestions.CityPrefix
import nl.dpes.profileservice.tapir.ErrorMessage
import org.scalamock.stubs.Stubs
import sttp.client3.*
import sttp.client3.testing.SttpBackendStub
import sttp.model.StatusCode
import sttp.tapir.integ.cats.effect.CatsMonadError
import sttp.tapir.server.stub.TapirStubInterpreter
import weaver.SimpleIOSuite

object ProfileSearchControllerSpec extends SimpleIOSuite with Stubs {

  val unit: Unit = ()

  val index = "cv_database"
  val profiles: List[ProfileSearchResult] = List(
    ProfileSearchResult(
      id = ProfileId.unsafeApply("56cda6ad-e5b1-4cf1-a83a-0b340eabdcdb"),
      name = Some(Name(FirstName("John"), LastName("Doe"))),
      updatedDate = UpdatedDate(1288566000),
      workingHours = Some(WorkingHours(36, 40)),
      workLevels = List(WorkLevel("HBO")),
      experiences = List(Experience("Recruiter"), Experience("UX Designer"), Experience("English teacher")),
      preferredJobs = List(PreferredJob("management assistant")),
      photo = Some(Photo("profile-photos/7cd63c42-1234-1234-1234-01fb6a415123/152dea87-1234-1234-1234-dfb501ab1234.jpg")),
      city = Some(City("Amsterdam")),
      isFavorite = false,
      lastViewedDate = None
    )
  )

  val aggregations: Map[AggregationKeyName, List[TermBucket]] = Map(
    AggregationKeyName("provinces")         -> List(TermBucket("North-Holland", 1), TermBucket("Zeeland", 1)),
    AggregationKeyName("updatedDate")       -> List(TermBucket("Afgelopen jaar", 1), TermBucket("Afgelopen 6 maanden", 1)),
    AggregationKeyName("functionGroups")    -> List(TermBucket("Commercieel/Verkoop", 1), TermBucket("Directie/Management algemeen", 1)),
    AggregationKeyName("workLevels")        -> List(TermBucket("HBO", 1), TermBucket("MBO", 1)),
    AggregationKeyName("workingHours")      -> List(TermBucket("16 tot 24 uur", 1), TermBucket("24 tot 32 uur", 1)),
    AggregationKeyName("careerLevels")      -> List(TermBucket("Directie", 1), TermBucket("Ervaren", 1)),
    AggregationKeyName("requestedSalaries") -> List(TermBucket("1.750 - 2.500", 1), TermBucket("3.500 - 5.000", 1)),
    AggregationKeyName("availabilities")    -> List(TermBucket("Per direct", 1), TermBucket("In overleg", 1)),
    AggregationKeyName("driverLicenses")    -> List(TermBucket("A - motor", 1), TermBucket("B - personenauto", 1)),
    AggregationKeyName("languages")         -> List(TermBucket("Arabisch", 1), TermBucket("Nederlands", 1))
  )

  val searchResult: SearchResult = SearchResult(profiles, aggregations)
  val cities: List[suggestions.City] = List(suggestions.City("Amsterdam"), suggestions.City("Amstelveen"), suggestions.City("Amstelfort"))

  def profileSearchService(
    getProfilesFn: => IO[Either[BadRequest, SearchResult]] = IO(Right(searchResult)),
    getFavoritesFn: => IO[Either[UnknownError, SearchResult]] = IO(Right(searchResult.copy(aggregations = Map.empty))),
    getCitySuggestionsFn: => IO[Either[UnknownError, List[suggestions.City]]] = IO(Right(cities))
  ): ProfileSearchControllerService[IO] =
    new ProfileSearchControllerService[IO] {

      override def getProfile(
        recruiterId: RecruiterId,
        profileId: ProfileId
      ): IO[Either[ErrorMessage, Profile]] = // IO(profiles.head.asRight)
        IO(NotFound(s"$profileId for $recruiterId").asLeft[Profile])

      override def getCitySuggestions(cityPrefix: CityPrefix): IO[Either[UnknownError, List[suggestions.City]]] = getCitySuggestionsFn

      override def getProfiles(
        pagination: Pagination,
        filters: List[Filter],
        sortField: Option[SortField],
        sortOrder: Option[SortOrder],
        withAggregations: Boolean,
        recruiterId: Option[RecruiterId]
      ): IO[Either[BadRequest, SearchResult]] =
        getProfilesFn

      override def getProfilesById(ids: List[ProfileId], recruiterId: Option[RecruiterId]): IO[Either[UnknownError, SearchResult]] =
        getFavoritesFn
    }

  test("It should be able to return profiles") {
    for {
      controller     <- IO(ProfileSearchController.impl[IO](profileSearchService()))
      serverEndpoint <- controller.getProfiles
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <-
        basicRequest
          .get(uri"http://localhost:14200/profiles?pageNumber=1&pageSize=100&query=Recruiter&provinces=Zeeland,Noord-Holland")
          .send(backendStub)

    } yield {
      val result = response
      expect(
        result.code == StatusCode.Ok &&
          (result.body match {
            case Right(content) =>
              content.contains("\"profiles\":[{\"id\":\"56cda6ad-e5b1-4cf1-a83a-0b340eabdcdb\"") &&
              content.contains("\"provinces\":[{\"key\":\"North-Holland\",\"doc_count\":1},{\"key\":\"Zeeland\",\"doc_count\":1}]") &&
              content.contains(
                "\"updatedDate\":[{\"key\":\"Afgelopen jaar\",\"doc_count\":1},{\"key\":\"Afgelopen 6 maanden\",\"doc_count\":1}]"
              ) &&
              content.contains(
                "\"functionGroups\":[{\"key\":\"Commercieel/Verkoop\",\"doc_count\":1},{\"key\":\"Directie/Management algemeen\",\"doc_count\":1}]"
              ) &&
              content.contains("\"workLevels\":[{\"key\":\"HBO\",\"doc_count\":1},{\"key\":\"MBO\",\"doc_count\":1}]") &&
              content.contains(
                "\"workingHours\":[{\"key\":\"16 tot 24 uur\",\"doc_count\":1},{\"key\":\"24 tot 32 uur\",\"doc_count\":1}]"
              ) &&
              content.contains("\"careerLevels\":[{\"key\":\"Directie\",\"doc_count\":1},{\"key\":\"Ervaren\",\"doc_count\":1}]") &&
              content.contains(
                "\"requestedSalaries\":[{\"key\":\"1.750 - 2.500\",\"doc_count\":1},{\"key\":\"3.500 - 5.000\",\"doc_count\":1}]"
              ) &&
              content.contains("\"availabilities\":[{\"key\":\"Per direct\",\"doc_count\":1},{\"key\":\"In overleg\",\"doc_count\":1}]") &&
              content.contains(
                "\"driverLicenses\":[{\"key\":\"A - motor\",\"doc_count\":1},{\"key\":\"B - personenauto\",\"doc_count\":1}]"
              ) &&
              content.contains(
                "\"languages\":[{\"key\":\"Arabisch\",\"doc_count\":1},{\"key\":\"Nederlands\",\"doc_count\":1}]"
              )
            case Left(error) =>
              failure("This test should successful")
              false
          })
      )
    }
  }

  test("It should return Not_Found when invoking an unknown endpoint") {
    for {
      controller     <- IO(ProfileSearchController.impl[IO](profileSearchService()))
      serverEndpoint <- controller.getProfiles
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/unknown")
        .send(backendStub)
    } yield expect(response.code == StatusCode.NotFound)
  }

  test("It should return Bad Request error when the pagination parameters are invalid") {
    for {
      controller     <- IO(ProfileSearchController.impl[IO](profileSearchService(getProfilesFn = ???)))
      serverEndpoint <- controller.getProfiles
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/profiles?pageNumber=1&pageSize=-5")
        .send(backendStub)
    } yield expect(response.code == StatusCode.BadRequest)
  }

  test("It should return Internal Server Error when the service returns an Unknown error") {
    for {
      controller <- IO(
        ProfileSearchController
          .impl[IO](profileSearchService(getProfilesFn = IO.raiseError(new Throwable("Error occurred"))))
      )
      serverEndpoint <- controller.getProfiles
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/profiles?pageNumber=1&pageSize=100")
        .send(backendStub)
    } yield expect(response.code == StatusCode.InternalServerError)
  }

  test("It should return Internal Server Error when the service encounters an issue") {
    for {
      controller <- IO(
        ProfileSearchController.impl[IO](profileSearchService(getProfilesFn = IO.raiseError(new Throwable("Error occurred"))))
      )
      serverEndpoint <- controller.getProfiles
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/profiles?pageNumber=1&pageSize=100")
        .send(backendStub)
    } yield expect(response.code == StatusCode.InternalServerError)
  }

  test("It should return not found when no profile is found") {
    for {
      controller <- IO(
        ProfileSearchController.impl[IO](profileSearchService(getProfilesFn = IO.raiseError(new Throwable("Error occurred"))))
      )
      serverEndpoint <- controller.getProfile
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/profile/123456789123456789123456789123456789")
        .header("X-Recruiter-ID", "123456789123456789")
        .send(backendStub)
    } yield {
      val body: Either[String, String] = response.body
      expect(
        body == """{"message":"ProfileId(123456789123456789123456789123456789) for RecruiterId(123456789123456789)"}""".asLeft[String]
      ) and expect(response.code == StatusCode.NotFound)
    }
  }

  test("It should be able to return a list of autocompleted city name suggestions") {
    for {
      controller     <- IO(ProfileSearchController.impl[IO](profileSearchService()))
      serverEndpoint <- controller.getCitySuggestions
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/profiles/autocomplete/cities?cityPrefix=Amst")
        .send(backendStub)
    } yield expect(
      response.code == StatusCode.Ok && response.body == Right("[\"Amsterdam\",\"Amstelveen\",\"Amstelfort\"]")
    )
  }

  test("It should be able to return a list of favorite profiles") {
    for {
      controller     <- IO(ProfileSearchController.impl[IO](profileSearchService()))
      serverEndpoint <- controller.getFavorites
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .post(uri"http://localhost:14200/profiles")
        .header("X-Recruiter-ID", "123456789123456789")
        .body("""{"ids":["123456789123456789123456789123456789","923456789123456789123456789123456781"]}""")
        .send(backendStub)
    } yield expect(response.code == StatusCode.Ok) and expect(
      response.body == Right(
        """{"profiles":[{"id":"56cda6ad-e5b1-4cf1-a83a-0b340eabdcdb","name":{"firstName":"John","lastName":"Doe"},"updatedDate":1288566000,"workingHours":{"min":36,"max":40},"workLevels":["HBO"],"experiences":["Recruiter","UX Designer","English teacher"],"preferredJobs":["management assistant"],"photo":"profile-photos/7cd63c42-1234-1234-1234-01fb6a415123/152dea87-1234-1234-1234-dfb501ab1234.jpg","city":"Amsterdam","isFavorite":false}],"aggregations":{},"totalNumberOfProfiles":0}"""
      )
    )
  }

  test("It should be able to search profiles by a geo position") {
    val profileSearchService = stub[ProfileSearchControllerService[IO]]

    profileSearchService.getProfiles
      .returns(params => IO.pure(Right(nl.dpes.profileservice.search.searchmodel.SearchResult(List.empty, Map.empty))))

    for {
      controller     <- IO(ProfileSearchController.impl[IO](profileSearchService))
      serverEndpoint <- controller.getProfiles
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/profiles?geoDistance=52.23,13.44")
        .send(backendStub)
      calls = profileSearchService.getProfiles.calls
      filters = calls.headOption.map(_._2)
    } yield expect(response.code == StatusCode.Ok)
      && expect(filters.exists(_.contains(GeoDistanceFilter(52.23, 13.44, None))), filters.toString)
  }

  test("It should filter profiles by distance when both a geo position and a commute distance are provided") {
    val profileSearchService = stub[ProfileSearchControllerService[IO]]

    profileSearchService.getProfiles
      .returns(params => IO.pure(Right(nl.dpes.profileservice.search.searchmodel.SearchResult(List.empty, Map.empty))))

    for {
      controller     <- IO(ProfileSearchController.impl[IO](profileSearchService))
      serverEndpoint <- controller.getProfiles
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/profiles?geoDistance=52.23,13.44&commuteDistance=30")
        .send(backendStub)
      calls = profileSearchService.getProfiles.calls
      filters = calls.headOption.map(_._2)
    } yield expect(response.code == StatusCode.Ok)
      && expect(filters.exists(_.contains(GeoDistanceFilter(52.23, 13.44, Some(Distance(30))))), filters.toString)
  }

  test("It should not filter profiles by distance when both a geo position and a commute distance 'Heel Nederland' provided") {
    val profileSearchService = stub[ProfileSearchControllerService[IO]]

    profileSearchService.getProfiles
      .returns(params => IO.pure(Right(nl.dpes.profileservice.search.searchmodel.SearchResult(List.empty, Map.empty))))

    for {
      controller     <- IO(ProfileSearchController.impl[IO](profileSearchService))
      serverEndpoint <- controller.getProfiles
      backendStub <- IO(
        TapirStubInterpreter(SttpBackendStub[IO, Any](CatsMonadError()))
          .whenServerEndpoint(serverEndpoint)
          .thenRunLogic()
          .backend()
      )
      response <- basicRequest
        .get(uri"http://localhost:14200/profiles?geoDistance=52.23,13.44&commuteDistance=Heel Nederland")
        .send(backendStub)
      calls = profileSearchService.getProfiles.calls
      filters = calls.headOption.map(_._2)
    } yield expect(response.code == StatusCode.Ok)
      && expect(filters.exists(_.contains(GeoDistanceFilter(52.23, 13.44, None))), filters.toString)
  }
}
