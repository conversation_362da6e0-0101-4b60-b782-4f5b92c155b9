package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.SavedSearchId
import weaver.FunSuite

object SavedSearchIdSpec extends FunSuite {

  test("SavedSearchId should be json encoded as a string") {
    val savedSearchId = SavedSearchId("123456789123456789")
    val json = savedSearchId.asJson

    expect(json == "123456789123456789".asJson)
  }

  test("SavedSearchId should be encoded and decoded correctly") {
    val savedSearchId = SavedSearchId("123456789123456789")

    val roundTrippedSavedSearchId = decode[SavedSearchId](savedSearchId.asJson.noSpaces)

    expect(roundTrippedSavedSearchId == Right(savedSearchId))
  }
}
