package nl.dpes.profileservice.search.searchmodel.filter

import weaver.FunSuite

object CommuteDistanceSpec extends FunSuite {
  test("A distance can be 'Heel Nederland'") {
    CommuteDistance("Heel Nederland") match {
      case Left(error)     => failure("Expected Right, got Left: " + error.getMessage)
      case Right(distance) => expect(distance == CommuteDistance.WholeCountry)
    }
  }

  test("A distance can be a positive integer string") {
    CommuteDistance("30") match {
      case Left(error)                               => failure("Expected Right, got Left: " + error.getMessage)
      case Right(CommuteDistance.Distance(distance)) => expect(distance == 30)
      case Right(value)                              => failure("Expected Distance, got: " + value)
    }
  }

  test("A distance cannot be a negative integer string") {
    CommuteDistance("-10") match {
      case Left(CommuteDistance.NegativeDistance(value)) => expect(value == -10)
      case Left(error)                                   => failure("Expected NegativeDistance error, got: " + error.getMessage)
      case Right(distance)                               => failure("Expected Left, got Right: " + distance)
    }
  }

  test("A distance cannot have an unknown value") {
    CommuteDistance("invalid") match {
      case Left(CommuteDistance.NotCommuteDistance(value)) => expect(value == "invalid")
      case Left(error)                                     => failure("Expected NotCommuteDistance error, got: " + error.getMessage)
      case Right(distance)                                 => failure("Expected Left, got Right: " + distance)
    }
  }
}
