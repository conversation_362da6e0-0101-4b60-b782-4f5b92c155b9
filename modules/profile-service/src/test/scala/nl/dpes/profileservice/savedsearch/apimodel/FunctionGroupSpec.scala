package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.FunctionGroup
import weaver.FunSuite

object FunctionGroupSpec extends FunSuite {

  test("FunctionGroup should be json encoded as a string") {
    val functionGroup = FunctionGroup("group")
    val json = functionGroup.asJson

    expect(json == "group".asJson)
  }

  test("FunctionGroup should be encoded and decoded correctly") {
    val functionGroup = FunctionGroup("group")

    val roundTrippedFunctionGroup = decode[FunctionGroup](functionGroup.asJson.noSpaces)

    expect(roundTrippedFunctionGroup == Right(functionGroup))
  }
}
