package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.City
import weaver.FunSuite

object CitySpec extends FunSuite {

  test("City should be json encoded as a string") {
    val city = City("Amsterdam")
    val json = city.asJson

    expect(json == "Amsterdam".asJson)
  }

  test("City should be encoded and decoded correctly") {
    val city = City("Amsterdam")

    val roundTrippedCity = decode[City](city.asJson.noSpaces)

    expect(roundTrippedCity == Right(city))
  }
}
