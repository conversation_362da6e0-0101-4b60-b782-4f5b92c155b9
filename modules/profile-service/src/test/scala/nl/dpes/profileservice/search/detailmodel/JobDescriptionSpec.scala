package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object JobDescriptionSpec extends FunSuite {
  test("JobDescription should be json encoded as a string") {
    val jobDescription = JobDescription("Software Engineer")
    val json = jobDescription.asJson

    expect(json == "Software Engineer".asJson)
  }

  test("JobDescription should be encoded and decoded correctly") {
    val jobDescription = JobDescription("Software Engineer")

    val roundTrippedJobDescription = decode[JobDescription](jobDescription.asJson.noSpaces)

    expect(roundTrippedJobDescription == Right(jobDescription))
  }

  test("JobDescription should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[JobDescription]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("JobDescription should transform from detailpage.JobDescription") {
    val detailPageJobDescription = detailpage.JobDescription("Software Engineer")
    val jobDescription = JobDescription(detailPageJobDescription.value)

    val transformedJobDescription = detailPageJobDescription.into[JobDescription].transform

    expect(transformedJobDescription == jobDescription)
  }
}
