package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object YearSpec extends FunSuite {
  test("Year should be json encoded as an integer") {
    val year = Year(2023)
    val json = year.asJson

    expect(json == 2023.asJson)
  }

  test("Year should be encoded and decoded correctly") {
    val year = Year(2023)

    val roundTrippedYear = decode[Year](year.asJson.noSpaces)

    expect(roundTrippedYear == Right(year))
  }

  test("Year should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[Year]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[Int]])
  }

  test("Year should transform from detailpage.Year") {
    val detailPageYear = detailpage.Year(2023)
    val year = Year(detailPageYear.value)

    val transformedYear = detailPageYear.into[Year].transform

    expect(transformedYear == year)
  }
}
