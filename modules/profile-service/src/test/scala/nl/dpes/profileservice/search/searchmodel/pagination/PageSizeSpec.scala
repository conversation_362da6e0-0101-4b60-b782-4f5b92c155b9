package nl.dpes.profileservice.search.searchmodel.pagination

import weaver.FunSuite

object PageSizeSpec extends FunSuite {
  test("PageSize should be created with a positive value") {
    PageSize(1) match {
      case Left(error)               => failure(s"Construction should succeed, got '$error'")
      case Right(pageSize: PageSize) => expect(pageSize.value == 1)
    }
  }

  test("PageSize should not be created with a non-positive value") {
    PageSize(0) match {
      case Left(error)               => expect(error == "Page size (0) must be positive")
      case Right(pageSize: PageSize) => failure(s"Construction should fail, got '$pageSize'")
    }
  }

  test("PageSize should not be created with a negative value") {
    PageSize(-1) match {
      case Left(error)               => expect(error == "Page size (-1) must be positive")
      case Right(pageSize: PageSize) => failure(s"Construction should fail, got '$pageSize'")
    }
  }
}
