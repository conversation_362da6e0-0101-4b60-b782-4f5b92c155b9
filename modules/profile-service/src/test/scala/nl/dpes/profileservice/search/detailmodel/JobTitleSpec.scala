package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object JobTitleSpec extends FunSuite {
  test("JobTitle should be json encoded as a string") {
    val jobTitle = JobTitle("Software Engineer")
    val json = jobTitle.asJson

    expect(json == "Software Engineer".asJson)
  }

  test("JobTitle should be encoded and decoded correctly") {
    val jobTitle = JobTitle("Software Engineer")

    val roundTrippedJobTitle = decode[JobTitle](jobTitle.asJson.noSpaces)

    expect(roundTrippedJobTitle == Right(jobTitle))
  }

  test("JobTitle should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[JobTitle]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("JobTitle should transform from detailpage.JobTitle") {
    val detailPageJobTitle = detailpage.JobTitle("Software Engineer")
    val jobTitle = JobTitle(detailPageJobTitle.value)

    val transformedJobTitle = detailPageJobTitle.into[JobTitle].transform

    expect(transformedJobTitle == jobTitle)
  }
}
