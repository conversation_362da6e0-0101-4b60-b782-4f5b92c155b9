package nl.dpes.profileservice.search.searchmodel.pagination

import weaver.FunSuite

object PageNumberSpec extends FunSuite {
  test("PageNumber should be created with a positive value") {
    PageNumber(1) match {
      case Left(error)                   => failure(s"Construction should succeed, got '$error'")
      case Right(pageNumber: PageNumber) => expect(pageNumber.value == 1)
    }
  }

  test("PageNumber should not be created with a non-positive value") {
    PageNumber(0) match {
      case Left(error)                   => expect(error == "Page number (0) must be positive")
      case Right(pageNumber: PageNumber) => failure(s"Construction should fail, got '$pageNumber'")
    }
  }

  test("PageNumber should not be created with a negative value") {
    PageNumber(-1) match {
      case Left(error)                   => expect(error == "Page number (-1) must be positive")
      case Right(pageNumber: PageNumber) => failure(s"Construction should fail, got '$pageNumber'")
    }
  }
}
