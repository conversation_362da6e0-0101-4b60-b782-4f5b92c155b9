package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.Longitude
import weaver.FunSuite

object LongitudeSpec extends FunSuite {

  test("Longitude should be json encoded as a string") {
    val longitude = Longitude("30.30")
    val json = longitude.asJson

    expect(json == "30.30".asJson)
  }

  test("Longitude should be encoded and decoded correctly") {
    val longitude = Longitude("30.30")

    val roundTrippedLongitude = decode[Longitude](longitude.asJson.noSpaces)

    expect(roundTrippedLongitude == Right(longitude))
  }
}
