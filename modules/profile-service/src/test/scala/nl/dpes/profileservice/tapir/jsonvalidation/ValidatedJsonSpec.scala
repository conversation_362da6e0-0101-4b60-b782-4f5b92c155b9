package nl.dpes.profileservice
package tapir.jsonvalidation

import cats.effect.IO
import io.circe
import io.circe.*
import io.circe.parser.parse
import nl.dpes.profileservice.tapir.ErrorMessage.BadRequest
import nl.dpes.profileservice.tapir.jsonvalidation.ValidatedJson.*
import nl.dpes.profileservice.tapir.jsonvalidation.datastructure.Foo
import nl.dpes.profileservice.tracing.HttpServerOptions
import org.http4s.HttpRoutes
import org.http4s.client.Client
import org.typelevel.log4cats.SelfAwareStructuredLogger
import org.typelevel.log4cats.slf4j.Slf4jLogger
import sttp.model.StatusCode
import sttp.tapir
import sttp.tapir.*
import sttp.tapir.json.circe.*
import sttp.tapir.server.ServerEndpoint.Full
import sttp.tapir.server.http4s.Http4sServerInterpreter
import weaver.{Expectations, SimpleIOSuite}

object ValidatedJsonSpec extends SimpleIOSuite {
  case class MessageResponse(message: String) derives Encoder, Decoder, Schema

  val logger: SelfAwareStructuredLogger[IO] = Slf4jLogger.getLogger[IO]

  val fooLogic: Foo => IO[Either[BadRequest, Foo]] =
    foo => IO.pure(foo.asRight)

  val validatedJsonEndpoint: Full[Unit, Unit, Foo, BadRequest, Foo, Any, IO] =
    endpoint.post
      .in("foo")
      .in(validatedJsonBody[Foo])
      .errorOut(statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]))
      .out(statusCode(StatusCode.Ok).and(jsonBody[Foo]))
      .out(statusCode(StatusCode.Ok))
      .serverLogic(fooLogic)

  val validatedQueryParamEndpoint: Full[Unit, Unit, String, BadRequest, MessageResponse, Any, IO] =
    endpoint.get
      .in("bar")
      .in(query[String]("param").validate(Validator.fixedLength(5)))
      .errorOut(statusCode(StatusCode.BadRequest).and(jsonBody[BadRequest]))
      .out(statusCode(StatusCode.Ok).and(jsonBody[MessageResponse]))
      .serverLogic(param => IO.pure(MessageResponse(param).asRight[BadRequest]))

  // --- interpreter with serveroptions that should handle the error ---
  val routes: HttpRoutes[IO] =
    Http4sServerInterpreter[IO](HttpServerOptions.serverOptions(logger))
      .toRoutes(validatedJsonEndpoint :: validatedQueryParamEndpoint :: Nil)

  val client: Client[IO] = Client.fromHttpApp(routes.orNotFound)

  // --- helper: compare JSON strings structurally ---
  def expectJsonEquals(actual: String, expected: String): Expectations = {
    val parsedActual = parse(actual).fold(throw _, identity)
    val parsedExpected = parse(expected).fold(throw _, identity)
    expect(clue(parsedActual) == clue(parsedExpected))
  }

  val inValidJson =
    """
      |{
      |  "singleValue": "not-baz",
      |  "multiValue": [
      |    "baz",
      |    "also-not-baz",
      |    "baz",
      |    "definitely-not-baz"
      |  ],
      |  "singleObject": {
      |    "singleValue": "not-baz",
      |    "multiValue": [
      |      "baz",
      |      "also-not-baz",
      |      "baz",
      |      "definitely-not-baz"
      |    ]
      |  },
      |  "multiObject": [
      |    {
      |      "singleValue": "not-baz",
      |      "multiValue": [
      |        "baz",
      |        "also-not-baz",
      |        "baz",
      |        "definitely-not-baz"
      |      ]
      |    },
      |    {
      |      "singleValue": "not-baz",
      |      "multiValue": [
      |        "baz",
      |        "also-not-baz",
      |        "baz",
      |        "definitely-not-baz"
      |      ]
      |    }
      |  ]
      |}
      |""".stripMargin

  val validJson =
    """{
      |  "singleValue": "baz",
      |  "multiValue": [
      |    "baz",
      |    "baz"
      |  ],
      |  "singleObject": {
      |    "singleValue": "baz",
      |    "multiValue": [
      |      "baz",
      |      "baz"
      |    ]
      |  },
      |  "multiObject": [
      |    {
      |      "singleValue": "baz",
      |      "multiValue": [
      |        "baz",
      |        "baz"
      |      ]
      |    },
      |    {
      |      "singleValue": "baz",
      |      "multiValue": [
      |        "baz",
      |        "baz"
      |      ]
      |    }
      |  ]
      |}""".stripMargin

  val errorJson =
    s"""{
       |  "errors": [
       |    {
       |      "parameterType": "body",
       |      "inputName": ".singleValue",
       |      "message": "Bar can only be 'baz', got 'not-baz'"
       |    },
       |    {
       |      "parameterType": "body",
       |      "inputName": ".multiValue[1]",
       |      "message": "Bar can only be 'baz', got 'also-not-baz'"
       |    },
       |    {
       |      "parameterType": "body",
       |      "inputName": ".multiValue[3]",
       |      "message": "Bar can only be 'baz', got 'definitely-not-baz'"
       |    },
       |    {
       |      "parameterType": "body",
       |      "inputName": ".singleObject.singleValue",
       |      "message": "Bar can only be 'baz', got 'not-baz'"
       |    },
       |    {
       |      "parameterType": "body",
       |      "inputName": ".singleObject.multiValue[1]",
       |      "message": "Bar can only be 'baz', got 'also-not-baz'"
       |    },
       |    {
       |      "parameterType": "body",
       |      "inputName": ".singleObject.multiValue[3]",
       |      "message": "Bar can only be 'baz', got 'definitely-not-baz'"
       |    },
       |    {
       |      "parameterType": "body",
       |      "inputName": ".multiObject[0].singleValue",
       |      "message": "Bar can only be 'baz', got 'not-baz'"
       |    },
       |    {
       |      "parameterType": "body",
       |      "inputName": ".multiObject[0].multiValue[1]",
       |      "message": "Bar can only be 'baz', got 'also-not-baz'"
       |    },
       |    {
       |      "parameterType": "body",
       |      "inputName": ".multiObject[0].multiValue[3]",
       |      "message": "Bar can only be 'baz', got 'definitely-not-baz'"
       |    },
       |    {
       |      "parameterType": "body",
       |      "inputName": ".multiObject[1].singleValue",
       |      "message": "Bar can only be 'baz', got 'not-baz'"
       |    },
       |    {
       |      "parameterType": "body",
       |      "inputName": ".multiObject[1].multiValue[1]",
       |      "message": "Bar can only be 'baz', got 'also-not-baz'"
       |    },
       |    {
       |      "parameterType": "body",
       |      "inputName": ".multiObject[1].multiValue[3]",
       |      "message": "Bar can only be 'baz', got 'definitely-not-baz'"
       |    }
       |  ]
       |}""".stripMargin

  test("An object with multiple errors should accumulate all errors") {
    val request = org.http4s
      .Request[IO](org.http4s.Method.POST, org.http4s.Uri.unsafeFromString(s"/foo"))
      .withEntity(inValidJson)
      .putHeaders(org.http4s.headers.`Content-Type`(org.http4s.MediaType.application.json))

    client.run(request).use { response =>
      response.as[String].map { body =>
        expectJsonEquals(body, errorJson) and expect(clue(response.status) == org.http4s.Status.BadRequest)
      }
    }
  }

  test("An object without errors should return a result") {
    val request = org.http4s
      .Request[IO](org.http4s.Method.POST, org.http4s.Uri.unsafeFromString(s"/foo"))
      .withEntity(validJson)
      .putHeaders(org.http4s.headers.`Content-Type`(org.http4s.MediaType.application.json))

    client.run(request).use { response =>
      response.as[String].map { body =>
        expectJsonEquals(body, validJson) and expect(clue(response.status) == org.http4s.Status.Ok)
      }
    }
  }

  test("Validated non body json should continue to work as expected") {
    val request = org.http4s
      .Request[IO](org.http4s.Method.GET, org.http4s.Uri.unsafeFromString(s"/bar?param=too-long"))
      .putHeaders(org.http4s.headers.`Content-Type`(org.http4s.MediaType.application.json))

    val expectedErrorJson =
      s"""{
         |  "errors": [
         |    {
         |      "parameterType": "queryparameter",
         |      "inputName": "param",
         |      "message": "expected param to have length less than or equal to 5, but got: \\"too-long\\""
         |    }
         |  ]
         |}""".stripMargin

    client.run(request).use { response =>
      response.as[String].map { body =>
        expectJsonEquals(body, expectedErrorJson) and expect(clue(response.status) == org.http4s.Status.BadRequest)
      }
    }
  }
}
