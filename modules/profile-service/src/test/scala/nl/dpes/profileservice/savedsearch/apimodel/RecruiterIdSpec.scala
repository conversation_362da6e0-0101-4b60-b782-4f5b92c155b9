package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.RecruiterId
import weaver.FunSuite

object RecruiterIdSpec extends FunSuite {

  test("RecruiterId should be json encoded as a string") {
    val recruiterId = RecruiterId.unsafeApply("123456789123456789")
    val json = recruiterId.asJson

    expect(json == "123456789123456789".asJson)
  }

  test("RecruiterId should be encoded and decoded correctly") {
    val recruiterId = RecruiterId.unsafeApply("123456789123456789")

    val roundTrippedRecruiterId = decode[RecruiterId](recruiterId.asJson.noSpaces)

    expect(roundTrippedRecruiterId == Right(recruiterId))
  }
}
