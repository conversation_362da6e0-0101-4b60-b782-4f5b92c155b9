package nl.dpes.profileservice.search.searchmodel.sort

import cats.effect.IO
import weaver.SimpleIOSuite

object SortOrderSpec extends SimpleIOSuite {
  test("Invalid orders are not allowed") {
    for {
      sortOrder <- SortOrder("invalid").attempt
    } yield expect(sortOrder == Left(SortOrder.InvalidSortOrder("invalid")))
  }

  test("Valid orders are allowed") {
    for {
      asc  <- SortOrder("asc")
      desc <- SortOrder("desc")
    } yield expect(asc == SortOrder.asc) and expect(desc == SortOrder.desc)
  }
}
