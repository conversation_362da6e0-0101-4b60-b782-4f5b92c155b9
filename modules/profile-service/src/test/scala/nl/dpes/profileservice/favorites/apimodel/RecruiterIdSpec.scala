package nl.dpes.profileservice.favorites.apimodel

import io.scalaland.chimney.syntax.transformInto
import nl.dpes.recruiterfavorites.service.domainmodel.RecruiterId as DomainRecruiterId
import weaver.FunSuite

object RecruiterIdSpec extends FunSuite {

  test("It should create a RecruiterId") {
    expect(RecruiterId("  ABCDEFGHIJ12345678  ") == RecruiterId("ABCDEFGHIJ12345678"))
  }

  test("It should check if a RecruiterId is empty") {
    expect(RecruiterId("") == Left("RecruiterId is a 18-character identifier. You have provided '0' characters."))
  }

  test("It should check if a RecruiterId contains only whitespaces") {
    expect(RecruiterId("    ") == Left("RecruiterId is a 18-character identifier. You have provided '0' characters."))
  }

  test("It should check if a RecruiterId has an invalid number of characters") {
    expect(RecruiterId("123456789123456789123") == Left("RecruiterId is a 18-character identifier. You have provided '21' characters."))
  }

  test("It can be transformed into the domains RecruiterId") {
    RecruiterId("ABCDEFGHIJ12345678") match {
      case Left(value) => failure(s"Expected a valid RecruiterId, but got an error: $value")
      case Right(recruiterId) =>
        val domainRecruiterId = recruiterId.transformInto[DomainRecruiterId]
        expect(domainRecruiterId.value == "ABCDEFGHIJ12345678")
    }
  }

  test("It can be transformed from the domains RecruiterId") {
    val domainRecruiterId = DomainRecruiterId("ABCDEFGHIJ12345678")
    val recruiterId = domainRecruiterId.transformInto[RecruiterId]
    expect(recruiterId.value == "ABCDEFGHIJ12345678")
  }
}
