package nl.dpes.profileservice.search.searchmodel

import cats.effect.IO
import nl.dpes.profilesearch.utils.SimpleSearchQueryTranslator.UnmatchedQuoteOrBracket
import nl.dpes.profileservice.search.searchmodel.SearchTerm
import weaver.*

object SearchTermSpec extends FunSuite {
  test("SearchTerm can be parsed from a query parameter") {
    val searchTerm = SearchTerm("query=not(matching")
    searchTerm match {
      case Left(UnmatchedQuoteOrBracket(query)) => expect(clue(query) == "query=not(matching")
      case Right(value)                         => failure("SearchTerm should fail parsing")
    }
  }

  test("SearchTerm can be parsed from a valid query") {
    val searchTerm = SearchTerm("query:valid")
    searchTerm match {
      case Left(_)      => failure("The parsing of the SearchTerm should succeed")
      case Right(value) => expect(value.value == "query:valid")
    }
  }
}
