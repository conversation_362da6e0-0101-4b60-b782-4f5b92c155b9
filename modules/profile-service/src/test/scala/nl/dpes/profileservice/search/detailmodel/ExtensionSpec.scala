package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object ExtensionSpec extends FunSuite {
  test("Extension should be json encoded as a string") {
    val extension = Extension(".docx")
    val json = extension.asJson

    expect(json == ".docx".asJson)
  }

  test("Extension should be encoded and decoded correctly") {
    val extension = Extension(".docx")

    val roundTrippedExtension = decode[Extension](extension.asJson.noSpaces)

    expect(roundTrippedExtension == Right(extension))
  }

  test("Extension should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[Extension]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("Extension should transform from detailpage.Extension") {
    val detailPageExtension = detailpage.Extension(".docx")
    val extension = Extension(detailPageExtension.value)

    val transformedExtension = detailPageExtension.into[Extension].transform

    expect(transformedExtension == extension)
  }
}
