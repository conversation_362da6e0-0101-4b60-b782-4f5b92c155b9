package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object WorkLevelSpec extends FunSuite {
  test("WorkLevel should be json encoded as a string") {
    val workLevel = WorkLevel("HBO")
    val json = workLevel.asJson

    expect(json == "HBO".asJson)
  }

  test("WorkLevel should be encoded and decoded correctly") {
    val workLevel = WorkLevel("HBO")

    val roundTrippedWorkLevel = decode[WorkLevel](workLevel.asJson.noSpaces)

    expect(roundTrippedWorkLevel == Right(workLevel))
  }

  test("WorkLevel should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[WorkLevel]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("WorkLevel should transform from detailpage.WorkLevel") {
    val detailPageWorkLevel = detailpage.WorkLevel("HBO")
    val workLevel = WorkLevel(detailPageWorkLevel.value)

    val transformedWorkLevel = detailPageWorkLevel.into[WorkLevel].transform

    expect(transformedWorkLevel == workLevel)
  }
}
