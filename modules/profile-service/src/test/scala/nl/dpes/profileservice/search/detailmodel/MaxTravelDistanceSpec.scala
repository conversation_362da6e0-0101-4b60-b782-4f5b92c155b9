package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object MaxTravelDistanceSpec extends FunSuite {
  test("MaxTravelDistance should be json encoded as an integer") {
    val maxTravelDistance = MaxTravelDistance(50)
    val json = maxTravelDistance.asJson

    expect(json == 50.asJson)
  }

  test("MaxTravelDistance should be encoded and decoded correctly") {
    val maxTravelDistance = MaxTravelDistance(50)

    val roundTrippedMaxTravelDistance = decode[MaxTravelDistance](maxTravelDistance.asJson.noSpaces)

    expect(roundTrippedMaxTravelDistance == Right(maxTravelDistance))
  }

  test("MaxTravelDistance should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[MaxTravelDistance]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[Int]])
  }

  test("MaxTravelDistance should transform from detailpage.MaxTravelDistance") {
    val detailPageMaxTravelDistance = detailpage.MaxTravelDistance(50)
    val maxTravelDistance = MaxTravelDistance(detailPageMaxTravelDistance.value)

    val transformedMaxTravelDistance = detailPageMaxTravelDistance.into[MaxTravelDistance].transform

    expect(transformedMaxTravelDistance == maxTravelDistance)
  }
}
