package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.RequestedSalary
import weaver.FunSuite

object RequestedSalarySpec extends FunSuite {

  test("RequestedSalary should be json encoded as a string") {
    val requestedSalary = RequestedSalary("90000 - 100000")
    val json = requestedSalary.asJson

    expect(json == "90000 - 100000".asJson)
  }

  test("RequestedSalary should be encoded and decoded correctly") {
    val requestedSalary = RequestedSalary("90000 - 100000")

    val roundTrippedRequestedSalary = decode[RequestedSalary](requestedSalary.asJson.noSpaces)

    expect(roundTrippedRequestedSalary == Right(requestedSalary))
  }
}
