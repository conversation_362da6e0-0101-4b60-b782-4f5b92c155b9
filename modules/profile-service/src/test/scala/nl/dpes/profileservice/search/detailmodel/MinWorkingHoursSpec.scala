package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object MinWorkingHoursSpec extends FunSuite {
  test("MinWorkingHours should be json encoded as an integer") {
    val minWorkingHours = MinWorkingHours(20)
    val json = minWorkingHours.asJson

    expect(json == 20.asJson)
  }

  test("MinWorkingHours should be encoded and decoded correctly") {
    val minWorkingHours = MinWorkingHours(20)

    val roundTrippedMinWorkingHours = decode[MinWorkingHours](minWorkingHours.asJson.noSpaces)

    expect(roundTrippedMinWorkingHours == Right(minWorkingHours))
  }

  test("MinWorkingHours should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[MinWorkingHours]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[Int]])
  }

  test("MinWorkingHours should transform from detailpage.MinWorkingHours") {
    val detailPageMinWorkingHours = detailpage.MinWorkingHours(20)
    val minWorkingHours = MinWorkingHours(detailPageMinWorkingHours.value)

    val transformedMinWorkingHours = detailPageMinWorkingHours.into[MinWorkingHours].transform

    expect(transformedMinWorkingHours == minWorkingHours)
  }
}
