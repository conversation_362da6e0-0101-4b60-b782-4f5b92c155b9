package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.Province
import weaver.FunSuite

object ProvinceSpec extends FunSuite {

  test("Province should be json encoded as a string") {
    val province = Province("Noord-Holland")
    val json = province.asJson

    expect(json == "Noord-Holland".asJson)
  }

  test("Province should be encoded and decoded correctly") {
    val province = Province("Noord-Holland")

    val roundTrippedProvince = decode[Province](province.asJson.noSpaces)

    expect(roundTrippedProvince == Right(province))
  }
}
