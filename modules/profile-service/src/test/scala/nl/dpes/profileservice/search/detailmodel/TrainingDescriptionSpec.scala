package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object TrainingDescriptionSpec extends FunSuite {
  test("TrainingDescription should be json encoded as a string") {
    val trainingDescription = TrainingDescription("Scala Programming")
    val json = trainingDescription.asJson

    expect(json == "Scala Programming".asJson)
  }

  test("TrainingDescription should be encoded and decoded correctly") {
    val trainingDescription = TrainingDescription("Scala Programming")

    val roundTrippedTrainingDescription = decode[TrainingDescription](trainingDescription.asJson.noSpaces)

    expect(roundTrippedTrainingDescription == Right(trainingDescription))
  }

  test("TrainingDescription should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[TrainingDescription]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("TrainingDescription should transform from detailpage.TrainingDescription") {
    val detailPageTrainingDescription = detailpage.TrainingDescription("Scala Programming")
    val trainingDescription = TrainingDescription(detailPageTrainingDescription.value)

    val transformedTrainingDescription = detailPageTrainingDescription.into[TrainingDescription].transform

    expect(transformedTrainingDescription == trainingDescription)
  }
}
