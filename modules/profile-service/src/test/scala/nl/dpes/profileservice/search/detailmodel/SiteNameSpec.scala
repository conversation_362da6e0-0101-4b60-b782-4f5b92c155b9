package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object SiteNameSpec extends FunSuite {
  test("SiteName should be json encoded as a string") {
    val siteName = SiteName("intermediair.nl")
    val json = siteName.asJson

    expect(json == "intermediair.nl".asJson)
  }

  test("SiteName should be encoded and decoded correctly") {
    val siteName = SiteName("intermediair.nl")

    val roundTrippedSiteName = decode[SiteName](siteName.asJson.noSpaces)

    expect(roundTrippedSiteName == Right(siteName))
  }

  test("SiteName should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[SiteName]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("SiteName should transform from detailpage.SiteName") {
    val detailPageSiteName = detailpage.SiteName("intermediair.nl")
    val siteName = SiteName(detailPageSiteName.value)

    val transformedSiteName = detailPageSiteName.into[SiteName].transform

    expect(transformedSiteName == siteName)
  }
}
