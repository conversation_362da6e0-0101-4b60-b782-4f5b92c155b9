package nl.dpes.profileservice.search.searchmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.profilesearch
import sttp.tapir.Schema
import weaver.FunSuite

object EmailAddressSpec extends FunSuite {

  test("EmailAddress should be json encoded as a string") {
    val emailAddress = EmailAddress("<EMAIL>")
    val json = emailAddress.asJson

    expect(json == "<EMAIL>".asJson)
  }

  test("EmailAddress should be encoded and decoded correctly") {
    val emailAddress = EmailAddress("<EMAIL>")

    val roundTrippedEmailAddress = decode[EmailAddress](emailAddress.asJson.noSpaces)

    expect(roundTrippedEmailAddress == Right(emailAddress))
  }

  test("EmailAddress should have a schema") {
    val schema = implicitly[Schem<PERSON>[EmailAddress]]
    expect(schema.isInstanceOf[Schema[String]])
  }
}
