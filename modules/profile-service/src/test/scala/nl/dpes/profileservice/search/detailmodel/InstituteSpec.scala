package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object InstituteSpec extends FunSuite {
  test("Institute should be json encoded as a string") {
    val institute = Institute("Delft University of Technology")
    val json = institute.asJson

    expect(json == "Delft University of Technology".asJson)
  }

  test("Institute should be encoded and decoded correctly") {
    val institute = Institute("Delft University of Technology")

    val roundTrippedInstitute = decode[Institute](institute.asJson.noSpaces)

    expect(roundTrippedInstitute == Right(institute))
  }

  test("Institute should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[Institute]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("Institute should transform from detailpage.Institute") {
    val detailPageInstitute = detailpage.Institute("Delft University of Technology")
    val institute = Institute(detailPageInstitute.value)

    val transformedInstitute = detailPageInstitute.into[Institute].transform

    expect(transformedInstitute == institute)
  }
}
