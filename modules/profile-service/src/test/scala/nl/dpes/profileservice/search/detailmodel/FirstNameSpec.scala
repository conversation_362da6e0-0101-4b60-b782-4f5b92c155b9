package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object FirstNameSpec extends FunSuite {
  test("FirstName should be json encoded as a string") {
    val firstName = FirstName("John")
    val json = firstName.asJson

    expect(json == "John".asJson)
  }

  test("FirstName should be encoded and decoded correctly") {
    val firstName = FirstName("<PERSON>")

    val roundTrippedFirstName = decode[FirstName](firstName.asJson.noSpaces)

    expect(roundTrippedFirstName == Right(firstName))
  }

  test("FirstName should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[FirstName]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("FirstName should transform from detailpage.FirstName") {
    val detailPageFirstName = detailpage.FirstName("<PERSON>")
    val firstName = FirstName(detailPageFirstName.value)

    val transformedFirstName = detailPageFirstName.into[FirstName].transform

    expect(transformedFirstName == firstName)
  }
}
