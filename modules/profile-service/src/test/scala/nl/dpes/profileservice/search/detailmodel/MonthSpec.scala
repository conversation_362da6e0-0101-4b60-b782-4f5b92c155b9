package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object MonthSpec extends FunSuite {
  test("Month should be json encoded as an integer") {
    val month = Month(5)
    val json = month.asJson

    expect(json == 5.asJson)
  }

  test("Month should be encoded and decoded correctly") {
    val month = Month(5)

    val roundTrippedMonth = decode[Month](month.asJson.noSpaces)

    expect(roundTrippedMonth == Right(month))
  }

  test("Month should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[Month]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[Int]])
  }

  test("Month should transform from detailpage.Month") {
    val detailPageMonth = detailpage.Month(5)
    val month = Month(detailPageMonth.value)

    val transformedMonth = detailPageMonth.into[Month].transform

    expect(transformedMonth == month)
  }
}
