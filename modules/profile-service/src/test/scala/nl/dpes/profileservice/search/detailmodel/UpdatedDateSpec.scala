package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object UpdatedDateSpec extends FunSuite {

  test("UpdatedDate should be json encoded as a long") {
    val updatedDate = UpdatedDate(1288566000)
    val json = updatedDate.asJson

    expect(json == 1288566000.asJson)
  }

  test("UpdatedDate should be encoded and decoded correctly") {
    val updatedDate = UpdatedDate(1288566000)
    val decodedUpdatedDate = decode[UpdatedDate](updatedDate.asJson.noSpaces)

    expect(decodedUpdatedDate == Right(updatedDate))
  }

  test("UpdatedDate should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[UpdatedDate]]

    expect(schema.isInstanceOf[sttp.tapir.Schema[Long]])
  }

  test("UpdatedDate should transform from detailpage.UpdatedDate") {
    val detailPageUpdatedDate = detailpage.UpdatedDate(1288566000)
    val updatedDate = UpdatedDate(detailPageUpdatedDate.value)
    val transformedUpdatedDate = detailPageUpdatedDate.into[UpdatedDate].transform

    expect(transformedUpdatedDate == updatedDate)
  }
}
