package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object PhotoPathSpec extends FunSuite {
  test("PhotoPath should be json encoded as a string") {
    val photoPath = PhotoPath("http://example.com/photo.jpg")
    val json = photoPath.asJson

    expect(json == "http://example.com/photo.jpg".asJson)
  }

  test("PhotoPath should be encoded and decoded correctly") {
    val photoPath = PhotoPath("http://example.com/photo.jpg")

    val roundTrippedPhotoPath = decode[PhotoPath](photoPath.asJson.noSpaces)

    expect(roundTrippedPhotoPath == Right(photoPath))
  }

  test("PhotoPath should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[PhotoPath]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("PhotoPath should transform from detailpage.PhotoPath") {
    val detailPagePhotoPath = detailpage.PhotoPath("http://example.com/photo.jpg")
    val photoPath = PhotoPath(detailPagePhotoPath.value)

    val transformedPhotoPath = detailPagePhotoPath.into[PhotoPath].transform

    expect(transformedPhotoPath == photoPath)
  }
}
