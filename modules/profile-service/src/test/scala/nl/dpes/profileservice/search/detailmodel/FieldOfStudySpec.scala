package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object FieldOfStudySpec extends FunSuite {
  test("FieldOfStudy should be json encoded as a string") {
    val fieldOfStudy = FieldOfStudy("Computer Science")
    val json = fieldOfStudy.asJson

    expect(json == "Computer Science".asJson)
  }

  test("FieldOfStudy should be encoded and decoded correctly") {
    val fieldOfStudy = FieldOfStudy("Computer Science")

    val roundTrippedFieldOfStudy = decode[FieldOfStudy](fieldOfStudy.asJson.noSpaces)

    expect(roundTrippedFieldOfStudy == Right(fieldOfStudy))
  }

  test("FieldOfStudy should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[FieldOfStudy]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("FieldOfStudy should transform from detailpage.FieldOfStudy") {
    val detailPageFieldOfStudy = detailpage.FieldOfStudy("Computer Science")
    val fieldOfStudy = FieldOfStudy(detailPageFieldOfStudy.value)

    val transformedFieldOfStudy = detailPageFieldOfStudy.into[FieldOfStudy].transform

    expect(transformedFieldOfStudy == fieldOfStudy)
  }
}
