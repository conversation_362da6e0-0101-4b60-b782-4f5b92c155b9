package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object MaxWorkingHoursSpec extends FunSuite {
  test("MaxWorkingHours should be json encoded as an integer") {
    val maxWorkingHours = MaxWorkingHours(40)
    val json = maxWorkingHours.asJson

    expect(json == 40.asJson)
  }

  test("MaxWorkingHours should be encoded and decoded correctly") {
    val maxWorkingHours = MaxWorkingHours(40)

    val roundTrippedMaxWorkingHours = decode[MaxWorkingHours](maxWorkingHours.asJson.noSpaces)

    expect(roundTrippedMaxWorkingHours == Right(maxWorkingHours))
  }

  test("MaxWorkingHours should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[MaxWorkingHours]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[Int]])
  }

  test("MaxWorkingHours should transform from detailpage.MaxWorkingHours") {
    val detailPageMaxWorkingHours = detailpage.MaxWorkingHours(40)
    val maxWorkingHours = MaxWorkingHours(detailPageMaxWorkingHours.value)

    val transformedMaxWorkingHours = detailPageMaxWorkingHours.into[MaxWorkingHours].transform

    expect(transformedMaxWorkingHours == maxWorkingHours)
  }
}
