package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object IntroductionSpec extends FunSuite {
  test("Introduction should be json encoded as a string") {
    val introduction = Introduction("This is an introduction.")
    val json = introduction.asJson

    expect(json == "This is an introduction.".asJson)
  }

  test("Introduction should be encoded and decoded correctly") {
    val introduction = Introduction("This is an introduction.")

    val roundTrippedIntroduction = decode[Introduction](introduction.asJson.noSpaces)

    expect(roundTrippedIntroduction == Right(introduction))
  }

  test("Introduction should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[Introduction]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("Introduction should transform from detailpage.Introduction") {
    val detailPageIntroduction = detailpage.Introduction("This is an introduction.")
    val introduction = Introduction(detailPageIntroduction.value)

    val transformedIntroduction = detailPageIntroduction.into[Introduction].transform

    expect(transformedIntroduction == introduction)
  }
}
