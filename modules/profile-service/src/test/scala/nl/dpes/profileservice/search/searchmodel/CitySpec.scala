package nl.dpes.profileservice.search.searchmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.profilesearch
import sttp.tapir.Schema
import weaver.FunSuite

object CitySpec extends FunSuite {
  test("City should be a string") {
    val city: City = City("Amsterdam")
    expect(city.value == "Amsterdam")
  }

  test("City should be encoded and decoded correctly") {
    val city = City("Amsterdam")
    val json = city.asJson
    val decodedCity = json.as[City]

    expect(decodedCity == Right(city))
  }

  test("City should have a schema") {
    val schema = implicitly[Schema[City]]
    expect(schema.isInstanceOf[Schema[String]])
  }

  test("City should transform from profilesearch.City") {
    val profileSearchCity = profilesearch.City("Amsterdam")
    val city = City(profileSearchCity.value)

    val transformedCity = profileSearchCity.into[City].transform

    expect(transformedCity == city)
  }

}
