package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object PreferredJobSpec extends FunSuite {
  test("PreferredJob should be json encoded as a string") {
    val preferredJob = PreferredJob("Software Engineer")
    val json = preferredJob.asJson

    expect(json == "Software Engineer".asJson)
  }

  test("PreferredJob should be encoded and decoded correctly") {
    val preferredJob = PreferredJob("Software Engineer")

    val roundTrippedPreferredJob = decode[PreferredJob](preferredJob.asJson.noSpaces)

    expect(roundTrippedPreferredJob == Right(preferredJob))
  }

  test("PreferredJob should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[PreferredJob]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("PreferredJob should transform from detailpage.PreferredJob") {
    val detailPagePreferredJob = detailpage.PreferredJob("Software Engineer")
    val preferredJob = PreferredJob(detailPagePreferredJob.value)

    val transformedPreferredJob = detailPagePreferredJob.into[PreferredJob].transform

    expect(transformedPreferredJob == preferredJob)
  }
}
