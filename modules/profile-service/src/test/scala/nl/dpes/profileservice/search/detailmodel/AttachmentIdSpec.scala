package nl.dpes.profileservice.search.detailmodel

import weaver.FunSuite
import io.circe.parser.*
import io.circe.syntax.*
import io.circe.*
import nl.dpes.profilesearch.service.model.detailpage
import io.scalaland.chimney.dsl.*

object AttachmentIdSpec extends FunSuite {
  test("AttachmentId should be json encoded as a string") {
    val id = AttachmentId("12345")
    val json = id.asJson

    expect(json == "12345".asJson)
  }

  test("AttachmentId should be encoded and decoded correctly") {
    val id = AttachmentId("12345")

    val roundTrippedId = decode[AttachmentId](id.asJson.noSpaces)

    expect(roundTrippedId == Right(id))
  }

  test("AttachmentId should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[AttachmentId]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("AttachmentId should transform from detailpage.AttachmentId") {
    val detailPageId = detailpage.AttachmentId("67890")
    val attachmentId = AttachmentId(detailPageId.value)

    val transformedId = detailPageId.into[AttachmentId].transform

    expect(transformedId == attachmentId)
  }
}
