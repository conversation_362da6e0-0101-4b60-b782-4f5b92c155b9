package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object CompanyNameSpec extends FunSuite {
  test("CompanyName should be json encoded as a string") {
    val companyName = CompanyName("ACME")
    val json = companyName.asJson

    expect(json == "ACME".asJson)
  }

  test("CompanyName should be encoded and decoded correctly") {
    val companyName = CompanyName("ACME")

    val roundTrippedCompanyName = decode[CompanyName](companyName.asJson.noSpaces)

    expect(roundTrippedCompanyName == Right(companyName))
  }

  test("CompanyName should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[CompanyName]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("CompanyName should transform from detailpage.CompanyName") {
    val detailPageCompanyName = detailpage.CompanyName("ACME")
    val companyName = CompanyName(detailPageCompanyName.value)

    val transformedCompanyName = detailPageCompanyName.into[CompanyName].transform

    expect(transformedCompanyName == companyName)
  }
}
