package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.Availability
import weaver.FunSuite

object AvailabilitySpec extends FunSuite {

  test("Availability should be json encoded as a string") {
    val availability = Availability("Immediate")
    val json = availability.asJson

    expect(json == "Immediate".asJson)
  }

  test("Availability should be encoded and decoded correctly") {
    val availability = Availability("Immediate")

    val roundTrippedAvailability = decode[Availability](availability.asJson.noSpaces)

    expect(roundTrippedAvailability == Right(availability))
  }
}
