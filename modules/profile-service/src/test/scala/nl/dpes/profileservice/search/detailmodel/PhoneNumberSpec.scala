package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object PhoneNumberSpec extends FunSuite {
  test("PhoneNumber should be json encoded as a string") {
    val phoneNumber = PhoneNumber("************")
    val json = phoneNumber.asJson

    expect(json == "************".asJson)
  }

  test("PhoneNumber should be encoded and decoded correctly") {
    val phoneNumber = PhoneNumber("************")

    val roundTrippedPhoneNumber = decode[PhoneNumber](phoneNumber.asJson.noSpaces)

    expect(roundTrippedPhoneNumber == Right(phoneNumber))
  }

  test("PhoneNumber should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[PhoneNumber]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("PhoneNumber should transform from detailpage.PhoneNumber") {
    val detailPagePhoneNumber = detailpage.PhoneNumber("************")
    val phoneNumber = PhoneNumber(detailPagePhoneNumber.value)

    val transformedPhoneNumber = detailPagePhoneNumber.into[PhoneNumber].transform

    expect(transformedPhoneNumber == phoneNumber)
  }
}
