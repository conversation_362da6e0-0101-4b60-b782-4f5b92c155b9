package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object RequestedSalarySpec extends FunSuite {
  test("RequestedSSalary should be json encoded as a string") {
    val requestedSSalary = RequestedSalary("50000")
    val json = requestedSSalary.asJson

    expect(json == "50000".asJson)
  }

  test("RequestedSalary should be encoded and decoded correctly") {
    val requestedSSalary = RequestedSalary("50000")

    val roundTrippedRequestedSalary = decode[RequestedSalary](requestedSSalary.asJson.noSpaces)

    expect(roundTrippedRequestedSalary == Right(requestedSSalary))
  }

  test("RequestedSalary should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[RequestedSalary]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("RequestedSalary should transform from detailpage.RequestedSalary") {
    val detailPageRequestedSalary = detailpage.RequestedSalary("50000")
    val requestedSSalary = RequestedSalary(detailPageRequestedSalary.value)

    val transformedRequestedSalary = detailPageRequestedSalary.into[RequestedSalary].transform

    expect(transformedRequestedSalary == requestedSSalary)
  }
}
