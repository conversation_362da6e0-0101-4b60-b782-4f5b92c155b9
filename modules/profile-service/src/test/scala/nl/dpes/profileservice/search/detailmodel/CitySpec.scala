package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object CitySpec extends FunSuite {
  test("city should be json encoded as a string") {
    val city = City("Klazienaveen")
    val json = city.asJson

    expect(json == "Klazienaveen".asJson)
  }

  test("City should be encoded and decoded correctly") {
    val city = City("Klazienaveen")

    val roundTrippedCity = decode[City](city.asJson.noSpaces)

    expect(roundTrippedCity == Right(city))
  }

  test("City should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[City]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("City should transform from detailpage.City") {
    val detailPageCity = detailpage.City("Klazienaveen")
    val city = City(detailPageCity.value)

    val transformedCity = detailPageCity.into[City].transform

    expect(transformedCity == city)
  }
}
