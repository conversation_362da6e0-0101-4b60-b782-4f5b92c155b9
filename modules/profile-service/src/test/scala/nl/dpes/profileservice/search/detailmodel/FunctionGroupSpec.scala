package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object FunctionGroupSpec extends FunSuite {
  test("FunctionGroup should be json encoded as a string") {
    val functionGroup = FunctionGroup("Engineering")
    val json = functionGroup.asJson

    expect(json == "Engineering".asJson)
  }

  test("FunctionGroup should be encoded and decoded correctly") {
    val functionGroup = FunctionGroup("Engineering")

    val roundTrippedFunctionGroup = decode[FunctionGroup](functionGroup.asJson.noSpaces)

    expect(roundTrippedFunctionGroup == Right(functionGroup))
  }

  test("FunctionGroup should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[FunctionGroup]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("FunctionGroup should transform from detailpage.FunctionGroup") {
    val detailPageFunctionGroup = detailpage.FunctionGroup("Engineering")
    val functionGroup = FunctionGroup(detailPageFunctionGroup.value)

    val transformedFunctionGroup = detailPageFunctionGroup.into[FunctionGroup].transform

    expect(transformedFunctionGroup == functionGroup)
  }
}
