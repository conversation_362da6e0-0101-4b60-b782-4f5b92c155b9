package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.DriverLicense
import weaver.FunSuite

object DriverLicenseSpec extends FunSuite {

  test("DriverLicense should be json encoded as a string") {
    val driverLicense = DriverLicense("A-license")
    val json = driverLicense.asJson

    expect(json == "A-license".asJson)
  }

  test("DriverLicense should be encoded and decoded correctly") {
    val driverLicense = DriverLicense("A-license")

    val roundTrippedDriverLicense = decode[DriverLicense](driverLicense.asJson.noSpaces)

    expect(roundTrippedDriverLicense == Right(driverLicense))
  }
}
