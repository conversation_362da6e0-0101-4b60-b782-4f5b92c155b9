package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object DriversLicenseSpec extends FunSuite {
  test("DriversLicense should be json encoded as a string") {
    val driversLicense = DriversLicense("B")
    val json = driversLicense.asJson

    expect(json == "B".asJson)
  }

  test("DriversLicense should be encoded and decoded correctly") {
    val driversLicense = DriversLicense("B")

    val roundTrippedDriversLicense = decode[DriversLicense](driversLicense.asJson.noSpaces)

    expect(roundTrippedDriversLicense == Right(driversLicense))
  }

  test("DriversLicense should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[DriversLicense]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("DriversLicense should transform from detailpage.DriversLicense") {
    val detailPageDriversLicense = detailpage.DriversLicense("B")
    val driversLicense = DriversLicense(detailPageDriversLicense.value)

    val transformedDriversLicense = detailPageDriversLicense.into[DriversLicense].transform

    expect(transformedDriversLicense == driversLicense)
  }
}
