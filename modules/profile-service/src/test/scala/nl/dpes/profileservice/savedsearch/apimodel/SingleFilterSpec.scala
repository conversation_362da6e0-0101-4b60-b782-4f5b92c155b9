package nl.dpes.profileservice.savedsearch.apimodel

import io.scalaland.chimney.dsl.*
import io.scalaland.chimney.partial
import io.scalaland.chimney.partial.ErrorMessage.EmptyValue
import nl.dpes.profilesearch.domain.{City as DomainCity, SearchTerm as DomainSearchTerm, UpdateDate as DomainUpdateDate}
import nl.dpes.profilesearch.domain.filter.*
import weaver.FunSuite

object SingleFilterSpec extends FunSuite {
  test("SingleFilter[SearchTerm] should be able transform to and from SearchTermFilter") {

    val apiValue: SearchTerm = SearchTerm("test search term")
    val domainValue: DomainSearchTerm = DomainSearchTerm.fromString("test search term").get

    val singleFilter: SingleFilter[SearchTerm] = SingleFilter(apiValue)
    val domainFilter: SearchTermFilter = SearchTermFilter(domainValue)

    expect.same(singleFilter.intoPartial[SearchTermFilter].transform.asEither, Right(domainFilter)) and
    expect.same(domainFilter.into[SingleFilter[SearchTerm]].transform, singleFilter)
  }

  test("SingleFilter[SearchTerm] should not be empty when transforming to SearchTermFilter") {
    val emptySingleFilter: SingleFilter[SearchTerm] = SingleFilter(SearchTerm(""))

    emptySingleFilter.intoPartial[SearchTermFilter].transform.asEither match {
      case Right(_) => failure("Expected an exception for empty SingleFilter")
      case Left(e)  => expect(clue(e.errors.toList) == List(partial.Error(EmptyValue)))
    }
  }

  test("SingleFilter[City] should be able to transform to and from CityFilter") {

    val apiValue: City = City("Amsterdam")
    val domainValue: DomainCity = DomainCity("Amsterdam")

    val singleFilter: SingleFilter[City] = SingleFilter(apiValue)
    val domainFilter: CityFilter = CityFilter(domainValue)

    expect.same(singleFilter.into[CityFilter].transform, domainFilter) and
    expect.same(domainFilter.into[SingleFilter[City]].transform, singleFilter)
  }

  test("SingleFilter[GeoDistance] should be able to transform to and from GeoDistanceFilter") {
    val apiValue: GeoDistance = GeoDistance(Latitude("52.194"), Longitude("4.562"), None)
    val domainValue: GeoDistanceFilter = GeoDistanceFilter(52.194, 4.562, None)

    val singleFilter: SingleFilter[GeoDistance] = SingleFilter(apiValue)

    expect.same(singleFilter.into[GeoDistanceFilter].transform, domainValue) and
    expect.same(domainValue.into[SingleFilter[GeoDistance]].transform, singleFilter)
  }

  test("SingleFilter[UpdatedDate] should be able to transform to and from UpdateDateFilter") {
    val apiValue: UpdatedDate = UpdatedDate("Afgelopen jaar")
    val domainValue: DomainUpdateDate = DomainUpdateDate.LastYear

    val singleFilter: SingleFilter[UpdatedDate] = SingleFilter(apiValue)
    val updateDateFilter: UpdateDateFilter = UpdateDateFilter(domainValue)

    expect.same(singleFilter.intoPartial[UpdateDateFilter].transform.asEither, Right(updateDateFilter)) and
    expect.same(updateDateFilter.into[SingleFilter[UpdatedDate]].transform, singleFilter)
  }

  test("SingleFilter[UpdatedDate] should not allow incorrect values") {
    val invalidApiValue: UpdatedDate = UpdatedDate("Invalid date")

    val singleFilter: SingleFilter[UpdatedDate] = SingleFilter(invalidApiValue)

    singleFilter.intoPartial[UpdateDateFilter].transform.asEither match {
      case Right(_) => failure("Expected an exception for invalid UpdatedDate")
      case Left(e)  => expect(clue(e.errors.toList) == List(partial.Error(EmptyValue)))
    }
  }
}
