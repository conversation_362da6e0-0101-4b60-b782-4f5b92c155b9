package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.Latitude
import weaver.FunSuite

object LatitudeSpec extends FunSuite {

  test("Latitude should be json encoded as a string") {
    val latitude = Latitude("10.10")
    val json = latitude.asJson

    expect(json == "10.10".asJson)
  }

  test("Latitude should be encoded and decoded correctly") {
    val latitude = Latitude("10.10")

    val roundTrippedLatitude = decode[Latitude](latitude.asJson.noSpaces)

    expect(roundTrippedLatitude == Right(latitude))
  }
}
