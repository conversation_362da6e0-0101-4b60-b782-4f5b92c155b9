package nl.dpes.profileservice.savedsearch.apimodel

import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import nl.dpes.profileservice.savedsearch.apimodel.Frequency
import weaver.FunSuite

object FrequencySpec extends FunSuite {

  test("Frequency should be json encoded as a string") {
    val frequency = Frequency("Dagelijks").toOption.get
    val json = frequency.as<PERSON>son

    expect(json == "Dagelijks".asJson)
  }

  test("Frequency should be encoded and decoded correctly") {
    val frequency = Frequency("Dagelijks").toOption.get

    val roundTrippedFrequency = decode[Frequency](frequency.asJson.noSpaces)

    expect(roundTrippedFrequency == Right(frequency))
  }

  test("Unable to decode an invalid frequency") {
    expect(
      clue(Json.fromString("unknown").as[Frequency]) == Left(
        DecodingFailure(
          s"The provided frequency 'unknown' is not supported. The possible values are 'Da<PERSON>ijks', '<PERSON><PERSON>i<PERSON><PERSON>' and 'Nooit'",
          List()
        )
      )
    )
  }

  test("formatting of supported values should be correct") {
    val noValue = List.empty[String]
    val singleValue = List("a")
    val doubleValue = List("a", "b")
    val tripleValue = List("a", "b", "c")
    val quadValue = List("a", "b", "c", "d")

    expect(Frequency.formatSupportedValues(noValue) == "") &&
    expect(Frequency.formatSupportedValues(singleValue) == "The possible value is 'a'") &&
    expect(Frequency.formatSupportedValues(doubleValue) == "The possible values are 'a' and 'b'") &&
    expect(Frequency.formatSupportedValues(tripleValue) == "The possible values are 'a', 'b' and 'c'") &&
    expect(Frequency.formatSupportedValues(quadValue) == "The possible values are 'a', 'b', 'c' and 'd'")
  }
}
