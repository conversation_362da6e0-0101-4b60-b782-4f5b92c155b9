package nl.dpes.profileservice.savedsearch.apimodel

import cats.data.NonEmptyList
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.domain
import nl.dpes.profilesearch.domain.filter
import weaver.FunSuite

object SearchFiltersSpec extends FunSuite {
  test("The search filters should be transformed") {
    val searchFilters = SearchFilters(
      Some(SingleFilter(SearchTerm("developer"))),
      Some(SingleFilter(City("Amsterdam"))),
      Some(SingleFilter(GeoDistance(Latitude("52.3676"), Longitude("4.9041"), None))),
      Some(MultiFilter(Seq(Province("Noord-Holland")))),
      Some(SingleFilter(UpdatedDate("Alles"))),
      Some(MultiFilter(Seq(FunctionGroup("Software Engineering")))),
      Some(MultiFilter(Seq(WorkLevel("Senior")))),
      Some(MultiFilter(Seq(WorkingHour("32 tot en met 40 uur")))),
      Some(MultiFilter(Seq(CareerLevel("Mid-level")))),
      Some(MultiFilter(Seq(RequestedSalary("> 7.000")))),
      Some(MultiFilter(Seq(Availability("Per direct")))),
      Some(MultiFilter(Seq(DriverLicense("B")))),
      Some(MultiFilter(Seq(Language("English"))))
    )

    val expectedFilters = Right(
      nl.dpes.savedsearch.domain.SavedSearchFilters(
        searchTerm = Some(filter.SearchTermFilter(domain.SearchTerm.unsafe("developer"))),
        city = Some(filter.CityFilter(domain.City("Amsterdam"))),
        geoDistance = Some(filter.GeoDistanceFilter(52.3676, 4.9041, None)),
        provinces = Some(filter.ProvinceFilter(NonEmptyList.of(domain.Province("Noord-Holland")))),
        updatedDate = Some(filter.UpdateDateFilter(domain.UpdateDate.All)),
        functionGroups = Some(filter.FunctionGroupFilter(NonEmptyList.of(domain.FunctionGroup("Software Engineering")))),
        workLevels = Some(filter.WorkLevelFilter(NonEmptyList.of(domain.WorkLevel("Senior")))),
        workingHours = Some(filter.WorkingHourFilter(NonEmptyList.of(domain.WorkingHours.From32To40))),
        careerLevels = Some(filter.CareerLevelFilter(NonEmptyList.of(domain.CareerLevel("Mid-level")))),
        requestedSalaries = Some(filter.RequestedSalaryFilter(NonEmptyList.of(domain.RequestedSalary.MoreThan7000))),
        availabilities = Some(filter.AvailabilityFilter(NonEmptyList.of(domain.Availability.PerDirect))),
        driversLicenses = Some(filter.DriversLicenseFilter(NonEmptyList.of(domain.DriversLicense("B")))),
        languages = Some(filter.LanguageFilter(NonEmptyList.of(domain.Language("English"))))
      )
    )

    expect.same(expectedFilters, searchFilters.intoPartial[nl.dpes.savedsearch.domain.SavedSearchFilters].transform.asEither)
  }

  test("An empty JSON representation should be decoded to SearchFilters with all None values") {
    val json = """{"searchTerm": ""}"""
    val expectedFilters = SearchFilters(None, None, None, None, None, None, None, None, None, None, None, None, None)

    val decodedFilters = io.circe.parser.decode[SearchFilters](json)

    decodedFilters match {
      case Left(error)    => failure(s"Decoding failed with error: $error")
      case Right(filters) => expect(expectedFilters == filters)
    }
  }

  test("A JSON representation with null values should be decoded to SearchFilters with all None values") {
    val json =
      """{"searchTerm": null, "city": null, "geoDistance": null, "provinces": null, "updatedDate": null, "functionGroups": null, "workLevels": null, "workingHours": null, "careerLevels": null, "requestedSalaries": null, "availabilities": null, "driversLicenses": null, "languages": null}"""
    val expectedFilters = SearchFilters(None, None, None, None, None, None, None, None, None, None, None, None, None)

    val decodedFilters = io.circe.parser.decode[SearchFilters](json)

    decodedFilters match {
      case Left(error)    => failure(s"Decoding failed with error: $error")
      case Right(filters) => expect(expectedFilters == filters)
    }
  }

  test("An object with only Nones will be encoded as an empty JSON object") {

    val json =
      """{}"""
    val filters = SearchFilters(None, None, None, None, None, None, None, None, None, None, None, None, None)

    val encodedFilters = filters.asJson.noSpaces

    expect(clue(encodedFilters) == json)
  }

  test("An object with empty lists, none and strings will also be encoded as an empty JSON object") {
    val json =
      """{}"""
    val filters = SearchFilters(
      Some(SingleFilter(SearchTerm(""))),
      Some(SingleFilter(City(""))),
      None,
      Some(MultiFilter(Seq.empty)),
      Some(SingleFilter(UpdatedDate(""))),
      Some(MultiFilter(Seq.empty)),
      Some(MultiFilter(Seq.empty)),
      Some(MultiFilter(Seq.empty)),
      Some(MultiFilter(Seq.empty)),
      Some(MultiFilter(Seq.empty)),
      Some(MultiFilter(Seq.empty)),
      Some(MultiFilter(Seq.empty)),
      Some(MultiFilter(Seq.empty))
    )

    val encodedFilters = filters.asJson.noSpaces

    expect(clue(encodedFilters) == json)
  }

  test("A JSON representation with empty arrays and strings should be decoded to SearchFilters with all None values") {
    val json =
      """{"searchTerm": "", "city": "", "geoDistance": null, "provinces": [], "updatedDate": "", "functionGroups": [], "workLevels": [], "workingHours": [], "careerLevels": [], "requestedSalaries": [], "availabilities": [], "driversLicenses": [], "languages": []}"""
    val expectedFilters = SearchFilters(None, None, None, None, None, None, None, None, None, None, None, None, None)

    val decodedFilters = io.circe.parser.decode[SearchFilters](json)

    decodedFilters match {
      case Left(error)    => failure(s"Decoding failed with error: $error")
      case Right(filters) => expect(clue(filters) == expectedFilters)
    }
  }

}
