package nl.dpes.profileservice.savedsearch

import cats.Monad
import cats.effect.IO
import io.scalaland.chimney.dsl.*
import nl.dpes.profileservice.savedsearch.apimodel.*
import nl.dpes.profileservice.tapir.ErrorMessage
import nl.dpes.savedsearch.domain.{
  Frequency as DomainFrequency,
  Name as DomainName,
  RecruiterId as DomainRecruiterId,
  SavedSearch as DomainSavedSearch,
  SavedSearchFilters as DomainSearchFilters
}
import nl.dpes.savedsearch.service.SavedSearchService
import nl.dpes.savedsearch.service.SavedSearchService.SavedSearchServiceException
import org.scalamock.stubs.Stubs
import org.typelevel.log4cats.testing.StructuredTestingLogger
import org.typelevel.log4cats.{LoggerFactory, SelfAwareStructuredLogger}
import weaver.SimpleIOSuite

import java.util.UUID

object SavedSearchControllerServiceSpec extends SimpleIOSuite with Stubs {
  private val searchFilters: SearchFilters = SearchFilters(
    searchTerm = None,
    city = None,
    geoDistance = None,
    provinces = None,
    updatedDate = None,
    functionGroups = None,
    workLevels = None,
    workingHours = None,
    careerLevels = None,
    requestedSalaries = None,
    availabilities = None,
    driversLicenses = None,
    languages = None
  )

  private val logger = StructuredTestingLogger.impl[IO]()
  given loggerFactory: LoggerFactory[IO] = getLoggerFactory(logger)

  test("It should be possible to create a saved search") {
    val savedSearchService = stub[SavedSearchService[IO]]
    savedSearchService.create.returns(_ => IO.pure(nl.dpes.savedsearch.domain.SavedSearchId("123e4567-e89b-12d3-a456-************")))

    val savedSearchControllerService = SavedSearchControllerService.impl(savedSearchService)

    for {
      result <- savedSearchControllerService.create(
        RecruiterId.unsafeApply("recruiter1"),
        SavedSearchCreation(
          SavedSearchName.apply("Test Search"),
          Frequency.unsafeApply("Dagelijks"),
          searchFilters
        )
      )
    } yield expect(result.isRight, result.toString) // Assuming the service returns Right for successful creation
  }

  test("It should return an error when creating a saved search fails") {
    val savedSearchService = stub[SavedSearchService[IO]]
    savedSearchService.create.returns(_ => IO.raiseError(new Exception("Creation failed")))

    val savedSearchControllerService = SavedSearchControllerService.impl(savedSearchService)

    for {
      result <- savedSearchControllerService
        .create(
          RecruiterId.unsafeApply("recruiter1"),
          SavedSearchCreation(
            SavedSearchName.apply("Test Search"),
            Frequency.unsafeApply("Dagelijks"),
            searchFilters
          )
        )
    } yield expect(result.isLeft, result.toString) // Expecting an error due to the raised exception
  }

  test("It should return a conflict error when a saved search already exists") {
    val savedSearchService = stub[SavedSearchService[IO]]
    savedSearchService.create.returns(_ =>
      IO.raiseError(SavedSearchService.SavedSearchAlreadyExistsException("Saved search already exists"))
    )

    val savedSearchControllerService = SavedSearchControllerService.impl(savedSearchService)

    for {
      result <- savedSearchControllerService.create(
        RecruiterId.unsafeApply("recruiter1"),
        SavedSearchCreation(
          SavedSearchName.apply("Test Search"),
          Frequency.unsafeApply("Dagelijks"),
          searchFilters
        )
      )
    } yield expect(result.isLeft, result.toString) // Expecting a conflict error
  }

  test("It should retrieve all saved searches for a recruiter") {
    val savedSearchService = stub[SavedSearchService[IO]]
    val savedSearches = IO(
      List(
        DomainSavedSearch(
          id = UUID.fromString("123e4567-e89b-12d3-a456-************"),
          name = DomainName("search1"),
          recruiterId = DomainRecruiterId("recruiter1"),
          frequency = DomainFrequency.Daily,
          filters = searchFilters
            .intoPartial[DomainSearchFilters]
            .transform
            .asOption
            .getOrElse(
              throw new Exception("Failed to transform search filters")
            )
        )
      )
    )
    savedSearchService.getAllForRecruiter.returns(_ => savedSearches)

    val savedSearchControllerService = SavedSearchControllerService.impl(savedSearchService)

    for {
      result <- savedSearchControllerService.getAll(RecruiterId.unsafeApply("recruiter1"))
    } yield expect(result.isRight && result.toOption.exists(_.nonEmpty), result.toString) // Expecting a non-empty list of searches
  }

  test("It should update the frequency of a saved search") {
    val savedSearchService = stub[SavedSearchService[IO]]
    savedSearchService.updateFrequency.returns((_, _, _) => IO.unit)

    val savedSearchControllerService = SavedSearchControllerService.impl(savedSearchService)

    for {
      result <- savedSearchControllerService.update(
        RecruiterId.unsafeApply("recruiter1"),
        SavedSearchId("123e4567-e89b-12d3-a456-************"),
        SavedSearchUpdate(Frequency.unsafeApply("Wekelijks"))
      )
    } yield expect(result.isRight, result.toString) // Assuming the service returns Right for successful update
  }

  test("It should return an error when updating a saved search fails") {
    val savedSearchService = stub[SavedSearchService[IO]]
    savedSearchService.updateFrequency.returns((_, _, _) => IO.raiseError(new Exception("Update failed")))

    val savedSearchControllerService = SavedSearchControllerService.impl(savedSearchService)

    for {
      result <- savedSearchControllerService.update(
        RecruiterId.unsafeApply("recruiter1"),
        SavedSearchId("123e4567-e89b-12d3-a456-************"),
        SavedSearchUpdate(Frequency.unsafeApply("Wekelijks"))
      )
    } yield expect(result.isLeft, result.toString) // Expecting an error due to the raised exception
  }

  test("It should be able to delete a saved search") {
    val savedSearchService = stub[SavedSearchService[IO]]
    savedSearchService.delete.returns(_ => IO.unit)

    val savedSearchControllerService = SavedSearchControllerService.impl(savedSearchService)

    for {
      result <- savedSearchControllerService.delete(
        RecruiterId.unsafeApply("123456789123456789"),
        SavedSearchId("123456789123456789123456789123456789")
      )
    } yield expect(result == Right(()))
  }

  test("It should return an error when unable to delete a saved search") {
    val savedSearchService = stub[SavedSearchService[IO]]
    savedSearchService.delete.returns(_ => IO.raiseError(SavedSearchServiceException("Unable to delete")))

    val savedSearchControllerService = SavedSearchControllerService.impl(savedSearchService)

    val id = SavedSearchId("123456789123456789123456789123456789")
    val recruiterId = RecruiterId.unsafeApply("123456789123456789")
    for {
      result <- savedSearchControllerService.delete(recruiterId, id)
    } yield result match {
      case Left(thr) =>
        expect(
          clue(thr.getMessage) == s"Unknown error: Failed to delete the saved search '${id.value}' for recruiter '${recruiterId.value}'"
        )
      case Right(value) => failure(s"Expected an error but got $value")
    }
  }

  test("A saved search can be retrieved by id for a recruiter") {
    val savedSearchService = stub[SavedSearchService[IO]]
    val savedSearchId = nl.dpes.savedsearch.domain.SavedSearchId("123e4567-e89b-12d3-a456-************")
    val recruiterId = nl.dpes.savedsearch.domain.RecruiterId("recruiter1")
    val savedSearch = nl.dpes.savedsearch.domain.SavedSearch(
      id = UUID.fromString(savedSearchId.value),
      name = DomainName("Test Search"),
      recruiterId = recruiterId,
      frequency = DomainFrequency.Daily,
      filters = searchFilters
        .intoPartial[DomainSearchFilters]
        .transform
        .asOption
        .getOrElse(
          throw new Exception("Failed to transform search filters")
        )
    )

    savedSearchService.getById.returns((_, _) => IO.pure(Some(savedSearch)))

    val savedSearchControllerService = SavedSearchControllerService.impl(savedSearchService)

    for {
      result <- savedSearchControllerService.getById(
        RecruiterId.unsafeApply(recruiterId.value),
        SavedSearchId(savedSearchId.value)
      )
    } yield expect(result.isRight && result.toOption.contains(savedSearch.into[SavedSearch].transform), result.toString)
  }

  test("When a saved search does not exist a 404 is returned") {
    val savedSearchService = stub[SavedSearchService[IO]]
    savedSearchService.getById.returns((_, _) => IO.pure(None))

    val savedSearchControllerService = SavedSearchControllerService.impl(savedSearchService)

    for {
      result <- savedSearchControllerService.getById(
        RecruiterId.unsafeApply("recruiter1"),
        SavedSearchId("123e4567-e89b-12d3-a456-************")
      )
    } yield expect(result.isLeft && result.left.toOption.exists(_.isInstanceOf[ErrorMessage.NotFound]), result.toString)
  }

  test("Any error produced when getting a saved search will be returned as an unknown error") {
    val savedSearchService = stub[SavedSearchService[IO]]
    savedSearchService.getById.returns((_, _) => IO.raiseError(new Exception("Unknown error")))

    val savedSearchControllerService = SavedSearchControllerService.impl(savedSearchService)

    for {
      result <- savedSearchControllerService.getById(
        RecruiterId.unsafeApply("recruiter1"),
        SavedSearchId("123e4567-e89b-12d3-a456-************")
      )
    } yield expect(result.isLeft && result.left.toOption.exists(_.isInstanceOf[ErrorMessage.UnknownError]), result.toString)
  }

  private def getLoggerFactory[F[_]: Monad](logger: StructuredTestingLogger[F]) = new LoggerFactory[F] {
    override def getLoggerFromName(name: String): SelfAwareStructuredLogger[F] = logger
    override def fromName(name: String): F[SelfAwareStructuredLogger[F]] = Monad[F].pure(getLoggerFromName(name))
  }
}
