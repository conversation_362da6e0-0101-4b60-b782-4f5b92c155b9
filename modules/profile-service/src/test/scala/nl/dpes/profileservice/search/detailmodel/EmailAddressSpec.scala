package nl.dpes.profileservice.search.detailmodel

import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.detailpage
import weaver.FunSuite

object EmailAddressSpec extends FunSuite {
  test("EmailAddress should be json encoded as a string") {
    val emailAddress = EmailAddress("<EMAIL>")
    val json = emailAddress.asJson

    expect(json == "<EMAIL>".asJson)
  }

  test("EmailAddress should be encoded and decoded correctly") {
    val emailAddress = EmailAddress("<EMAIL>")

    val roundTrippedEmailAddress = decode[EmailAddress](emailAddress.asJson.noSpaces)

    expect(roundTrippedEmailAddress == Right(emailAddress))
  }

  test("EmailAddress should have a schema") {
    val schema = implicitly[sttp.tapir.Schema[EmailAddress]]
    expect(schema.isInstanceOf[sttp.tapir.Schema[String]])
  }

  test("EmailAddress should transform from detailpage.EmailAddress") {
    val detailPageEmailAddress = detailpage.EmailAddress("<EMAIL>")
    val emailAddress = EmailAddress(detailPageEmailAddress.value)

    val transformedEmailAddress = detailPageEmailAddress.into[EmailAddress].transform

    expect(transformedEmailAddress == emailAddress)
  }
}
