package nl.dpes.recruiterfavorites.service

import cats.data.*
import nl.dpes.recruiterfavorites.service.domainmodel.{PaginatedProfileIds, ProfileId, RecruiterId}

trait FavoritesRepository[F[_]] {
  def initialize: F[Unit]
  def saveFavorite(recruiterId: RecruiterId, profileId: ProfileId): F[Unit]
  def deleteFavorite(recruiterId: RecruiterId, profileId: ProfileId): F[Unit]
  def deleteFavorite(profileId: ProfileId): F[Unit]
  def isFavorite(recruiterId: RecruiterId, profileId: ProfileId): F[Boolean]
  def getFavoritesByIds(recruiterId: RecruiterId, profileIds: NonEmptyList[ProfileId]): F[List[ProfileId]]
  def getFavoritesForRecruiter(recruiterId: RecruiterId, page: Long = 1, limit: Long = 10): F[PaginatedProfileIds]
}
