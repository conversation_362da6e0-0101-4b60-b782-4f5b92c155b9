package nl.dpes.recruiterfavorites.service

import cats.implicits.*
import cats.effect.kernel.{MonadCancelThrow, Resource}
import org.typelevel.log4cats.LoggerFactory
import nl.dpes.recruiterfavorites.service.domainmodel.*

trait FavoritesService[F[_]] {
  def saveFavorite(recruiterId: RecruiterId, profileId: ProfileId): F[Unit]

  def deleteFavorite(recruiterId: RecruiterId, profileId: ProfileId): F[Unit]

  def deleteFavorite(profileId: ProfileId): F[Unit]

  def isFavorite(recruiterId: RecruiterId, profileId: ProfileId): F[Boolean]

  def filterFavorites(recruiterId: RecruiterId, profileIds: List[ProfileId]): F[List[ProfileId]]

  def getFavoritesForRecruiter(recruiterId: RecruiterId, page: Long = 1, limit: Long = 10): F[PaginatedProfileIds]
}

object FavoritesService {

  def impl[F[_]: {MonadCancelThrow, LoggerFactory}](favoritesRepository: FavoritesRepository[F]): FavoritesService[F] =
    new FavoritesService[F] {
      override def saveFavorite(recruiterId: RecruiterId, profileId: ProfileId): F[Unit] =
        favoritesRepository.saveFavorite(recruiterId, profileId)
          *> LoggerFactory[F].getLogger.info(s"Recruiter '$recruiterId' has added the profile '$profileId' to the favorite list.")

      override def deleteFavorite(recruiterId: RecruiterId, profileId: ProfileId): F[Unit] =
        favoritesRepository.deleteFavorite(recruiterId, profileId)

      override def deleteFavorite(profileId: ProfileId): F[Unit] =
        favoritesRepository.deleteFavorite(profileId)

      override def isFavorite(recruiterId: RecruiterId, profileId: ProfileId): F[Boolean] =
        favoritesRepository.isFavorite(recruiterId, profileId)

      override def filterFavorites(recruiterId: RecruiterId, profileIds: List[ProfileId]): F[List[ProfileId]] =
        profileIds.toNel match {
          case Some(ids) => favoritesRepository.getFavoritesByIds(recruiterId, ids)
          case None      => List[ProfileId]().pure
        }

      override def getFavoritesForRecruiter(recruiterId: RecruiterId, page: Long = 1, limit: Long = 10): F[PaginatedProfileIds] =
        favoritesRepository.getFavoritesForRecruiter(recruiterId, page, limit)
    }

  def resource[F[_]: {MonadCancelThrow, LoggerFactory}](favoritesRepository: FavoritesRepository[F]): Resource[F, FavoritesService[F]] =
    Resource.pure(FavoritesService.impl(favoritesRepository))
}
