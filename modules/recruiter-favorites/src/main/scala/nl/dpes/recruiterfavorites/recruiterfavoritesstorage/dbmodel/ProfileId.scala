package nl.dpes.recruiterfavorites.recruiterfavoritesstorage.dbmodel

import doobie.Meta
import io.scalaland.chimney.Transformer
import nl.dpes.recruiterfavorites.service.domainmodel.ProfileId as DomainProfileId

case class ProfileId(value: String)

object ProfileId {
  given Meta[ProfileId] = Meta[String].imap(apply)(_.value)
  given Transformer[ProfileId, DomainProfileId] = id => DomainProfileId(id.value)
  given Transformer[DomainProfileId, ProfileId] = id => ProfileId(id.value)
}
