package nl.dpes.recruiterfavorites.recruiterfavoritesstorage

import cats.data.*
import cats.effect.*
import cats.implicits.*
import doobie.implicits.*
import doobie.util.transactor.Transactor
import doobie.{Fragment, Fragments, Meta}
import io.scalaland.chimney.dsl.*
import nl.dpes.recruiterfavorites.recruiterfavoritesstorage.dbmodel.{ProfileId as DbProfileId, RecruiterId as DbRecruiterId}
import nl.dpes.recruiterfavorites.service.FavoritesRepository
import nl.dpes.recruiterfavorites.recruiterfavoritesstorage.config.FavoriteProfilesConfig.TableName
import nl.dpes.recruiterfavorites.service.domainmodel.{PaginatedProfileIds, ProfileId, RecruiterId}
import org.typelevel.log4cats.LoggerFactory

import java.sql.Timestamp
import java.time.Instant

object MysqlFavoritesRepository {

  given Meta[Instant] = Meta[Timestamp].imap(_.toInstant)(Timestamp.from)

  def impl[F[_]: {MonadCancelThrow, LoggerFactory}](tableName: TableName, xa: Transactor[F]): FavoritesRepository[F] =
    new FavoritesRepository[F] {

      val favoriteProfiles: Fragment = Fragment.const(tableName.value)

      override def initialize: F[Unit] =
        sql"""
       CREATE TABLE IF NOT EXISTS $favoriteProfiles (
          recruiterId VARCHAR(36),
          profileId   VARCHAR(36),
          timestamp   TIMESTAMP NOT NULL,
          PRIMARY KEY (recruiterId, profileId),
          INDEX (recruiterId),
          INDEX (profileId)
       )
      """.update.run.transact(xa).void

      override def saveFavorite(recruiterId: RecruiterId, profileId: ProfileId): F[Unit] = {
        val dbRecruiterId = getRecruiterDbId(recruiterId)
        val dbProfileId = getProfileDbId(profileId)

        sql"""
        INSERT INTO $favoriteProfiles (recruiterId, profileId, timestamp)
        VALUES ($dbRecruiterId, $dbProfileId, ${Instant.now()})
         """.update.run.transact(xa).void.handleErrorWith { thr =>
          if (thr.getMessage.toLowerCase.contains("duplicate")) {
            LoggerFactory[F].getLogger.warn(s"Profile '$profileId' was already set as favorite by the recruiter '$recruiterId'.")
          } else {
            thr.raiseError[F, Unit]
          }
        }
      }

      override def deleteFavorite(recruiterId: RecruiterId, profileId: ProfileId): F[Unit] = {
        val dbRecruiterId = getRecruiterDbId(recruiterId)
        val dbProfileId = getProfileDbId(profileId)

        sql"""
           DELETE FROM $favoriteProfiles
           WHERE recruiterId = $dbRecruiterId AND profileId = $dbProfileId
          """.update.run.transact(xa).flatMap { deletedRows =>
          if (deletedRows == 0)
            LoggerFactory[F].getLogger.warn(s"No favorite profile found '$profileId' to delete for '$recruiterId'")
          else
            LoggerFactory[F].getLogger.info(s"Recruiter '$recruiterId' has removed the profile '$profileId' from the favorite list.")
        }
      }

      override def deleteFavorite(profileId: ProfileId): F[Unit] = {
        val dbProfileId = getProfileDbId(profileId)

        sql"""
           DELETE FROM $favoriteProfiles
           WHERE profileId = $dbProfileId
          """.update.run.transact(xa).flatMap { deletedRows =>
          if (deletedRows == 0)
            LoggerFactory[F].getLogger.warn(s"Profile '${profileId.value}' was not found. Nothing to be deleted from the favorites list.")
          else
            LoggerFactory[F].getLogger.info(s"Profile '${profileId.value}' was deleted from the favorites list.")
        }
      }

      override def isFavorite(recruiterId: RecruiterId, profileId: ProfileId): F[Boolean] = {
        val dbRecruiterId = getRecruiterDbId(recruiterId)
        val dbProfileId = getProfileDbId(profileId)

        sql"""
           SELECT EXISTS(
             SELECT 1 FROM $favoriteProfiles
             WHERE recruiterId = $dbRecruiterId AND profileId = $dbProfileId
           )
         """.query[Boolean].unique.transact(xa)
      }

      override def getFavoritesByIds(recruiterId: RecruiterId, profileIds: NonEmptyList[ProfileId]): F[List[ProfileId]] =
        val dbRecruiterId = getRecruiterDbId(recruiterId)
        val dbProfileIds = profileIds.into[NonEmptyList[DbProfileId]].transform
        val query: Fragment =
          sql"""
            SELECT profileId
            FROM $favoriteProfiles
            WHERE recruiterId = $dbRecruiterId AND ${Fragments.in(fr"profileId", dbProfileIds)}
            """

        query.query[DbProfileId].to[List].map(_.into[List[ProfileId]].transform).transact(xa)

      override def getFavoritesForRecruiter(recruiterId: RecruiterId, page: Long = 1, limit: Long = 10): F[PaginatedProfileIds] = {
        val id = getRecruiterDbId(recruiterId)
        val offset = (page - 1) * limit

        val profileIdQuery = sql"""
            SELECT profileId
            FROM $favoriteProfiles
            WHERE recruiterId = $id
            ORDER BY timestamp
            LIMIT $limit OFFSET $offset
          """
          .query[DbProfileId]
          .to[List]
          .map(_.into[List[ProfileId]].transform)

        val totalQuery = sql"""
            SELECT COUNT(*)
            FROM $favoriteProfiles
            WHERE recruiterId = $id
          """.query[Long].unique

        (for {
          profileIds <- profileIdQuery
          total      <- totalQuery
        } yield PaginatedProfileIds(profileIds, total)).transact(xa)
      }

      private def getRecruiterDbId(recruiterId: RecruiterId) = recruiterId.into[DbRecruiterId].transform

      private def getProfileDbId(profileId: ProfileId) = profileId.into[DbProfileId].transform
    }

  def resource[F[_]: {MonadCancelThrow, LoggerFactory}](
    tableName: TableName,
    transactor: Transactor[F]
  ): Resource[F, FavoritesRepository[F]] =
    Resource.eval(for {
      repo <- MysqlFavoritesRepository.impl[F](tableName, transactor).pure
      _    <- repo.initialize
    } yield repo)
}
