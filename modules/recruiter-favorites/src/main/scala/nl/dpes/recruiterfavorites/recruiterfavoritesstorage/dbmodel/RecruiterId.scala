package nl.dpes.recruiterfavorites.recruiterfavoritesstorage.dbmodel

import doobie.Meta
import io.scalaland.chimney.Transformer
import nl.dpes.recruiterfavorites.service.domainmodel.RecruiterId as DomainRecruiterId

case class RecruiterId(value: String)

object RecruiterId {
  given Meta[RecruiterId] = Meta[String].imap(apply)(_.value)
  given Transformer[RecruiterId, DomainRecruiterId] = id => DomainRecruiterId(id.value)
  given Transformer[DomainRecruiterId, RecruiterId] = id => RecruiterId(id.value)
}
