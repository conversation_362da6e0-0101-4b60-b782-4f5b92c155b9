package nl.dpes.recruiterfavorites.recruiterfavoritesstorage.config

import doobie.Fragment
import nl.dpes.recruiterfavorites.recruiterfavoritesstorage.config.FavoriteProfilesConfig.TableName
import pureconfig.ConfigReader

case class FavoriteProfilesConfig(tableName: TableName) derives ConfigReader

object FavoriteProfilesConfig {

  case class TableName(value: String)

  given ConfigReader[TableName] = ConfigReader[String].map(TableName.apply)

  given Conversion[TableName, Fragment] = tableName => Fragment.const(tableName.value)
}
