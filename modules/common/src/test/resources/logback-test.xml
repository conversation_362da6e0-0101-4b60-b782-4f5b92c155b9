<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Set root logger to ERROR to reduce noise significantly -->
    <root level="ERROR">
        <appender-ref ref="STDOUT" />
    </root>

    <!-- Completely silence these noisy loggers -->
    <logger name="org.testcontainers" level="OFF" />
    <logger name="com.zaxxer.hikari" level="OFF" />
    <logger name="tc." level="OFF" />
    <logger name="PROFILE ACCESS LOG" level="OFF" />

    <!-- Keep only ERROR level for application loggers -->
    <logger name="nl.dpes" level="ERROR" />
</configuration>
