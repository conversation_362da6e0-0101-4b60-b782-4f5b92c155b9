package nl.dpes.savedsearchsender.service

import nl.dpes.profilesearch.service.model.profilesearch.SearchResult
import nl.dpes.savedsearch.domain.SavedSearch
import nl.dpes.savedsearchsender.domain.{Email<PERSON>ddress, SavedSearchProfiles}

trait MailSender[F[_]] {
  def sendSavedSearchEmail(recipient: <PERSON>ail<PERSON><PERSON><PERSON>, savedSearch: SavedSearch, searchResult: SavedSearchProfiles): F[Unit]
}
