package nl.dpes.savedsearchsender.config

import nl.dpes.profilesearch.config.{BlazeServerConfig, ElasticsearchConfig}
import nl.dpes.profilesearch.creditservice.CreditServiceConfig
import nl.dpes.profilesearch.database.config.DbConfig
import nl.dpes.profilesearch.service.lock.LockConfig
import nl.dpes.profilesearch.service.unlockedprofiles.UnlockedProfileConfig
import nl.dpes.profilesearch.service.viewedprofiles.ViewedProfilesConfig
import nl.dpes.profilesearch.service.visitedprofiles.VisitedProfilesConfig
import nl.dpes.savedsearch.config.SavedSearchConfig
import pureconfig.ConfigReader

final case class AppConfig(
  databaseConfig: DbConfig,
  elasticsearchConfig: ElasticsearchConfig,
  savedSearchConfig: SavedSearchConfig,
  salesforceGatewayConfig: SalesforceGatewayConfig,
  mailServiceConfig: MailServiceConfig
) derives ConfigReader
