package nl.dpes.savedsearchsender.mail

import cats.effect.Async
import cats.implicits.*
import cats.{<PERSON><PERSON>, MonadThrow}
import nl.dpes.savedsearch.domain.SavedSearch
import nl.dpes.savedsearchsender.domain.{EmailAddress, SavedSearchProfiles}
import nl.dpes.savedsearchsender.mail.Mail.given
import nl.dpes.savedsearchsender.service.MailSender
import org.typelevel.log4cats.Logger
import sttp.client3.circe.*
import sttp.client3.{basicRequest, SttpBackend, UriContext}
import sttp.model.{Header, MediaType}

class MockHttpMailServiceClient[F[_]: {MonadThrow, Logger}](baseUrl: String) extends MailSender[F] {

  override def sendSavedSearchEmail(
    recipient: EmailAddress,
    savedSearch: SavedSearch,
    searchResult: SavedSearchProfiles
  ): F[Unit] = {
    val request = basicRequest
      .put(uri"$baseUrl/mail")
      .headers(Header.contentType(MediaType.ApplicationJson))
      .body(Mail.NewSavedSearchEmail(recipient, savedSearch, searchResult))
      .show(includeBody = true)

    Logger[F].info(s"[Dry Run] Request details: $request")
  }
}
