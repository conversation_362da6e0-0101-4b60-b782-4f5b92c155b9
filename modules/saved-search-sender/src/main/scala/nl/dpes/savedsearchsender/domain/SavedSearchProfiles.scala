package nl.dpes.savedsearchsender.domain

import nl.dpes.profilesearch.service.model.dbmodel.FacetsResult.TermBucket
import nl.dpes.profilesearch.service.model.profilesearch.ProfileSearchResult

case class SavedSearchProfiles(
  profiles: List[ProfileSearchResult],
  totalNumberOfProfiles: Long
)

object SavedSearchProfiles {
  def fromSearchResult(searchResult: nl.dpes.profilesearch.service.model.profilesearch.SearchResult): SavedSearchProfiles =
    SavedSearchProfiles(
      profiles = searchResult.profiles,
      totalNumberOfProfiles = searchResult.totalNumberOfProfiles
    )
}
