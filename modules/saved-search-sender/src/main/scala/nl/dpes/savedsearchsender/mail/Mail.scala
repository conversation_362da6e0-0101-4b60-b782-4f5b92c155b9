package nl.dpes.savedsearchsender.mail

import io.circe.generic.semiauto.deriveEncoder
import io.circe.syntax.EncoderOps
import io.circe.{Encoder, Json}
import nl.dpes.profilesearch.service.model.profilesearch.ProfileSearchResult
import nl.dpes.savedsearch.domain.SavedSearch
import nl.dpes.savedsearchsender.domain.{EmailAddress, SavedSearchProfiles}
import nl.dpes.savedsearchsender.mail.Mail.MailParameters

import java.time.LocalDateTime
import scala.collection.immutable.{AbstractMap, SeqMap, SortedMap}

sealed trait Mail {
  val `type`: String
  val recipient: EmailAddress
  val parameters: MailParameters
  val subAccountId: Option[String] = None
  val metaData: Map[String, String] = Map.empty
  val sendAt: Option[LocalDateTime] = None
}

object Mail {
  sealed trait MailParameters
  case class NewSavedSearchEmailParameters(
    recruiter: Map[String, String],
    candidateCount: Long,
    search: Map[String, String],
    jobSeekers: List[NewSavedSearchJobSeeker]
  ) extends MailParameters

  case class NewSavedSearchJobSeeker(
    id: String,
    functionName: String,
    experiences: List[String],
    desiredJobs: List[String],
    workLevels: String,
    workingHours: String,
    availability: String,
    city: String,
    site_nvb: Int
  )

  case class NewSavedSearchEmail(
    recruiterEmail: EmailAddress,
    savedSearch: SavedSearch,
    savedSearchResult: SavedSearchProfiles
  ) extends Mail {
    override val `type`: String = "recruiter saved search nvb"

    override val parameters: MailParameters = NewSavedSearchEmailParameters(
      recruiter = Map(
        "id" -> savedSearch.recruiterId.value
      ),
      candidateCount = savedSearchResult.totalNumberOfProfiles,
      search = Map(
        "id"   -> savedSearch.id.toString,
        "name" -> savedSearch.name.value
      ),
      jobSeekers = savedSearchResult.profiles.map { (profile: ProfileSearchResult) =>
        NewSavedSearchJobSeeker(
          id = profile.id.value,
          functionName = profile.experiences.headOption.map(_.toString).getOrElse(""),
          experiences = profile.experiences.slice(0, 3).map(_.toString),
          desiredJobs = profile.preferredJobs.map(_.toString),
          workLevels = profile.workLevels.map(_.value).mkString(", "),
          workingHours = profile.workingHours.map(value => s"van ${value.min} tot ${value.max}").getOrElse(""),
          availability = profile.availability.map(_.value).getOrElse(""),
          city = profile.city.map(_.value).getOrElse(""),
          site_nvb = 1
        )
      }
    )

    override val recipient: EmailAddress = recruiterEmail
  }

  given Encoder[NewSavedSearchJobSeeker] = deriveEncoder[NewSavedSearchJobSeeker]
  given Encoder[NewSavedSearchEmailParameters] = deriveEncoder[NewSavedSearchEmailParameters]

  given Encoder[NewSavedSearchEmail] = new Encoder[NewSavedSearchEmail] {
    override def apply(a: NewSavedSearchEmail): Json = Json.fromFields(
      List(
        "type"      -> Json.fromString(a.`type`),
        "recipient" -> Json.fromString(a.recipient.value),
        "parameters" -> (a.parameters match {
          case p: NewSavedSearchEmailParameters => p.asJson
        }),
        "subAccountId" -> a.subAccountId
          .map(Json.fromString)
          .getOrElse(Json.Null),
        "metaData" -> Json.fromFields(a.metaData.map { case (k, v) => (k, Json.fromString(v)) }),
        "sendAt" -> a.sendAt
          .map(dateTime => Json.fromString(dateTime.toString))
          .getOrElse(Json.Null)
      )
    )
  }
}
