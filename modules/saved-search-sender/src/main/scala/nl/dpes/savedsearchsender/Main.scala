package nl.dpes.savedsearchsender

import cats.Monad
import cats.effect.kernel.{Clock, MonadCancelThrow}
import cats.effect.{Async, ExitCode, IO, IOApp, Resource}
import com.sksamuel.elastic4s.http.JavaClient
import com.sksamuel.elastic4s.{ElasticClient, ElasticNodeEndpoint, ElasticProperties, Executor, Functor}
import nl.dpes.profilesearch.config.ElasticsearchConfig
import nl.dpes.profilesearch.config.Syntax.loadF
import nl.dpes.profilesearch.database.Database
import nl.dpes.profilesearch.service.lock.{Key, LockManager}
import nl.dpes.profilesearch.service.model.profilesearch.{LastViewedDate, ProfileId, SearchResult}
import nl.dpes.profilesearch.service.viewedprofiles.ViewedProfilesService
import nl.dpes.profilesearch.service.visitedprofiles.{ProfileView, VisitedProfilesService}
import nl.dpes.savedsearch.config.SavedSearchConfig
import nl.dpes.savedsearch.domain.{Frequency, RecruiterId}
import nl.dpes.savedsearchsender.config.AppConfig
import nl.dpes.savedsearchsender.service.SavedSearchSenderService
import org.typelevel.log4cats.{Logger, LoggerFactory, SelfAwareStructuredLogger, StructuredLogger}
import org.typelevel.log4cats.slf4j.{Slf4jFactory, Slf4jLogger}
import pureconfig.ConfigSource
import com.sksamuel.elastic4s.cats.effect.instances.*
import io.grpc.{ManagedChannel, ManagedChannelBuilder}
import nl.dpes.b2b.salesforce.v1.recruiter_service.RecruiterServiceGrpc
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpes.savedsearchsender.domain.{EmailAddress, SavedSearchProfiles}
import nl.dpes.savedsearchsender.mail.{HttpMailServiceClient, MockHttpMailServiceClient}
import nl.dpes.savedsearchsender.recruiter.SalesForceGatewayRecruiterRepository
import sttp.client3.httpclient.cats.HttpClientCatsBackend

object Main extends IOApp {
  given logger: SelfAwareStructuredLogger[IO] =
    Slf4jLogger.getLoggerFromName[IO]("SavedSearchSender")
  given LoggerFactory[IO] = Slf4jFactory.create[IO]

  override def run(args: List[String]): IO[ExitCode] =
    program[IO]
      .use(_ => IO.unit)
      .as(ExitCode.Success)

  private def program[F[+_]: {Async, StructuredLogger, LoggerFactory, Executor, Functor, Clock}] = {
    val logger = StructuredLogger[F]

    for {
      startTime     <- Resource.eval(Clock[F].realTimeInstant)
      frequency     <- Resource.eval(Async[F].fromEither(Frequency.fromString(sys.env("FREQUENCY"))))
      _             <- Resource.eval(logger.info(s"Starting Saved Search Sender for the $frequency frequency..."))
      appConfig     <- Resource.eval(ConfigSource.default.loadF[F, AppConfig])
      dbTransactor  <- Database.resource(appConfig.databaseConfig)
      elasticClient <- createElasticClient(appConfig.elasticsearchConfig)

      savedSearchRepository <- nl.dpes.savedsearch.storage.MySqlSavedSearchRepository
        .resource(appConfig.savedSearchConfig.tableName, dbTransactor)
      profileSearchService <- nl.dpes.profilesearch.service.ProfileSearchService
        .resource[F](
          appConfig.elasticsearchConfig.index,
          elasticClient,
          fakeVisitedProfilesService,
          fakeViewedProfilesService,
          fakeLockManager
        )

      grpcChannel <- createGrpcClient[F](
        appConfig.salesforceGatewayConfig.host,
        appConfig.salesforceGatewayConfig.port
      )
      salesForceGatewayRecruiterRepository = SalesForceGatewayRecruiterRepository[F](RecruiterServiceGrpc.stub(grpcChannel))
      sttpBackend    <- HttpClientCatsBackend.resource[F]()
      httpMailSender <- Resource.pure(new HttpMailServiceClient[F](appConfig.mailServiceConfig.baseUrl, sttpBackend))

      savedSearchSender <- SavedSearchSenderService.resource(
        savedSearchRepository,
        profileSearchService,
        salesForceGatewayRecruiterRepository,
        httpMailSender
      )

      _ <- Resource.eval(
        savedSearchSender.send(frequency)
      )

      endTime <- Resource.eval(Clock[F].realTimeInstant)
      elapsedTime = endTime.minusMillis(startTime.toEpochMilli).toEpochMilli
      _ <- Resource.eval(
        logger
          .addContext(Map("elapsedTimeMs" -> elapsedTime.toString))
          .info("Finished successfully.")
      )
    } yield ()
  }

  // These services are not used in this application, so we provide fake implementations (instead of refactoring the whole ProfileSearchService).
  private def fakeVisitedProfilesService[F[_]: MonadCancelThrow]: VisitedProfilesService[F] =
    new VisitedProfilesService[F] {
      override def registerProfileView(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[ProfileView] =
        MonadCancelThrow[F].raiseError(new NotImplementedError("This method is not implemented for this application."))
    }

  private def fakeViewedProfilesService[F[_]: Monad]: ViewedProfilesService[F] =
    new ViewedProfilesService[F] {
      override def getLastViewedDate(
        recruiterId: profilesearch.RecruiterId,
        profileId: profilesearch.ProfileId
      ): F[Option[LastViewedDate]] =
        Monad[F].pure(None)

      override def save(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit] = Monad[F].unit
    }

  private def fakeLockManager[F[_]: MonadCancelThrow]: LockManager[F] =
    new LockManager[F] {
      override def runInLock[A](key: Key, fa: F[A]): F[A] =
        MonadCancelThrow[F].raiseError(new NotImplementedError("This method is not implemented for this application."))
    }

  private def createElasticClient[F[_]: Async](elasticsearchConfig: ElasticsearchConfig) =
    Resource.fromAutoCloseable[F, ElasticClient] {
      val elasticNodeEndpoint: ElasticNodeEndpoint =
        ElasticNodeEndpoint(elasticsearchConfig.hostScheme, elasticsearchConfig.host, elasticsearchConfig.port, None)
      Async[F].delay(ElasticClient(JavaClient(ElasticProperties(Seq(elasticNodeEndpoint)))))
    }

  private def createGrpcClient[F[_]: Async](host: String, port: Int): Resource[F, ManagedChannel] =
    Resource.make {
      Async[F].delay(
        ManagedChannelBuilder.forAddress(host, port).usePlaintext().build()
      )
    }(client => Async[F].delay(client.shutdown()))
}
