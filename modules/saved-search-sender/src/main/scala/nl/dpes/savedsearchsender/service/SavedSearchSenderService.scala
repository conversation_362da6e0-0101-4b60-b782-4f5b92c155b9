package nl.dpes.savedsearchsender.service

import cats.implicits.*
import cats.effect.kernel.{Async, Resource}
import nl.dpes.profilesearch.domain.filter
import nl.dpes.profilesearch.domain.filter.UpdateDateFilter
import nl.dpes.profilesearch.service.ProfileSearchService
import nl.dpes.profilesearch.service.model.profilesearch.SearchResult
import nl.dpes.profilesearch.service.model.profilesearch.pagination.Pagination
import nl.dpes.savedsearch.domain.{Frequency, SavedSearch, SavedSearchFilters}
import nl.dpes.savedsearch.service.SavedSearchRepository
import nl.dpes.savedsearchsender.domain.SavedSearchProfiles
import org.typelevel.log4cats.Logger
import nl.dpes.profilesearch.domain.UpdateDate

trait SavedSearchSenderService[F[_]: {Async, Logger}] {
  def send(frequency: Frequency): F[Unit]
}

object SavedSearchSenderService {
  def impl[F[_]: {Async, Logger}](
    savedSearchRepository: SavedSearchRepository[F],
    profileSearchService: ProfileSearchService[F],
    recruiterRepository: RecruiterRepository[F],
    mailSender: MailSender[F]
  ): SavedSearchSenderService[F] =
    new SavedSearchSenderService[F] {
      override def send(frequency: Frequency): F[Unit] =
        for {
          _                      <- Logger[F].info(s"Sending saved searches with $frequency frequency for all recruiters...")
          allSavedSearchesToSend <- savedSearchRepository.getAllByFrequency(frequency)
          // todo: remove it once tested
          onlyTestSavedSearches = allSavedSearchesToSend
            .filter(savedSearch =>
              List(
                "0038E00001JRcmTQAT",
                "0038E00001W8YmcQAF",
                "recruiter-123",
                "recruiter2",
                "0034H0000270RVcQAM" // rodrigo pro
              )
                .contains(savedSearch.recruiterId)
            )

          _ <- Logger[F].info(s"Found ${allSavedSearchesToSend.size} saved searches to send")
          _ <- onlyTestSavedSearches.traverse { savedSearch =>
            val tryToProcessTheSaveSearch = processRecruiterSavedSearch(savedSearch).attempt

            tryToProcessTheSaveSearch.flatMap {
              case Left(e) =>
                Logger[F].error(e)(
                  s"Failed to process saved search '${savedSearch.name.value}' for recruiter '${savedSearch.recruiterId.value}'"
                )
              case Right(_) => Async[F].unit
            }
          }
        } yield ()

      private def processRecruiterSavedSearch(savedSearch: SavedSearch): F[Unit] =
        for {
          _ <- Logger[F].info(
            s"Processing saved search '${savedSearch.name.value}' for recruiter '${savedSearch.recruiterId.value}'"
          )
          searchResult <- profileSearchService.getProfiles(
            pagination = Pagination.default,
            filters = toDomainFilters(savedSearch.filters, savedSearch.frequency),
            withoutAggregations = true
          )
          _ <-
            if (searchResult.totalNumberOfProfiles > 0)
              sendSavedSearchResultsToRecruiter(savedSearch, searchResult)
            else
              Logger[F].info(
                s"No profiles found for saved search '${savedSearch.name.value}' for recruiter '${savedSearch.recruiterId.value}'. Skipping."
              )
        } yield ()

      private def sendSavedSearchResultsToRecruiter(savedSearch: SavedSearch, searchResult: SearchResult): F[Unit] =
        for {
          recruiterEmailAddress <- recruiterRepository
            .getRecruiterEmail(savedSearch.recruiterId)
            .handleErrorWith { e =>
              Logger[F].error(e)(s"Failed to get email address for recruiter '${savedSearch.recruiterId.value}'") *> Async[F].raiseError(e)
            }

          _ <- Logger[F].info(
            s"Sending ${searchResult.totalNumberOfProfiles} search results for saved search '${savedSearch.name.value}' to recruiter '${savedSearch.recruiterId.value}' with email address '${recruiterEmailAddress.value}'"
          )
          _ <- mailSender.sendSavedSearchEmail(recruiterEmailAddress, savedSearch, SavedSearchProfiles.fromSearchResult(searchResult))
          _ <- Logger[F].info(
            s"Sent ${searchResult.totalNumberOfProfiles} search results for saved search '${savedSearch.name.value}' to recruiter '${savedSearch.recruiterId.value}'"
          )
        } yield ()
    }

  private def toDomainFilters(savedSearchFilters: SavedSearchFilters, frequency: Frequency): List[filter.Filter] =
    savedSearchFilters.searchTerm.toList ++
      savedSearchFilters.city.toList ++
      savedSearchFilters.geoDistance.toList ++
      savedSearchFilters.provinces.toList ++
      savedSearchFilters.functionGroups.toList ++
      savedSearchFilters.workLevels.toList ++
      savedSearchFilters.workingHours.toList ++
      savedSearchFilters.careerLevels.toList ++
      savedSearchFilters.requestedSalaries.toList ++
      savedSearchFilters.availabilities.toList ++
      savedSearchFilters.driversLicenses.toList ++
      savedSearchFilters.languages.toList ++
      getUpdatedDateByFrequency(frequency).map(UpdateDateFilter(_)).toList

  private def getUpdatedDateByFrequency(frequency: Frequency) = frequency match {
    case Frequency.Daily  => Some(UpdateDate.Last24Hours)
    case Frequency.Weekly => Some(UpdateDate.LastWeek)
    case _                => None
  }

  def resource[F[_]: {Async, Logger}](
    savedSearchRepository: SavedSearchRepository[F],
    profileSearchService: ProfileSearchService[F],
    recruiterRepository: RecruiterRepository[F],
    mailSender: MailSender[F]
  ): Resource[F, SavedSearchSenderService[F]] =
    Resource.pure(SavedSearchSenderService.impl(savedSearchRepository, profileSearchService, recruiterRepository, mailSender))
}
