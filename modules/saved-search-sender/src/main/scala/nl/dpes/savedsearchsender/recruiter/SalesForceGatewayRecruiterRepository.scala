package nl.dpes.savedsearchsender.recruiter

import cats.effect.Async
import cats.implicits.*
import io.grpc.ManagedChannelBuilder
import nl.dpes.b2b.salesforce.v1.recruiter_service.RecruiterServiceGrpc
import nl.dpes.b2b.salesforce.v1.recruiter_service.RecruiterServiceGrpc.RecruiterService
import nl.dpes.savedsearch.domain.RecruiterId
import nl.dpes.savedsearchsender.domain.EmailAddress
import nl.dpes.savedsearchsender.service.RecruiterRepository

class SalesForceGatewayRecruiterRepository[F[_]: Async](recruiterService: RecruiterService) extends RecruiterRepository[F] {
  override def getRecruiterEmail(recruiterId: RecruiterId): F[EmailAddress] = {
    val request = nl.dpes.b2b.salesforce.v1.recruiter_service.GetRecruiterEmailByIdRequest(contactId = recruiterId.value)
    Async[F].fromFuture(Async[F].pure(recruiterService.getRecruiterEmailById(request))).map { response =>
      EmailAddress(response.emailAddress)
    }
  }
}
