package nl.dpes.savedsearchsender.mail

import cats.{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>d<PERSON>hr<PERSON>}
import nl.dpes.profilesearch.service.model.profilesearch.SearchResult
import nl.dpes.savedsearch.domain.SavedSearch
import nl.dpes.savedsearchsender.domain.{<PERSON>ail<PERSON>ddress, SavedSearchProfiles}
import nl.dpes.savedsearchsender.service.MailSender
import sttp.client3.{basicRequest, SttpBackend, UriContext}
import sttp.model.{Header, MediaType}
import cats.implicits.*
import Mail.given
import sttp.client3.circe.*

class HttpMailServiceClient[F[_]: MonadThrow](baseUrl: String, sttpBackend: SttpBackend[F, ?]) extends MailSender[F] {

  override def sendSavedSearchEmail(
    recipient: <PERSON>ail<PERSON>dd<PERSON>,
    savedSearch: SavedSearch,
    searchResult: SavedSearchProfiles
  ): F[Unit] =
    for {
      response <- basicRequest
        .put(uri"$baseUrl/mail")
        .headers(Header.contentType(MediaType.ApplicationJson))
        .body(Mail.NewSavedSearchEmail(recipient, savedSearch, searchResult))
        .send(sttpBackend)

      _ <- response.code match {
        case sttp.model.StatusCode.Ok => Monad[F].unit
        case _ =>
          MonadThrow[F].raiseError(
            new Exception(
              s"Sending the mail failed with the code ${response.code.toString} and the message '${response.body.left.getOrElse("")}'"
            )
          )
      }
    } yield ()
}
