database-config {
  url = ${?DATABASE_CONNECTION_STRING}
  thread-count = 10
  username = ${?DATABASE_USER}
  password = ${?DATABASE_PASSWORD}
}

elasticsearch-config {
  host-scheme = "http"
  host-scheme = ${?ELASTICSEARCH_HOST_SCHEME}
  host = "elasticsearch"
  host = ${?ELASTICSEARCH_CLUSTER_HOST}
  port = "9200"
  port = ${?ELASTICSEARCH_CLUSTER_PORT}
  index = "cv_database"
  index = ${?ELASTICSEARCH_CV_DATABASE_INDEX}
}

salesforce-gateway-config {
  host = "localhost"
  host = ${?B2B_SALESFORCE_GATEWAY_SERVICE_HOST}
  port = 11310
}

saved-search-config {
  table-name = "saved_search"
}

mail-service-config {
  base-url = "http://localhost:11320"
  base-url = ${?MAIL_SERVICE_BASE_URL}
}
