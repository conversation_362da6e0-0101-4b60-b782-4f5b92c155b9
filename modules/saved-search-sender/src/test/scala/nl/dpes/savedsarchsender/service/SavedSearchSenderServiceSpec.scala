package nl.dpes.savedsarchsender.service

import cats.Monad
import cats.effect.IO
import com.fasterxml.jackson.databind.JsonSerializable.Base
import nl.dpes.profilesearch.service.ProfileSearchService
import nl.dpes.profilesearch.service.model.profilesearch.SearchResult
import nl.dpes.savedsarchsender.BaseSpec
import nl.dpes.savedsearch.domain.{Frequency, Name, RecruiterId, SavedSearch, SavedSearchFilters, SavedSearchId}
import nl.dpes.savedsearch.service.SavedSearchRepository
import nl.dpes.savedsearchsender.service.SavedSearchSenderService
import org.scalamock.stubs.Stubs
import org.typelevel.log4cats.{Logger, LoggerFactory}
import org.typelevel.log4cats.slf4j.Slf4jFactory
import org.typelevel.log4cats.{LoggerFactory, SelfAwareStructuredLogger}
import org.typelevel.log4cats.testing.StructuredTestingLogger
import org.typelevel.log4cats.testing.StructuredTestingLogger.*
import weaver.SimpleIOSuite

import java.util.UUID

object SavedSearchSenderServiceSpec extends SimpleIOSuite with Stubs with BaseSpec {
  test("It should send saved searches with the specified frequency") {
    given logger: StructuredTestingLogger[IO] = StructuredTestingLogger.impl[IO]()
    given loggerFactory: LoggerFactory[IO] = getLoggerFactory(logger)

    val frequency = Frequency.Daily

    val repository = stub[SavedSearchRepository[IO]]
    repository.getAllByFrequency.returns(_ => IO.pure(List(getTestSavedSearch)))

    val profileSearchService = stub[ProfileSearchService[IO]]
    profileSearchService.getProfiles.returns(_ => IO.pure(SearchResult(List.empty, Map.empty, 10)))

    val recruiterRepository = stub[nl.dpes.savedsearchsender.service.RecruiterRepository[IO]]
    recruiterRepository.getRecruiterEmail.returns(_ => IO.pure(nl.dpes.savedsearchsender.domain.EmailAddress("<EMAIL>")))

    val mailSender = stub[nl.dpes.savedsearchsender.service.MailSender[IO]]
    mailSender.sendSavedSearchEmail.returns((_, _, _) => IO.unit)

    SavedSearchSenderService.resource[IO](repository, profileSearchService, recruiterRepository, mailSender).use { service =>
      for {
        _    <- service.send(frequency)
        logs <- logger.logged.map(_.toList)
      } yield expect.same(
        Set(
          INFO("Sending saved searches with Daily frequency for all recruiters...", None, Map.empty),
          INFO("Found 1 saved searches to send", None, Map.empty),
          INFO("Processing saved search 'Test Saved Search' for recruiter 'recruiter-123'", None, Map.empty),
          INFO(
            "Sending 10 search results for saved search 'Test Saved Search' to recruiter 'recruiter-123' with email address '<EMAIL>'",
            None,
            Map.empty
          ),
          INFO("Sent 10 search results for saved search 'Test Saved Search' to recruiter 'recruiter-123'", None, Map.empty)
        ),
        logs.toSet
      )
    }
  }

  test("It should not send any emails if there are no saved search results") {
    given logger: StructuredTestingLogger[IO] = StructuredTestingLogger.impl[IO]()
    given loggerFactory: LoggerFactory[IO] = getLoggerFactory(logger)

    val frequency = Frequency.Daily

    val repository = stub[SavedSearchRepository[IO]]
    repository.getAllByFrequency.returns(_ => IO.pure(List(getTestSavedSearch)))

    val profileSearchService = stub[ProfileSearchService[IO]]
    profileSearchService.getProfiles.returns(_ => IO.pure(SearchResult(List.empty, Map.empty, 0)))

    val recruiterRepository = stub[nl.dpes.savedsearchsender.service.RecruiterRepository[IO]]
    recruiterRepository.getRecruiterEmail.returns(_ => IO.pure(nl.dpes.savedsearchsender.domain.EmailAddress("<EMAIL>")))
    val mailSender = stub[nl.dpes.savedsearchsender.service.MailSender[IO]]
    mailSender.sendSavedSearchEmail.returns((_, _, _) => IO.raiseError(new Exception("This should not be called")))

    SavedSearchSenderService.resource[IO](repository, profileSearchService, recruiterRepository, mailSender).use { service =>
      for {
        _    <- service.send(frequency)
        logs <- logger.logged.map(_.toList)
      } yield expect(
        logs.contains(
          INFO("No profiles found for saved search 'Test Saved Search' for recruiter 'recruiter-123'. Skipping.", None, Map.empty)
        )
      )
    }
  }

  test("It should handle errors when sending saved searches") {
    given logger: StructuredTestingLogger[IO] = StructuredTestingLogger.impl[IO]()
    given loggerFactory: LoggerFactory[IO] = getLoggerFactory(logger)
    val frequency = Frequency.Daily

    val repository = stub[SavedSearchRepository[IO]]
    repository.getAllByFrequency.returns(_ => IO.pure(List(getTestSavedSearch)))

    val profileSearchService = stub[ProfileSearchService[IO]]
    profileSearchService.getProfiles.returns(_ => IO.pure(SearchResult(List.empty, Map.empty, 10)))

    val recruiterRepository = stub[nl.dpes.savedsearchsender.service.RecruiterRepository[IO]]
    recruiterRepository.getRecruiterEmail.returns(_ => IO.pure(nl.dpes.savedsearchsender.domain.EmailAddress("<EMAIL>")))
    val mailSender = stub[nl.dpes.savedsearchsender.service.MailSender[IO]]
    mailSender.sendSavedSearchEmail.returnsOnCall {
      case 1 => IO.raiseError(new Exception("Failed to send email"))
      case _ => IO.unit
    }

    SavedSearchSenderService.resource[IO](repository, profileSearchService, recruiterRepository, mailSender).use { service =>
      for {
        _    <- service.send(frequency)
        logs <- logger.logged.map(_.toList)
      } yield expect(
        logs.exists {
          case StructuredTestingLogger.ERROR(message, throwOpt, ctx) =>
            message == "Failed to process saved search 'Test Saved Search' for recruiter 'recruiter-123'" &&
            throwOpt.exists(_.getMessage == "Failed to send email")
          case _ => false
        }
      )
    }
  }

  test("It should continue processing other saved searches if one fails") {
    given logger: StructuredTestingLogger[IO] = StructuredTestingLogger.impl[IO]()
    given loggerFactory: LoggerFactory[IO] = getLoggerFactory(logger)

    val frequency = Frequency.Daily

    val repository = stub[SavedSearchRepository[IO]]
    repository.getAllByFrequency.returns(_ =>
      IO.pure(List(getTestSavedSearch, getTestSavedSearch.copy(recruiterId = RecruiterId("recruiter2"))))
    )

    val profileSearchService = stub[ProfileSearchService[IO]]
    profileSearchService.getProfiles.returns(_ => IO.pure(SearchResult(List.empty, Map.empty, 10)))

    val recruiterRepository = stub[nl.dpes.savedsearchsender.service.RecruiterRepository[IO]]
    recruiterRepository.getRecruiterEmail.returns(_ => IO.pure(nl.dpes.savedsearchsender.domain.EmailAddress("<EMAIL>")))

    val mailSender = stub[nl.dpes.savedsearchsender.service.MailSender[IO]]
    mailSender.sendSavedSearchEmail.returnsOnCall {
      case 1 => IO.raiseError(new Exception("Failed to send email"))
      case _ => IO.unit
    }

    SavedSearchSenderService.resource[IO](repository, profileSearchService, recruiterRepository, mailSender).use { service =>
      for {
        _    <- service.send(frequency)
        logs <- logger.logged.map(_.toList)
      } yield expect(
        logs.count {
          case StructuredTestingLogger.INFO(message, _, _) if message.contains("Sent 10 search results for saved search") => true
          case _                                                                                                          => false
        } == 1 &&
          logs.exists {
            case StructuredTestingLogger.ERROR(message, throwOpt, ctx) =>
              message == "Failed to process saved search 'Test Saved Search' for recruiter 'recruiter-123'" &&
              throwOpt.exists(_.getMessage == "Failed to send email")
            case _ => false
          }
      )
    }
  }

  test("It should only log an error when failing to get recruiter mail") {
    given logger: StructuredTestingLogger[IO] = StructuredTestingLogger.impl[IO]()
    given loggerFactory: LoggerFactory[IO] = getLoggerFactory(logger)

    val frequency = Frequency.Daily

    val repository = stub[SavedSearchRepository[IO]]
    repository.getAllByFrequency.returns(_ => IO.pure(List(getTestSavedSearch)))

    val profileSearchService = stub[ProfileSearchService[IO]]
    profileSearchService.getProfiles.returns(_ => IO.pure(SearchResult(List.empty, Map.empty, 10)))

    val recruiterRepository = stub[nl.dpes.savedsearchsender.service.RecruiterRepository[IO]]
    recruiterRepository.getRecruiterEmail.returns(_ => IO.raiseError(new Exception("Failed to get recruiter email")))

    val mailSender = stub[nl.dpes.savedsearchsender.service.MailSender[IO]]
    mailSender.sendSavedSearchEmail.returns((_, _, _) => IO.unit)

    SavedSearchSenderService.resource[IO](repository, profileSearchService, recruiterRepository, mailSender).use { service =>
      for {
        _    <- service.send(frequency)
        logs <- logger.logged.map(_.toList)
      } yield expect(
        logs.exists {
          case StructuredTestingLogger.ERROR(message, throwOpt, ctx) =>
            message == "Failed to get email address for recruiter 'recruiter-123'" &&
            throwOpt.exists(_.getMessage == "Failed to get recruiter email")
          case _ => false
        }
      )
    }
  }
}
