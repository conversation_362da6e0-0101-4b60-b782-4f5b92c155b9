package nl.dpes.savedsarchsender.mail

import cats.effect.IO
import cats.mtl.implicits.toSetOps
import nl.dpes.savedsarchsender.BaseSpec
import nl.dpes.savedsarchsender.service.SavedSearchSenderServiceSpec.getLoggerFactory
import nl.dpes.savedsearch.domain.{Frequency, Name, RecruiterId, SavedSearch, SavedSearchFilters}
import nl.dpes.savedsearchsender.domain.{EmailAddress, SavedSearchProfiles}
import nl.dpes.savedsearchsender.mail.MockHttpMailServiceClient
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.testing.StructuredTestingLogger
import weaver.SimpleIOSuite

import java.util.UUID

object MockHttpMailServiceClientSpec extends SimpleIOSuite with BaseSpec {
  test("mock client should run without errors and log the request") {
    given logger: StructuredTestingLogger[IO] = StructuredTestingLogger.impl[IO]()
    given loggerFactory: LoggerFactory[IO] = getLoggerFactory(logger)
    val client = new MockHttpMailServiceClient[IO]("http://mock-mail-service")

    for {
      _ <- client
        .sendSavedSearchEmail(EmailAddress("<EMAIL>"), getTestSavedSearch, SavedSearchProfiles(List.empty, 10))
      logs <- logger.logged.map(_.toList)
    } yield expect(logs.exists(_.message.contains("Request details")))
  }

}
