package nl.dpes.savedsarchsender.mail

import cats.conversions.all.autoWidenFunctor
import cats.effect.IO
import nl.dpes.savedsearchsender.mail.HttpMailServiceClient
import weaver.{FunSuiteIO, SimpleIOSuite}
import sttp.client3.testing.*
import cats.effect.IO
import io.circe.Json
import io.circe.literal.json
import nl.dpes.savedsearch.domain.{Frequency, SavedSearchFilters}
import sttp.client3.{Response, SttpBackend}
import sttp.client3.impl.cats.implicits.*
import nl.dpes.savedsearch.domain.Frequency
import sttp.client3
import sttp.monad.MonadError

import java.util.UUID
import nl.dpes.profilesearch.service.model.profilesearch.*

object HttpMailServiceClientSpec extends SimpleIOSuite {
  private val emailAddress = nl.dpes.savedsearchsender.domain.EmailAddress("<EMAIL>")
  private val filters = SavedSearchFilters(
    searchTerm = None,
    city = None,
    geoDistance = None,
    provinces = None,
    updatedDate = None,
    functionGroups = None,
    workLevels = None,
    workingHours = None,
    careerLevels = None,
    requestedSalaries = None,
    availabilities = None,
    driversLicenses = None,
    languages = None
  )
  private val savedSearch = nl.dpes.savedsearch.domain.SavedSearch.from(
    id = UUID.fromString("123e4567-e89b-12d3-a456-************"),
    name = "Test Search",
    recruiterId = "123e4567-e89b-12d3-a456-************",
    frequency = Frequency.Daily,
    filters = filters
  )

  test("Sending a new saved search email returns 200 if successful") {
    val requests = scala.collection.mutable.ListBuffer.empty[client3.Request[?, ?]]

    val backend: SttpBackend[IO, Any] = SttpBackendStub[IO, Any](implicitly[MonadError[IO]])
      .whenRequestMatchesPartial {
        case r if r.uri.path == List("mail") && r.method == sttp.model.Method.PUT =>
          requests += r
          Response.ok(())
      }

    val client = HttpMailServiceClient[IO]("http://localhost:8080", backend)
    val expectedJson =
      json"""{
        "type": "recruiter saved search nvb",
        "recipient": "<EMAIL>",
        "parameters": {
          "recruiter": {
            "id": "123e4567-e89b-12d3-a456-************"
          },
          "candidateCount": 10,
          "search": {
            "id":  "123e4567-e89b-12d3-a456-************",
            "name":  "Test Search"
          },
          "jobSeekers": [{
              "id": "profile-1",
              "functionName": "Software Engineer",
              "experiences": ["Software Engineer"],
              "desiredJobs": ["Backend Developer"],
              "workLevels": "Senior",
              "workingHours": "van 32 tot 40",
              "availability": "Immediate",
              "city": "Amsterdam",
              "site_nvb": 1
          }]
        },
        "subAccountId": null,
        "metaData": {},
        "sendAt": null
      }"""

    for {
      _ <- client.sendSavedSearchEmail(
        emailAddress,
        savedSearch,
        nl.dpes.savedsearchsender.domain.SavedSearchProfiles(
          List(
            ProfileSearchResult(
              id = ProfileId.unsafe("profile-1"),
              experiences = List(Experience("Software Engineer")),
              preferredJobs = List(PreferredJob("Backend Developer")),
              workLevels = List(WorkLevel("Senior")),
              workingHours = Some(WorkingHours(32, 40)),
              availability = Some(Availability("Immediate")),
              name = Some(Name(FirstName("John"), LastName("Doe"))),
              city = Some(City("Amsterdam")),
              updatedDate = UpdatedDate(2L),
              photo = None,
              lastViewedDate = None
            )
          ),
          10L
        )
      )
      request = requests.toList.headOption.map(_.body.show)
    } yield expect.same(Some(expectedJson), request.map(parseRequest))
  }

  private def parseRequest(response: String) =
    io.circe.parser.parse(response.stripPrefix("string: ")).getOrElse(Json.Null)

  test("Sending a new saved search email returns an error if not successful") {
    val backend: SttpBackend[IO, Any] = SttpBackendStub[IO, Any](implicitly[MonadError[IO]]).whenAnyRequest
      .thenRespond(Response("Oops!", sttp.model.StatusCode.InternalServerError))

    val client = HttpMailServiceClient[IO]("http://localhost:8080", backend)

    for {
      result <- client
        .sendSavedSearchEmail(
          emailAddress,
          savedSearch,
          nl.dpes.savedsearchsender.domain.SavedSearchProfiles(List.empty, 10L)
        )
        .attempt
    } yield expect(result.isLeft)
      && expect.same("Sending the mail failed with the code 500 and the message 'Oops!'", result.left.map(_.getMessage).left.getOrElse(""))
  }
}
