package nl.dpes.savedsarchsender

import cats.Monad
import nl.dpes.savedsearch.domain.{Frequency, Name, RecruiterId, SavedSearch, SavedSearchFilters}
import org.typelevel.log4cats.{LoggerFactory, SelfAwareStructuredLogger}
import org.typelevel.log4cats.testing.StructuredTestingLogger

import java.util.UUID

trait BaseSpec {

  protected def getTestSavedSearch: SavedSearch =
    SavedSearch(
      id = UUID.fromString("123e4567-e89b-12d3-a456-************"),
      name = Name("Test Saved Search"),
      recruiterId = RecruiterId("recruiter-123"),
      frequency = Frequency.Daily,
      filters = SavedSearchFilters(
        searchTerm = None,
        city = None,
        geoDistance = None,
        provinces = None,
        updatedDate = None,
        functionGroups = None,
        workLevels = None,
        workingHours = None,
        careerLevels = None,
        requestedSalaries = None,
        availabilities = None,
        driversLicenses = None,
        languages = None
      )
    )

  protected def getLoggerFactory[F[_]: Monad](logger: StructuredTestingLogger[F]): LoggerFactory[F] = new LoggerFactory[F] {
    override def getLoggerFromName(name: String): SelfAwareStructuredLogger[F] = logger
    override def fromName(name: String): F[SelfAwareStructuredLogger[F]] = Monad[F].pure(getLoggerFromName(name))
  }
}
