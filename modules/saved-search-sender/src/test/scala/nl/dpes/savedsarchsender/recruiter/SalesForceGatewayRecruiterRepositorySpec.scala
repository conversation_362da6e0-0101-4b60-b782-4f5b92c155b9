package nl.dpes.savedsarchsender.recruiter

import cats.effect.IO
import nl.dpes.b2b.salesforce.v1.recruiter_service.*
import nl.dpes.savedsearchsender.recruiter.SalesForceGatewayRecruiterRepository
import org.scalamock.stubs.Stubs
import weaver.SimpleIOSuite

import scala.concurrent.Future

object SalesForceGatewayRecruiterRepositorySpec extends SimpleIOSuite with Stubs {
  test("The salesforce gateway recruiter repository should return the email address of a recruiter given its id") {
    val recruiterIdString = "1234"
    val recruiterId = nl.dpes.savedsearch.domain.RecruiterId(recruiterIdString)
    val emailAddressString = "<EMAIL>"
    val expectedEmailAddress = nl.dpes.savedsearchsender.domain.EmailAddress(emailAddressString)

    val request = GetRecruiterEmailByIdRequest(recruiterIdString)

    val recruiterService = stub[RecruiterServiceGrpc.RecruiterService]
    recruiterService.getRecruiterEmailById
      .returns(_ =>
        Future.successful(
          GetRecruiterEmailByIdResponse(emailAddressString)
        )
      )

    val repository = new SalesForceGatewayRecruiterRepository[IO](recruiterService)
    for {
      foundEmailAddress <- repository.getRecruiterEmail(recruiterId)
    } yield expect.same(expectedEmailAddress, foundEmailAddress)
  }
}
