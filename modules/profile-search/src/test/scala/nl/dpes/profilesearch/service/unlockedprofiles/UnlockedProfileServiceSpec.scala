package nl.dpes.profilesearch.service.unlockedprofiles

import cats.Monad
import cats.effect.IO
import cats.effect.testkit.TestControl
import org.scalamock.stubs.Stubs
import org.typelevel.log4cats.{LoggerFactory, SelfAwareStructuredLogger}
import org.typelevel.log4cats.testing.StructuredTestingLogger
import org.typelevel.log4cats.testing.StructuredTestingLogger.INFO
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.profilesearch
import weaver.SimpleIOSuite

import java.time.LocalDate
import scala.concurrent.duration.*

object UnlockedProfileServiceSpec extends SimpleIOSuite with Stubs {
  private val profileId = profilesearch.ProfileId.unsafe("id1")
  private val recruiterId = profilesearch.RecruiterId("1")

  test("A profile should be unlocked before the expiration date and be locked the next day") {
    val logger = StructuredTestingLogger.impl[IO]()
    given loggerFactory: LoggerFactory[IO] = getLoggerFactory(logger)

    val unlockedProfileRepository = stub[UnlockedProfileRepository[IO]]

    val expirationDate = IO.pure(Some(java.time.LocalDate.ofEpochDay(0)))

    unlockedProfileRepository.getExpirationDate
      .returns(_ => expirationDate)

    val service = UnlockedProfileService[IO](unlockedProfileRepository, 30)

    TestControl
      .executeEmbed {
        for {
          // The IO timer here is 0 (unix time)
          isUnlocked <- service.profileIsUnlocked(recruiterId, profileId)
          // Wait for some time
          _                     <- IO.sleep(1.day)
          isUnlockedAfterOneDay <- service.profileIsUnlocked(recruiterId, profileId)
          logs                  <- logger.logged.map(_.toList)
        } yield expect(
          isUnlocked && !isUnlockedAfterOneDay && logs == List(
            INFO(s"Recruiter '$recruiterId' has an unlocked profile '${profileId.value}'", None, Map.empty)
          )
        )
      }
  }

  test("A profile should be unlocked for 30 days") {
    val logger = StructuredTestingLogger.impl[IO]()
    given loggerFactory: LoggerFactory[IO] = getLoggerFactory(logger)

    val unlockedProfileRepository = stub[UnlockedProfileRepository[IO]]
    unlockedProfileRepository.setExpirationDate.returns(_ => IO.unit)

    val now = java.time.LocalDate.ofEpochDay(0)

    val service = UnlockedProfileService[IO](unlockedProfileRepository, 30)

    TestControl
      .executeEmbed {
        for {
          _ <- service.unlockProfile(recruiterId, profileId)
          setExpirationDateCalls = unlockedProfileRepository.setExpirationDate.calls
          logs <- logger.logged.map(_.toList)
        } yield expect(
          setExpirationDateCalls == List((recruiterId, profileId, now.plusDays(30))) && logs == List(
            INFO(s"Recruiter '$recruiterId' has unlocked a profile '${profileId.value}'", None, Map.empty)
          )
        )
      }
  }

  private def getLoggerFactory[F[_]: Monad](logger: StructuredTestingLogger[F]) = new LoggerFactory[F] {
    override def getLoggerFromName(name: String): SelfAwareStructuredLogger[F] = logger
    override def fromName(name: String): F[SelfAwareStructuredLogger[F]] = Monad[F].pure(getLoggerFromName(name))
  }
}
