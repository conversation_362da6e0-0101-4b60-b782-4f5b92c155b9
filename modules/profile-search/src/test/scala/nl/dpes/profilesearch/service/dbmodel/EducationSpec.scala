package nl.dpes.profilesearch
package service.dbmodel

import nl.dpes.profilesearch.service.model.dbmodel.{Education, Month, MonthOfYear, Year}
import weaver.FunSuite

object EducationSpec extends FunSuite {
  test("An education starting after another one will be first in the list") {
    val unsorted = List(
      Education(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none, none),
      Education(MonthOfYear(Month(1).some, Year(2002)).some, MonthOfYear(Month(10).some, Year(2002)).some, none, none, none, none)
    )
    val sorted = List(
      Education(MonthOfYear(Month(1).some, Year(2002)).some, MonthOfYear(Month(10).some, Year(2002)).some, none, none, none, none),
      Education(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none, none)
    )

    expect(unsorted.sorted == sorted)
  }

  test("An education starting during another one but ending after it will be first in the list") {
    val unsorted = List(
      Education(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none, none),
      Education(MonthOfYear(Month(5).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2002)).some, none, none, none, none)
    )
    val sorted = List(
      Education(MonthOfYear(Month(5).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2002)).some, none, none, none, none),
      Education(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none, none)
    )

    expect(unsorted.sorted == sorted)
  }

  test("An education starting during another one and ending before it will be last in the list") {
    val unsorted = List(
      Education(MonthOfYear(Month(5).some, Year(2001)).some, MonthOfYear(Month(9).some, Year(2001)).some, none, none, none, none),
      Education(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none, none)
    )
    val sorted = List(
      Education(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none, none),
      Education(MonthOfYear(Month(5).some, Year(2001)).some, MonthOfYear(Month(9).some, Year(2001)).some, none, none, none, none)
    )

    expect(unsorted.sorted == sorted)
  }

  test("An education without a start date will be last in the list") {
    val unsorted = List(
      Education(none, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none, none),
      Education(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none, none)
    )
    val sorted = List(
      Education(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none, none),
      Education(none, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none, none)
    )

    expect(unsorted.sorted == sorted)
  }

  test("An education without an end date will be first in the list") {
    val unsorted = List(
      Education(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none, none),
      Education(MonthOfYear(Month(1).some, Year(2001)).some, none, none, none, none, none)
    )
    val sorted = List(
      Education(MonthOfYear(Month(1).some, Year(2001)).some, none, none, none, none, none),
      Education(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none, none)
    )

    expect(unsorted.sorted == sorted)
  }
}
