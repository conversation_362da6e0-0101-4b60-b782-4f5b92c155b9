package nl.dpes.profilesearch.service.lock

import weaver.FunSuite

import scala.util.*

object LockConfigSpec extends FunSuite {
  val configString: String =
    """
      |valid-lock {
      |  maximum-concurrent-locks = 10
      |  lock-timeout = 5 seconds
      |}
      |
      |invalid-lock {
      |  maximum-concurrent-locks = 0
      |  lock-timeout = 5 seconds
      |}
      |""".stripMargin

  test("LockConfig should parse valid configuration") {
    val config = pureconfig.ConfigSource.string(configString).at("valid-lock").loadOrThrow[LockConfig]

    expect(config.maximumConcurrentLocks.value == 10) &&
    expect(config.lockTimeout.toSeconds == 5)
  }

  test("LockConfig should fail to parse invalid configuration") {
    val config = Try(pureconfig.ConfigSource.string(configString).at("invalid-lock").loadOrThrow[LockConfig])

    config match {
      case Success(_) => failure("Expected LockConfig to fail parsing with invalid configuration")
      case Failure(exception) =>
        expect(exception.isInstanceOf[LockConfig.LockConfigError]) &&
        expect(exception.getMessage.contains("Lock limit must be greater than zero"))
    }
    expect(true)
  }
}
