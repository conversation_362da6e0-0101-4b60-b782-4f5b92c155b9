package nl.dpes.profilesearch.testcontainers

import cats.effect.{IO, Resource}
import com.sksamuel.elastic4s.http.JavaClient
import com.sksamuel.elastic4s.{ElasticClient, ElasticNodeEndpoint, ElasticProperties}
import org.testcontainers.containers.GenericContainer
import org.testcontainers.utility.DockerImageName
import sttp.client3.{basicRequest, HttpURLConnectionBackend, UriContext}

import scala.concurrent.duration.DurationInt
import scala.jdk.DurationConverters.ScalaDurationOps
import org.elasticsearch.client.RestClient
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder
import org.apache.http.impl.client.HttpClientBuilder
import org.apache.http.client.config.RequestConfig
import org.elasticsearch.client.RestClientBuilder
import org.apache.http.HttpHost

class ElasticSearchContainer()
    extends GenericContainer[ElasticSearchContainer](
      DockerImageName.parse("docker.elastic.co/elasticsearch/elasticsearch:8.8.2")
    ) {
  addEnv("ES_JAVA_OPTS", "-Xmx256m")
  addEnv("discovery.type", "single-node")
  addEnv("xpack.security.enabled", "false")
  addExposedPort(9200)

  def isReady: IO[Boolean] = {
    val backend = HttpURLConnectionBackend()
    val request = basicRequest.get(uri"http://$getHost:${getMappedPort(9200)}/_cluster/health")
    IO.delay(request.send(backend).isSuccess)
  }
}

object ElasticSearchContainer {
  val cluster: Resource[IO, ElasticSearchContainer] = {
    val createAndStartContainer = {
      val container = new ElasticSearchContainer()
        .withReuse(false)
        .withStartupTimeout(60.seconds.toJava)
      container.start()
      container
    }

    def waitForReadiness(container: ElasticSearchContainer): IO[Unit] =
      IO.sleep(5.seconds) *> container.isReady.flatMap {
        case true  => IO.unit
        case false => waitForReadiness(container)
      }

    Resource.make(IO.delay(createAndStartContainer).flatTap(waitForReadiness))(container => IO.delay(container.stop()))
  }

  def elasticClient: Resource[IO, ElasticClient] = for {
    cl <- cluster
    client <- Resource.eval[IO, ElasticClient] {
      val builder = RestClient
        .builder(new HttpHost(cl.getHost, cl.getMappedPort(9200)))
        .setRequestConfigCallback(new RestClientBuilder.RequestConfigCallback {
          override def customizeRequestConfig(requestConfigBuilder: RequestConfig.Builder): RequestConfig.Builder =
            requestConfigBuilder
              .setConnectTimeout(10000) // time to establish connection
              .setSocketTimeout(120000) // time waiting for data
        })
        .build()

      IO(ElasticClient(JavaClient.fromRestClient(builder)))
    }
  } yield client
}
