package nl.dpes.profilesearch

import cats.*
import cats.effect.*
import cats.implicits.*
import com.sksamuel.elastic4s.ElasticClient
import com.sksamuel.elastic4s.cats.effect.instances.*
import doobie.util.transactor.Transactor
import nl.dpes.profilesearch.creditservice.{CreditService, EntitlementId}
import nl.dpes.profilesearch.service.lock.LockConfig.LockLimit
import nl.dpes.profilesearch.service.lock.{<PERSON><PERSON><PERSON><PERSON><PERSON>, LockManager, MysqlLockService}
import nl.dpes.profilesearch.service.model.profilesearch.RecruiterId
import nl.dpes.profilesearch.service.model.profilesearch.pagination.*
import nl.dpes.profilesearch.service.unlockedprofiles.{UnlockedProfileConfig, UnlockedProfileRepository, UnlockedProfileService}
import nl.dpes.profilesearch.service.viewedprofiles.{ViewedProfilesConfig, ViewedProfilesRepository, ViewedProfilesService}
import nl.dpes.profilesearch.service.visitedprofiles.{
  ProfileViewLimit,
  VisitedProfilesConfig,
  VisitedProfilesRepository,
  VisitedProfilesService
}
import nl.dpes.profilesearch.service.{ProfileSearchService, TableName}
import nl.dpes.profilesearch.testcontainers.DataGenerator
import nl.dpes.profilesearch.testcontainers.MySqlDatabaseGenerator.WrappedTransactor
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.*

import java.util.UUID
import scala.concurrent.duration.*
import scala.util.Random

class IntegrationTest(global: GlobalRead) extends IOSuite {
  given LoggerFactory[IO] = Slf4jFactory.create[IO]

  override type Res = ProfileSearchService[IO] // probably the service itself

  val lockConfig = LockConfig(
    maximumConcurrentLocks = LockLimit.unsafe(50),
    lockTimeout = 20.seconds
  )

  val unlockedProfileConfig = UnlockedProfileConfig(
    tableName = TableName("unlocked_profiles"),
    accessDurationInDays = 30L
  )

  val viewedProfilesConfig = ViewedProfilesConfig(
    tableName = TableName("viewed_profiles"),
    retentionThresholdInDays = 1
  )

  val visitedProfilesConfig = VisitedProfilesConfig(
    tableName = TableName("visited_profiles"),
    viewLimit = ProfileViewLimit(250)
  )

  private def unlockedProfileService[F[_]: {Async, LoggerFactory}](unlockedProfileConfig: UnlockedProfileConfig, xa: Transactor[F]) =
    for {
      repository <- UnlockedProfileRepository.initResource(unlockedProfileConfig.tableName, xa)
      service    <- UnlockedProfileService.resource[F](repository, unlockedProfileConfig.accessDurationInDays)
    } yield service

  private def viewedProfilesService[F[_]: {Async, LoggerFactory}](viewedProfilesConfig: ViewedProfilesConfig, xa: Transactor[F]) =
    for {
      repository <- ViewedProfilesRepository.resource[F](viewedProfilesConfig.tableName, viewedProfilesConfig.retentionThresholdInDays, xa)
      service    <- Resource.pure(ViewedProfilesService.impl(repository))
    } yield service

  private def visitedProfilesService[F[_]: {Async, LoggerFactory}](
    visitedProfilesConfig: VisitedProfilesConfig,
    xa: Transactor[F],
    creditService: CreditService[F],
    unlockedProfileService: UnlockedProfileService[F]
  ): Resource[F, VisitedProfilesService[F]] =
    for {
      repository <- VisitedProfilesRepository.resource[F](visitedProfilesConfig.tableName, xa)
      service    <- VisitedProfilesService.resource[F](visitedProfilesConfig.viewLimit, repository, creditService, unlockedProfileService)
    } yield service

  def populateDocuments(ec: ElasticClient): IO[String] = for {
    esIndex   <- IO.randomUUID.map(_.toString)
    _         <- DataGenerator.createESIndex(ec, esIndex)
    documents <- (1 to 10).toList.traverse(_ => DataGenerator.generate)
    _ <- documents.traverse(document =>
      DataGenerator.insertDocument(
        client = ec,
        index = esIndex,
        id = UUID.randomUUID().toString,
        document = document.toMap
      )
    )
  } yield esIndex

  def creditService: IO[CreditService[IO]] = for {
    usedRecruiterIds <- Ref.of[IO, Set[RecruiterId]](Set.empty)
  } yield new CreditService[IO] {
    def useProfileView(recruiterId: RecruiterId): IO[EntitlementId] = for {
      id             <- IO.randomUUID
      processingTime <- usedRecruiterIds.modify(ids => if (ids.contains(recruiterId)) (ids, 100) else (ids + recruiterId, 1000))
      _              <- IO.sleep(Random.between(processingTime - 100, processingTime + 100).millis) // Simulate some processing time
    } yield EntitlementId(id.toString)
  }

  override def sharedResource: Resource[IO, ProfileSearchService[IO]] = for {
    (ec, db)               <- (global.getOrFailR[ElasticClient](), global.getOrFailR[WrappedTransactor]()).parTupled
    createdIndex           <- Resource.eval(populateDocuments(ec))
    lockManager            <- Resource.eval(LockManager[IO](new MysqlLockService(), db.transactor, lockConfig))
    creditService          <- Resource.eval(creditService)
    unlockedProfileService <- unlockedProfileService(unlockedProfileConfig, db.transactor)
    viewedProfilesService  <- viewedProfilesService(viewedProfilesConfig, db.transactor)
    visitedProfilesService <- visitedProfilesService(visitedProfilesConfig, db.transactor, creditService, unlockedProfileService)
    profileSearchService   <- ProfileSearchService.resource(createdIndex, ec, visitedProfilesService, viewedProfilesService, lockManager)
  } yield profileSearchService

  test("Many concurrent requests should not cause deadlocks or timeouts") { service =>
    for {
      profiles <- service.getProfiles(Pagination(PageNumber.unsafe(1), PageSize.unsafe(10)))
      results <- profiles.profiles.traverse { profile =>
        service.getProfile(RecruiterId(Random.alphanumeric.take(18).mkString), profile.id)
      }
    } yield expect(results.size == profiles.profiles.size)
  }

  test("Many concurrent requests with different recruiters should not cause deadlocks or timeouts") { service =>
    for {
      profiles <- service.getProfiles(Pagination(PageNumber.unsafe(1), PageSize.unsafe(10)))
      results  <- profiles.profiles.traverse(profile => service.getProfile(RecruiterId(Random.alphanumeric.take(18).mkString), profile.id))
    } yield expect(results.size == profiles.profiles.size)
  }

  test("Many concurrent requests with the same recruiter should not cause deadlocks or timeouts") { service =>
    for {
      profiles <- service.getProfiles(Pagination(PageNumber.unsafe(1), PageSize.unsafe(10)))
      recruiterId = RecruiterId(Random.alphanumeric.take(18).mkString)
      results <- profiles.profiles.traverse(profile => service.getProfile(recruiterId, profile.id))
    } yield expect(results.size == profiles.profiles.size) and expect(clue(profiles.profiles.size) == 10)
  }

  test("Many concurrent requests with the same recruiter for the same profile should not cause deadlocks or timeouts") { service =>
    for {
      profiles <- service.getProfiles(Pagination(PageNumber.unsafe(1), PageSize.unsafe(1)))
      profile = profiles.profiles.head
      recruiterId = RecruiterId(Random.alphanumeric.take(18).mkString)
      results <- (1 to 10).toList.traverse(_ => service.getProfile(recruiterId, profile.id))
    } yield expect(clue(results.size) == 10)
  }
}
