package nl.dpes.profilesearch.utils

import weaver.FunSuite

object OptionalStringSpec extends FunSuite {

  case class AnotherType(value: String)

  test("It should trim input") {
    val input = Some(AnotherType("   example   "))
    val expected = Some(AnotherType("example"))
    val result = OptionalString.cleanup(input)(_.value, AnotherType.apply)
    expect(result == expected)
  }

  test("It should return None when input is empty") {
    val input = Some(AnotherType("   "))
    val result = OptionalString.cleanup(input)(_.value, AnotherType.apply)
    expect(result.isEmpty)
  }

  test("It should return None when input is None") {
    val input: Option[AnotherType] = None
    val result = OptionalString.cleanup(input)(_.value, AnotherType.apply)
    expect(result.isEmpty)
  }
}
