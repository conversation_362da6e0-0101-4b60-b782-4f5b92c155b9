package nl.dpes.profilesearch.testcontainers

import cats.effect.IO
import cats.implicits.*
import com.sksamuel.elastic4s.*
import com.sksamuel.elastic4s.ElasticDsl.*
import com.sksamuel.elastic4s.cats.effect.instances.*
import com.sksamuel.elastic4s.fields.ObjectField
import com.sksamuel.elastic4s.requests.mappings.MappingDefinition
import com.sksamuel.elastic4s.requests.mappings.dynamictemplate.DynamicMapping
import io.circe.*
import io.circe.generic.semiauto.*
import io.circe.parser.*

import scala.collection.mutable

object DataGenerator {

  case class Meta(timestamp: Long)

  case class Attachment(
    fileName: Option[String],
    extension: Option[String],
    uploadDate: Option[Long],
    name: Option[String],
    id: Option[String],
    isCv: Option[Boolean]
  )

  case class Education(
    fromDate: Option[String],
    educationId: Option[String],
    school: Option[String],
    gradeRank: Option[Int],
    grade: Option[String],
    toDate: Option[String],
    diploma: Option[Boolean],
    fieldOfStudy: Option[String]
  )

  case class Language(isoCode: Option[String], name: Option[String], isNative: Option[Boolean])

  case class Experience(
    experienceId: Option[String],
    fromDate: Option[String],
    companyName: Option[String],
    jobTitle: Option[String],
    toDate: Option[String],
    description: Option[String]
  )

  case class GeoPoint(lon: Option[String], lat: Option[String])

  case class Commute(
    zipCode: Option[String],
    province: Option[String],
    city: Option[String],
    maxTravelDistance: Option[Int],
    geoPoint: Option[GeoPoint]
  )

  case class TestProfile(
    emailAddress: Option[String],
    site: Option[String],
    id: Option[String],
    expose: Option[Boolean],
    _meta: Option[Meta],
    lastName: Option[String],
    unapprovedExperienceIds: Option[List[String]],
    attachments: Option[List[Attachment]],
    education: Option[List[Education]],
    languages: Option[List[Language]],
    workLevels: Option[List[String]],
    updatedDate: Option[Long],
    maxWorkingHours: Option[Int],
    experiences: Option[List[Experience]],
    minWorkingHours: Option[Int],
    firstName: Option[String],
    phoneNumber: Option[String],
    commute: Option[Commute],
    unapprovedEducationIds: Option[List[String]],
    workingHours: Option[List[String]]
  )

  implicit val metaDecoder: Decoder[Meta] = deriveDecoder
  implicit val attachmentDecoder: Decoder[Attachment] = deriveDecoder
  implicit val educationDecoder: Decoder[Education] = deriveDecoder
  implicit val languageDecoder: Decoder[Language] = deriveDecoder
  implicit val experienceDecoder: Decoder[Experience] = deriveDecoder
  implicit val geoPointDecoder: Decoder[GeoPoint] = deriveDecoder
  implicit val commuteDecoder: Decoder[Commute] = deriveDecoder
  implicit val personDecoder: Decoder[TestProfile] = deriveDecoder

  val cvDatabaseMapping: MappingDefinition = properties(
    dateField("_meta.timestamp"),
    keywordField("attachments.extension"),
    keywordField("attachments.fileName"),
    keywordField("attachments.id"),
    booleanField("attachments.isCv"),
    keywordField("attachments.name"),
    longField("attachments.uploadDate"),
    keywordField("attachmentContents.id"),
    textField("attachmentContents.content"),
    keywordField("availability").copy(index = Some(false)),
    keywordField("careerLevel").copy(index = Some(false)),
    keywordField("commute.city"),
    keywordField("commute.cityName"),
    geopointField("commute.geoPoint"),
    longField("commute.maxTravelDistance"),
    keywordField("commute.province").copy(normalizer = Some("lowercase_normalizer"), fields = List(keywordField("raw"))),
    keywordField("commute.zipCode"),
    keywordField("contractType"),
    keywordField("country"),
    keywordField("driverLicenses.group"),
    keywordField("driverLicenses.type").copy(index = Some(false)),
    objectField("education").copy(
      properties = Seq(
        keywordField("city"),
        textField("description"),
        booleanField("diploma"),
        keywordField("educationId"),
        textField("fieldOfStudy"),
        keywordField("fromDate").copy(index = Some(false)),
        keywordField("grade"),
        longField("gradeRank"),
        keywordField("school").copy(fields = List(textField("raw"))),
        keywordField("toDate").copy(index = Some(false))
      )
    ),
    keywordField("emailAddress"),
    objectField("experiences").copy(properties =
      Seq(
        keywordField("city"),
        keywordField("companyName"),
        textField("description"),
        keywordField("experienceId"),
        keywordField("fromDate").copy(index = Some(false)),
        textField("jobTitle")
          .fields(
            textField("raw")
          )
          .analyzer("whitespace"),
        keywordField("toDate").copy(index = Some(false))
      )
    ),
    booleanField("expose"),
    booleanField("findable"),
    keywordField("firstName"),
    keywordField("functionGroups").copy(index = Some(false), fields = List(textField("raw"))),
    nestedField("hiddenSensitiveFields").properties(
      booleanField("emailAddress").copy(index = Some(false)),
      booleanField("firstName").copy(index = Some(false)),
      booleanField("lastName").copy(index = Some(false)),
      booleanField("phoneNumber").copy(index = Some(false))
    ),
    keywordField("hobbies"),
    keywordField("id").copy(index = Some(false)),
    textField("introductionText"),
    booleanField("languages.isNative"),
    keywordField("languages.isoCode"),
    keywordField("languages.name").copy(index = Some(false)),
    keywordField("lastName"),
    keywordField("maxWorkingHours").copy(index = Some(false)),
    keywordField("minWorkingHours").copy(index = Some(false)),
    keywordField("phoneNumber"),
    keywordField("photo"),
    keywordField("preferredJobs").copy(index = Some(false), fields = List(textField("raw"))),
    keywordField("requestedSalary").copy(index = Some(false)),
    keywordField("site").copy(index = Some(false)),
    textField("training.description"),
    keywordField("training.institute"),
    keywordField("training.month"),
    keywordField("training.name"),
    keywordField("training.trainingId"),
    keywordField("training.year"),
    nestedField("trainings").properties(
      textField("description"),
      keywordField("institute"),
      keywordField("month").copy(index = Some(false)),
      keywordField("name"),
      keywordField("year").copy(index = Some(false))
    ),
    keywordField("unapprovedEducationIds"),
    keywordField("unapprovedExperienceIds"),
    longField("updatedDate"),
    keywordField("workLevels").copy(normalizer = Some("lowercase_normalizer"), fields = List(keywordField("raw"))),
    keywordField("workingHours").copy(index = Some(false))
  ).dynamic(DynamicMapping.Strict)

  val jsonString =
    """{
        "emailAddress": "<EMAIL>",
        "site": "nationalevacaturebank.nl",
        "id": "7cd63c42-1234-1234-1234-19fb61915119",
        "expose": true,
        "findable": true,
        "_meta": {
          "timestamp": *************
        },
        "preferredJobs": [
          "management assistant"
        ],
        "lastName": "Doe",
        "unapprovedExperienceIds": [
          "ebb27e52-1234-1234-1234-ecb231261234",
          "ebb166a2-1234-1234-1234-f704fa2c1234",
          "ebb23942-1234-1234-1234-47119e101234",
          "ebb1aca2-1234-1234-1234-f4f260d31234",
          "ebb0d5de-1234-1234-1234-2e3314991234",
          "ebb11f9e-1234-1234-1234-ae8071c11234",
          "ebb1f3c4-1234-1234-1234-e3fd04341234"
        ],
        "attachments": [
          {
            "fileName": "attachments/7cd63c42-1234-1234-1234-01fb6a411234/cv.doc",
            "extension": ".doc",
            "uploadDate": **********,
            "name": "John-Doe-cv",
            "id": "acbd3647-1234-1234-1234-acb703c81234",
            "isCv": true
          }
        ],
        "education": [
          {
            "fromDate": "1995-10",
            "educationId": "ebb30c28-1234-1234-1234-f49c021a1234",
            "school": "Universitá degli Studi di Torino",
            "toDate": "2000-10",
            "grade": "HBO",
            "diploma": true,
            "fieldOfStudy": "Degree in Russian Language and Literature; (IT)"
          },
          {
            "educationId": "ebb351b0-1286-11e7-857b-f8fd2edc40b5",
            "school": "University of Cambridge; British Council, Milan (IT)",
            "toDate": "1996-6",
            "grade": "HBO",
            "diploma": true,
            "fieldOfStudy": "Certificate of Proficiency in English; c/o"
          },
          {
            "fromDate": "2009-1",
            "educationId": "ebb2c5e2-1286-11e7-a246-70368a4b9647",
            "school": "University of Leiden, Leiden (NL)",
            "toDate": "2009-4",
            "grade": "HAVO",
            "diploma": true,
            "fieldOfStudy": "Intensive Course Dutch (A1)"
          }
        ],
       "driverLicenses": [
          {
            "type": "B - personenauto",
            "group": "B - personenauto"
          }
        ],
        "languages": [
          {
            "isoCode": "EN",
            "name": "Engels",
            "isNative": true
          },
          {
            "isoCode": "NL",
            "name": "Nederlands",
            "isNative": false
          }
        ],
        "photo": "profile-photos/7cd63c42-1234-1234-1234-01fb6a415123/152dea87-1234-1234-1234-dfb501ab1234.jpg",
        "availability": "In overleg",
        "updatedDate": **********,
        "maxWorkingHours": 40,
        "requestedSalary": "5.000 - 7.000",
        "experiences": [
          {
            "experienceId": "ebb1aca2-1234-1234-1234-f4f260d31234",
            "fromDate": "2002-9",
            "companyName": "Euralcom BV, Borgaro T.",
            "toDate": "2006-6",
            "jobTitle": "UX Designer"
          },
          {
            "experienceId": "ebb166a2-1286-11e7-905f-f704fa2c6ad7",
            "fromDate": "2006-6",
            "companyName": "Torino",
            "toDate": "2008-12",
            "jobTitle": "Recruiter"
          },
          {
            "experienceId": "ebb23942-1286-11e7-9231-47119e10bb94",
            "fromDate": "2002-2",
            "companyName": "University of Turin",
            "toDate": "2003-2",
            "jobTitle": "English teacher"
          }
        ],
        "minWorkingHours": 36,
        "firstName": "John",
        "careerLevel": "Directie",
        "commute": {
          "zipCode": "1018LL",
          "province": "Noord-Holland",
          "city": "Amsterdam",
          "maxTravelDistance": 50,
          "geoPoint": {
            "lon": "4.53222",
            "lat": "52.390884"
          }
        },
        "functionGroups": [
          "Administratief/Secretarieel"
        ],
        "unapprovedEducationIds": [
          "ebb30c28-1234-1234-1234-f49c021a1234",
          "ebb2c5e2-1234-1234-1234-70368a4b1234",
          "ebb42568-1234-1234-1234-0e7737c51234",
          "ebb3df2c-1234-1234-1234-60bd70211234",
          "ebb39878-1234-1234-1234-866081e61234",
          "ebb351b0-1234-1234-1234-f8fd2edc1234"
        ],
        "workingHours": [
          "32 tot en met 40 uur"
        ],
        "workLevels": [
          "HBO"
        ]
      }"""

  private def convertJsonToMap(json: Json): Map[String, Any] = {
    def convert(json: Json): Any = json.fold(
      jsonNull = null,
      jsonBoolean = identity,
      jsonNumber = num => num.toInt.getOrElse(num.toLong),
      jsonString = identity,
      jsonArray = arr => arr.map(convert),
      jsonObject = obj => obj.toMap.map { case (k, v) => k -> convert(v) }
    )

    convert(json).asInstanceOf[Map[String, Any]]
  }

  def generate: IO[mutable.Map[String, Any]] = {
    val json: Either[Error, Json] = parse(jsonString)
    val testProfile: Either[Error, TestProfile] = json.flatMap(_.as[TestProfile])
    testProfile match {
      case Right(p)    => mutable.Map(convertJsonToMap(json.getOrElse(Json.Null)).toSeq*).pure[IO]
      case Left(error) => error.raiseError[IO, mutable.Map[String, Any]]
    }
  }

  def createESIndex(client: ElasticClient, index: String): IO[Boolean] =
    client
      .execute {
        ElasticApi.createIndex(index).mapping(cvDatabaseMapping)
      }
      .flatMap {
        case RequestSuccess(_, _, _, result) => result.acknowledged.pure
        case RequestFailure(_, _, _, error)  => error.asException.raiseError
      }

  def insertDocument(client: ElasticClient, index: String, id: String, document: Map[String, Any]): IO[String] = {
    val documentWithId = document + ("id" -> id)
    client
      .execute {
        indexInto(Index(index)).id(id).fields(documentWithId).refreshImmediately
      }
      .flatMap {
        case RequestSuccess(_, _, _, result) => result.result.pure
        case RequestFailure(_, _, _, error)  => error.asException.raiseError
      }
  }
}
