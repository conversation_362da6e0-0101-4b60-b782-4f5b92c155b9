package nl.dpes.profilesearch
package service
package visitedprofiles

import weaver.FunSuite

object ProfileIdsSpec extends FunSuite {
  test("Adding the same id twice will not add the second one") {
    val profileId = ProfileId.unsafe("anId")
    val actual = ProfileIds.empty + profileId + profileId
    val expected = ProfileIds.empty + profileId

    expect(actual == expected)
  }

  test("adding different ids will add them all") {
    val profileId1 = ProfileId.unsafe("anId")
    val profileId2 = ProfileId.unsafe("anotherId")
    val result = ProfileIds.empty + profileId1 + profileId2

    expect(result.contains(profileId1)) and expect(result.contains(profileId2))
  }

  test("profileIds can indicate it contains an id") {
    val profileId: ProfileId = ProfileId.unsafe("anId")
    val actual: ProfileIds = ProfileIds.empty + profileId

    expect(actual.contains(profileId))
  }

  test("profileIds can indicate it does not contain an id") {
    val profileId: ProfileId = ProfileId.unsafe("anId")

    expect(!ProfileIds.empty.contains(profileId))
  }

  test("profileIds can indicate it reached a limit") {
    val profileId: ProfileId = ProfileId.unsafe("anId")
    val limit: ProfileViewLimit = ProfileViewLimit(1)
    val actual: ProfileIds = ProfileIds.empty + profileId

    expect(actual.reachedLimit(limit))
  }

  test("profileIds can indicate it did not reach a limit") {
    val profileId = ProfileId.unsafe("anId")
    val limit = ProfileViewLimit(2)
    val actual: ProfileIds = ProfileIds.empty + profileId

    expect(!actual.reachedLimit(limit))
  }

  test("when profileIds contains more than the limit it reached the limit") {
    val (id1, id2) = (ProfileId.unsafe("id1"), ProfileId.unsafe("id2"))
    val limit: ProfileViewLimit = ProfileViewLimit(1)
    val actual: ProfileIds = ProfileIds.empty + id1 + id2

    expect(actual.reachedLimit(limit))
  }
}
