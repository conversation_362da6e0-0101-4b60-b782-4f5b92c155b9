package nl.dpes.profilesearch.domain

import weaver.*

object WorkLevelSpec extends FunSuite {
  test("Known worklevels will always be ordered by their predefined order") {
    val workLevels = List(
      WorkLevel("VWO"),
      WorkLevel("WO"),
      WorkLevel("LBO"),
      WorkLevel("Postdoctoraal"),
      WorkLevel("MBO"),
      WorkLevel("HBO"),
      WorkLevel("VMBO/Mavo"),
      WorkLevel("HAVO"),
      WorkLevel("Lagere school")
    )

    expect(
      workLevels.sorted == List(
        WorkLevel("Postdoctoraal"),
        WorkLevel("WO"),
        WorkLevel("HBO"),
        WorkLevel("MBO"),
        WorkLevel("VWO"),
        WorkLevel("HAVO"),
        WorkLevel("VMBO/Mavo"),
        WorkLevel("LBO"),
        WorkLevel("Lagere school")
      )
    )
  }

  test("Unknown worklevels will be placed at the end of the list") {
    val workLevels = List(
      WorkLevel("LBO"),
      WorkLevel("Onbekend"),
      WorkLevel("Postdoctoraal")
    )

    expect(
      workLevels.sorted == List(
        WorkLevel("Postdoctoraal"),
        WorkLevel("LBO"),
        WorkLevel("Onbekend")
      )
    )
  }

  test("Unknown worklevels will be ordered alphabetically") {
    val workLevels = List(
      WorkLevel("Zonder titel"),
      WorkLevel("Postdoctoraal"),
      WorkLevel("Onbekend"),
      WorkLevel("LBO"),
      WorkLevel("HBO")
    )

    expect(
      workLevels.sorted == List(
        WorkLevel("Postdoctoraal"),
        WorkLevel("HBO"),
        WorkLevel("LBO"),
        WorkLevel("Onbekend"),
        WorkLevel("Zonder titel")
      )
    )
  }
}
