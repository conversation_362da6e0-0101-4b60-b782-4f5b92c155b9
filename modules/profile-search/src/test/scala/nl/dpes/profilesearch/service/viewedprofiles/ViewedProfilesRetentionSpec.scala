package nl.dpes.profilesearch.service.viewedprofiles

import cats.effect.*
import cats.implicits.*
import doobie.*
import doobie.implicits.*
import doobie.util.meta.Meta
import doobie.util.transactor.Transactor
import nl.dpes.profilesearch.service.TableName
import nl.dpes.profilesearch.service.model.profilesearch.{ProfileId, RecruiterId}
import nl.dpes.profilesearch.testcontainers.MySqlDatabaseGenerator
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.IOSuite

import java.sql.Timestamp
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

// We have to put it into a separate suite because the MySQL PROCEDURE should be unique for the database.
// There is a race condition here. It can be that the test creating the MySQL PROCEDURE is different from the test
// calling the PROCEDURE, that's why we've moved this test from 'ViewedProfilesRepositorySpec'
object ViewedProfilesRetentionSpec extends IOSuite with MySqlDatabaseGenerator {

  given Meta[Instant] = Meta[Timestamp].imap(_.toInstant)(Timestamp.from)
  given LoggerFactory[IO] = Slf4jFactory.create[IO]

  override type Res = Transactor[IO]

  override def sharedResource: Resource[IO, ViewedProfilesRetentionSpec.Res] = transactor

  case class Repo(viewedProfilesRepo: ViewedProfilesRepository[IO], tableName: TableName)

  private val recruiterId: RecruiterId = RecruiterId("This a recruiter id")
  private val recruiterId2: RecruiterId = RecruiterId("This a recruiter id")
  private val profileId: ProfileId = ProfileId("profile 1").toOption.get
  private val profileId2: ProfileId = ProfileId("profile 2").toOption.get
  private val profileId3: ProfileId = ProfileId("profile 3").toOption.get

  def randomRepo(xa: Transactor[IO]): Resource[IO, Repo] = {
    val id = UUID.randomUUID().toString.replace("-", "")
    for {
      repo <- ViewedProfilesRepository.resource(TableName(s"viewed_profiles_$id".trim), 365, xa)
    } yield Repo(repo, TableName(s"viewed_profiles_$id".trim))
  }

  test("It should delete the old profile views") { xa =>
    randomRepo(xa).use { repo =>
      for {
        // Setup recruiter1 data
        _ <- repo.viewedProfilesRepo.save(recruiterId, profileId)
        _ <- repo.viewedProfilesRepo.save(recruiterId, profileId2)
        _ <- repo.viewedProfilesRepo.save(recruiterId, profileId3)
        // Make profiles 2 and 3 old for recruiter1
        _ <- updateLastViewDate(recruiterId, profileId2, Instant.now().minus(367, ChronoUnit.DAYS), repo.tableName.fragment, xa)
        _ <- updateLastViewDate(recruiterId, profileId3, Instant.now().minus(367, ChronoUnit.DAYS), repo.tableName.fragment, xa)

        // Setup recruiter2 data
        _ <- repo.viewedProfilesRepo.save(recruiterId2, profileId)
        _ <- repo.viewedProfilesRepo.save(recruiterId2, profileId2)
        // Make profile 2 old for recruiter2
        _ <- updateLastViewDate(recruiterId2, profileId2, Instant.now().minus(367, ChronoUnit.DAYS), repo.tableName.fragment, xa)

        // Run cleanup
        _ <- sql"""CALL CleanupViewedProfiles();""".update.run.void.transact(xa)

        // Verify results
        viewsRecruiter1 <- getViews(recruiterId, repo.tableName.fragment, xa)
        viewsRecruiter2 <- getViews(recruiterId2, repo.tableName.fragment, xa)
      } yield expect(viewsRecruiter1 == List(profileId) && viewsRecruiter2 == List(profileId))
    }
  }

  def getViews(recruiterId: RecruiterId, tableName: Fragment, xa: Transactor[IO]): IO[List[ProfileId]] =
    sql"""
        SELECT profileId
        FROM $tableName
        WHERE recruiterId = $recruiterId
        ORDER BY lastViewedDate
       """
      .query[ProfileId]
      .to[List]
      .transact(xa)

  def updateLastViewDate(
    recruiterId: RecruiterId,
    profileId: ProfileId,
    timestamp: Instant,
    tableName: Fragment,
    xa: Transactor[IO]
  ): IO[Unit] =
    sql"""
        UPDATE $tableName
        SET lastViewedDate = $timestamp
        WHERE recruiterId = $recruiterId AND profileId = $profileId
      """.update.run.transact(xa).void

}
