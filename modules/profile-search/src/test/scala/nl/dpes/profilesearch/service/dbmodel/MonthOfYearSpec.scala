package nl.dpes.profilesearch
package service
package dbmodel

import nl.dpes.profilesearch.service.model.dbmodel.{Month, MonthOfYear, Year}
import weaver.FunSuite

object MonthOfYearSpec extends FunSuite {
  test("MonthOfYears will be sorted on year first and then on month") {
    val unsorted = List(
      MonthOfYear(Month(2).some, Year(2020)),
      MonthOfYear(Month(1).some, Year(2021)),
      MonthOfYear(Month(1).some, Year(2020)),
      MonthOfYear(Month(2).some, Year(2021))
    )
    val sorted = List(
      MonthOfYear(Month(1).some, Year(2020)),
      MonthOfYear(Month(2).some, Year(2020)),
      MonthOfYear(Month(1).some, Year(2021)),
      MonthOfYear(Month(2).some, Year(2021))
    )
    expect(unsorted.sorted == sorted)
  }

  test("MonthOfYear can be parsed from a string with month") {
    val parsed = io.circe.parser.decode[MonthOfYear]("\"2020-2\"")
    expect(parsed == Right(MonthOfYear(Month(2).some, Year(2020))))
  }

  test("MonthOfYear can be parsed from a string without month") {
    val parsed = io.circe.parser.decode[MonthOfYear]("\"2020\"")
    expect(parsed == Right(MonthOfYear(none, Year(2020))))
  }

  test("MonthOfYear cannot be parsed from an incorrect string") {
    val parsed = io.circe.parser.decode[MonthOfYear]("\"2020-113\"")
    parsed match {
      case Left(error)  => expect(error.getMessage == "DecodingFailure at : Cannot parse '2020-113' into a MonthOfYear")
      case Right(value) => failure(s"Expected a Left but got a Right: $value")
    }
  }
}
