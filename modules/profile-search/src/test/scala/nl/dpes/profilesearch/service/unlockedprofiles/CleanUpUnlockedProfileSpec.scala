package nl.dpes.profilesearch
package service
package unlockedprofiles

import cats.effect.{IO, Resource}
import cats.implicits.catsSyntaxApplicativeId
import io.scalaland.chimney.dsl.*
import doobie.*
import doobie.implicits.*
import nl.dpes.profilesearch.service.TableName
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpes.profilesearch.service.unlockedprofiles
import nl.dpes.profilesearch.testcontainers.MySqlDatabaseGenerator
import weaver.*

import java.time.LocalDate
import java.util.UUID

// We have to put it into a separate suite because the MySQL PROCEDURE should be unique for the database.
// There is a race condition. It can be that the test creating the MySQL PROCEDURE is different from the test
// calling the PROCEDURE, that's why we've moved this test here from 'UnlockedProfileRepositorySpec'
object CleanUpUnlockedProfileSpec extends IOSuite with MySqlDatabaseGenerator {
  override type Res = Transactor[IO]
  override def sharedResource: Resource[IO, Res] = transactor

  def createRepository(tableName: TableName, xa: Transactor[IO]): IO[UnlockedProfileRepository[IO]] =
    UnlockedProfileRepository.initResource[IO](tableName, xa).use(identity(_).pure)

  // We do this deliberately to avoid name clashes
  private val tableNameString = s"unlocked_profiles_${UUID.randomUUID()}".replace("-", "_")
  private val randomTableName: IO[TableName] = IO.pure(TableName(tableNameString))

  def getRepository(xa: Transactor[IO]): IO[UnlockedProfileRepository[IO]] = for {
    tableName  <- randomTableName
    repository <- createRepository(tableName, xa)
  } yield repository

  private val recruiterId = profilesearch.RecruiterId("1")
  private val profileId = profilesearch.ProfileId.unsafe("id")
  private val profileId2 = profilesearch.ProfileId.unsafe("id2")

  test("Expired records are removed from the table") { xa =>
    val expirationDate = LocalDate.ofEpochDay(0)
    val now = LocalDate.now()

    for {
      repository <- getRepository(xa)
      _ <- repository.setExpirationDate(
        recruiterId.into[unlockedprofiles.RecruiterId].transform,
        profileId.into[unlockedprofiles.ProfileId].transform,
        expirationDate
      )
      _ <- repository.setExpirationDate(recruiterId.into[RecruiterId].transform, profileId2.into[ProfileId].transform, now.plusDays(1))
      _ <- sql"""CALL CleanUpExpiredUnlockedProfiles();""".update.run.void.transact(xa)
      foundCleanedExpirationDate <- repository.getExpirationDate(recruiterId, profileId)
      foundValidExpirationDate   <- repository.getExpirationDate(recruiterId, profileId2)
    } yield expect(foundCleanedExpirationDate.isEmpty && foundValidExpirationDate.nonEmpty)
  }

  test("There is an event that cleans up the expired records") { xa =>
    for {
      repository <- getRepository(xa)
      eventName <- sql"""
        SELECT EVENT_NAME
        FROM information_schema.EVENTS
        WHERE EVENT_NAME = 'CleanUpExpiredUnlockedProfilesEvent'
      """.query[String].option.transact(xa)
    } yield expect(eventName.contains("CleanUpExpiredUnlockedProfilesEvent"))
  }
}
