package nl.dpes.profilesearch.domain

import weaver.*

object CareerLevelSpec extends FunSuite {
  test("Known careerevels will always be ordered by their predefined order") {
    val careerLevels = List(
      CareerLevel("Directie"),
      CareerLevel("Senior management"),
      CareerLevel("Leidinggevend"),
      CareerLevel("Ervaren"),
      CareerLevel("Starter")
    )

    expect(
      careerLevels.sorted == List(
        CareerLevel("Starter"),
        CareerLevel("Ervaren"),
        CareerLevel("Leidinggevend"),
        CareerLevel("Senior management"),
        CareerLevel("Directie")
      )
    )
  }

  test("Unknown careerevels will be placed at the end of the list") {
    val careerLevels = List(
      CareerLevel("Directie"),
      CareerLevel("Senior management"),
      CareerLevel("Leidinggevend"),
      CareerLevel("Unknown"),
      CareerLevel("Ervaren"),
      CareerLevel("Starter")
    )

    expect(
      careerLevels.sorted == List(
        CareerLevel("Starter"),
        CareerLevel("<PERSON><PERSON>ren"),
        <PERSON><PERSON>evel("Leidinggevend"),
        CareerLevel("Senior management"),
        CareerLevel("Directie"),
        CareerLevel("Unknown")
      )
    )
  }

  test("Unknown careerlevels will be ordered alphabetically") {
    val careerLevels = List(
      CareerLevel("Directie"),
      CareerLevel("Zonder titel"),
      CareerLevel("Senior management"),
      CareerLevel("Leidinggevend"),
      CareerLevel("Unknown"),
      CareerLevel("Ervaren"),
      CareerLevel("Starter")
    )

    expect(
      careerLevels.sorted == List(
        CareerLevel("Starter"),
        CareerLevel("Ervaren"),
        CareerLevel("Leidinggevend"),
        CareerLevel("Senior management"),
        CareerLevel("Directie"),
        CareerLevel("Unknown"),
        CareerLevel("Zonder titel")
      )
    )
  }
}
