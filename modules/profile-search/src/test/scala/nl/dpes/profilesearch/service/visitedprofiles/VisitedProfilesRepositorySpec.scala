package nl.dpes.profilesearch
package service
package visitedprofiles

import cats.effect.{IO, Resource}
import nl.dpes.profilesearch.testcontainers.MySqlDatabaseGenerator
import weaver.*
import io.scalaland.chimney.dsl.*
import doobie.*
import doobie.implicits.*
import doobie.util.*
import nl.dpes.profilesearch.testcontainers.MySqlDatabaseGenerator.WrappedTransactor
import nl.dpes.profilesearch.service.model.profilesearch

class VisitedProfilesRepositorySpec(global: GlobalRead) extends IOSuite {
  override type Res = Transactor[IO]
  override def sharedResource: Resource[IO, Res] =
    global.getOrFailR[WrappedTransactor]().map(_.transactor)

  def repository(tableName: TableName, xa: Transactor[IO]): IO[VisitedProfilesRepository[IO]] =
    VisitedProfilesRepository.resource[IO](tableName, xa).use(identity(_).pure)

  val randomTableName: IO[TableName] = IO.randomUUID.map(id => TableName(s"visited_profiles_$id".replace("-", "_")))

  def randomRepository(xa: Transactor[IO]): IO[VisitedProfilesRepository[IO]] = for {
    tableName  <- randomTableName
    repository <- repository(tableName, xa)
  } yield repository

  val randomRecruiterId: IO[profilesearch.RecruiterId] = IO.randomUUID.map(id => profilesearch.RecruiterId(id.toString))
  val randomProfileId: IO[profilesearch.ProfileId] = IO.randomUUID.map(id => profilesearch.ProfileId.unsafe(id.toString))

  test("Get empty ProfileIds when no profile was stored") { xa =>
    for {
      repository  <- randomRepository(xa)
      recruiterId <- randomRecruiterId
      result      <- repository.read(recruiterId)
    } yield expect(result == ProfileIds.empty)
  }

  test("profileIds can be stored and read back") { xa =>
    for {
      repository  <- randomRepository(xa)
      recruiterId <- randomRecruiterId
      (id1, id2)  <- (randomProfileId, randomProfileId).tupled
      profileIds  <- IO.pure(ProfileIds.empty + id1.into[ProfileId].transform + id2.into[ProfileId].transform)
      _           <- repository.store(recruiterId, profileIds.into[Set[profilesearch.ProfileId]].transform)
      result      <- repository.read(recruiterId)
    } yield expect(result == profileIds)
  }

  test("Concurrent updates will work correctly") { xa =>
    def addProfileId(repository: VisitedProfilesRepository[IO], recruiterId: RecruiterId): IO[ProfileId] = for {
      profileId <- randomProfileId
      _ <- repository.update(
        recruiterId.into[profilesearch.RecruiterId].transform,
        profileIds => profileIds + profileId.into[profilesearch.ProfileId].transform
      )
    } yield profileId.into[ProfileId].transform

    for {
      repository  <- randomRepository(xa)
      recruiterId <- randomRecruiterId
      ids         <- List.fill(100)(addProfileId(repository, recruiterId.into[RecruiterId].transform)).parSequence
      result      <- repository.read(recruiterId)
    } yield {
      val expected: ProfileIds = ids.foldLeft(ProfileIds.empty)(_ + _)
      expect(result == expected)
    }
  }

  test("Reading profileIds from an other date will return an empty list") { xa =>
    def changeDateToYesterday(tableName: TableName, recruiterId: RecruiterId): ConnectionIO[Unit] =
      sql"""
           |update ${tableName.fragment}
           |set date = subdate(current_date, 1)
           |where recruiterId = $recruiterId
           |""".stripMargin.update.run.void

    for {
      tableName   <- randomTableName
      repository  <- repository(tableName, xa)
      recruiterId <- randomRecruiterId
      (id1, id2)  <- (randomProfileId, randomProfileId).tupled
      profileIds  <- IO.pure(ProfileIds.empty + id1.into[ProfileId].transform + id2.into[ProfileId].transform)
      _           <- repository.store(recruiterId, profileIds.into[Set[profilesearch.ProfileId]].transform)
      _           <- changeDateToYesterday(tableName, recruiterId.into[RecruiterId].transform).transact(xa)
      result      <- repository.read(recruiterId)
    } yield expect(result == ProfileIds.empty)
  }
}
