package nl.dpes.profilesearch.utils

import nl.dpes.profilesearch.utils.SimpleSearchQueryTranslator.UnmatchedQuoteOrBracket
import weaver.FunSuite

object SimpleSearchQueryTranslatorSpec extends FunSuite {
  private val translate = SimpleSearchQueryTranslator.translate

  test("The simple search translator should map Dutch and English word operators to Elastic operators") {
    expect.same(Right("fox + cow"), translate("fox & cow")) &&
    expect.same(Right("fox + cow"), translate("fox AND cow")) &&
    expect.same(Right("fox -cow"), translate("fox !cow")) &&
    expect.same(Right("fox | cow"), translate("fox OR cow")) &&
    expect.same(Right("fox + -cow"), translate("fox AND NOT cow")) &&
    expect.same(Right("fox | cow"), translate("fox or cow")) &&
    expect.same(Right("fox + -cow"), translate("fox and not cow")) &&
    expect.same(Right("(fox | cow) + (-mouse)"), translate("(fox OF cow) EN (NIET mouse)")) &&
    expect.same(Right("(fox | cow) + (-mouse)"), translate("(fox of cow) en (niet mouse)"))
  }

  test("The simple search translator should act as a normal (prefix) search if there are no operators") {
    expect.same(Right("amster*"), translate("amster")) &&
    expect.same(Right("Software eng*"), translate("Software eng"))
  }

  test("The simple search translator should handle quoted strings correctly") {
    expect.same(Right("fox + \"brown cow\""), translate("fox AND \"brown cow\"")) &&
    expect.same(Right("fox | \"brown cow\""), translate("fox OR \"brown cow\"")) &&
    expect.same(Right("fox + -\"brown cow\""), translate("fox AND NOT \"brown cow\"")) &&
    expect.same(Right("\"brown fox\" + \"brown cow\""), translate("\"brown fox\" AND \"brown cow\"")) &&
    expect.same(Right("\"brown cow)\""), translate("\"brown cow)\""))
  }

  test("The simple search translator should return an error for invalid input") {
    expect(translate("fox AND (brown cow").isLeft) &&
    expect(translate("fox AND )brown cow(").isLeft) &&
    expect(translate("fox OR \"brown cow").isLeft)
  }

  test("The simple search translator should return an UnmatchedQuoteOrBracket for invalid input") {
    translate("Unmatching ( bracket") match {
      case Left(UnmatchedQuoteOrBracket(message)) => expect(message.contains("Unmatching ( bracket"))
      case _                                      => failure("Expected UnmatchedQuoteOrBracket error")
    }
  }
}
