package nl.dpes.profilesearch.utils

import weaver.FunSuite

object CommaSeparatedStringsSpec extends FunSuite {
  case class TestEntity(value: String)

  test("It should return None when trying to stringify an empty list") {
    val result = CommaSeparatedStrings.stringify(List.empty[TestEntity])(_.value, TestEntity.apply)
    expect(result.isEmpty)
  }

  test("It should be able to stringify a non-empty list") {
    val entities = List(TestEntity("one"), TestEntity("two"), TestEntity("three"))
    val result = CommaSeparatedStrings.stringify(entities)(_.value, TestEntity.apply)
    expect(result.contains(TestEntity("one,two,three")))
  }

  test("It should return an empty list when no input was provided") {
    val result = CommaSeparatedStrings.extractList(None: Option[TestEntity])(_.value, TestEntity.apply)
    expect(result.isEmpty)
  }

  test("It should return an empty list when some input was provided") {
    val input = Some(TestEntity("one,two,three"))
    val result = CommaSeparatedStrings.extractList(input)(_.value, TestEntity.apply)
    val expected = List(TestEntity("one"), TestEntity("two"), TestEntity("three"))
    expect(result == expected)
  }

  test("It should be able to trim whitespaces and ignore empty strings") {
    val input = Some(TestEntity(" one , , two , three "))
    val result = CommaSeparatedStrings.extractList(input)(_.value, TestEntity.apply)
    val expected = List(TestEntity("one"), TestEntity("two"), TestEntity("three"))
    expect(result == expected)
  }
}
