package nl.dpes.profilesearch
package service.model.profilesearch

import io.circe.syntax.*
import io.scalaland.chimney.syntax.transformInto
import nl.dpes.profilesearch.service.model.{dbmodel, profilesearch}
import weaver.FunSuite

object ProfileSearchResultSpec extends FunSuite {

  val dbProfile: dbmodel.Profile = dbmodel.Profile(
    id = dbmodel.ProfileId("56cda6ad-e5b1-4cf1-a83a-0b340eabdcdb"),
    site = dbmodel.SiteName("example-site"),
    firstName = Some(dbmodel.FirstName("John")),
    lastName = Some(dbmodel.LastName("Doe")),
    photo = Some(dbmodel.PhotoPath("profile-photos/7cd63c42-1234-1234-1234-01fb6a415123/152dea87-1234-1234-1234-dfb501ab1234.jpg")),
    introductionText = None,
    availability = None,
    requestedSalary = None,
    workLevels = Some(List(dbmodel.WorkLevel("HBO"))),
    preferredJobs = Some(List(dbmodel.PreferredJob("management assistant"))),
    functionGroups = None,
    emailAddress = dbmodel.EmailAddress("<EMAIL>"),
    phoneNumber = Some(dbmodel.PhoneNumber("+31 6 12345678")),
    commute = Some(dbmodel.Commute(Some(dbmodel.City("Amsterdam")), Some(dbmodel.MaxTravelDistance(0)))),
    minWorkingHours = Some(dbmodel.MinWorkingHours(36)),
    maxWorkingHours = Some(dbmodel.MaxWorkingHours(40)),
    experiences = Some(
      List(
        dbmodel.Experience(
          Some(dbmodel.MonthOfYear(dbmodel.Month(9).some, dbmodel.Year(2002))),
          Some(dbmodel.MonthOfYear(dbmodel.Month(6).some, dbmodel.Year(2006))),
          None,
          Some(dbmodel.JobTitle("UX Designer")),
          None
        ),
        dbmodel.Experience(
          Some(dbmodel.MonthOfYear(dbmodel.Month(6).some, dbmodel.Year(2006))),
          Some(dbmodel.MonthOfYear(dbmodel.Month(12).some, dbmodel.Year(2008))),
          None,
          Some(dbmodel.JobTitle("Recruiter")),
          None
        ),
        dbmodel.Experience(
          Some(dbmodel.MonthOfYear(dbmodel.Month(2).some, dbmodel.Year(2002))),
          Some(dbmodel.MonthOfYear(dbmodel.Month(2).some, dbmodel.Year(2003))),
          None,
          Some(dbmodel.JobTitle("English teacher")),
          None
        )
      )
    ),
    education = None,
    training = None,
    driverLicenses = None,
    attachments = None,
    updatedDate = dbmodel.UpdatedDate(**********)
  )

  val profile: ProfileSearchResult = ProfileSearchResult(
    id = ProfileId.unsafe("56cda6ad-e5b1-4cf1-a83a-0b340eabdcdb"),
    name = Some(Name(FirstName("John"), LastName("Doe"))),
    updatedDate = UpdatedDate(**********),
    workingHours = Some(WorkingHours(36, 40)),
    workLevels = List(WorkLevel("HBO")),
    availability = None,
    experiences = List(Experience("Recruiter"), Experience("UX Designer"), Experience("English teacher")),
    preferredJobs = List(PreferredJob("management assistant")),
    photo = Some(Photo("profile-photos/7cd63c42-1234-1234-1234-01fb6a415123/152dea87-1234-1234-1234-dfb501ab1234.jpg")),
    city = Some(City("Amsterdam")),
    lastViewedDate = None
  )

  test("It should create a Profile out of raw data") {
    val actual: ProfileSearchResult = dbProfile.transformInto[ProfileSearchResult]
    expect(actual == profile)
  }

  test("It should return empty lists for the empty array fields") {
    val dbProfileWithEmptyArrays = dbProfile.copy(workLevels = None, experiences = None, preferredJobs = None)
    val expectedProfile =
      profile.copy(workLevels = List(), experiences = List(), preferredJobs = List())

    val actual = dbProfileWithEmptyArrays.transformInto[profilesearch.ProfileSearchResult]

    expect(actual == expectedProfile)
  }

  test("It should only show the last 3 jobtitles in the experiences") {
    val experiences = List(
      dbmodel.Experience(
        Some(dbmodel.MonthOfYear(dbmodel.Month(9).some, dbmodel.Year(2002))),
        Some(dbmodel.MonthOfYear(dbmodel.Month(6).some, dbmodel.Year(2006))),
        None,
        Some(dbmodel.JobTitle("UX Designer")),
        None
      ),
      dbmodel.Experience(
        Some(dbmodel.MonthOfYear(dbmodel.Month(6).some, dbmodel.Year(2001))),
        Some(dbmodel.MonthOfYear(dbmodel.Month(12).some, dbmodel.Year(2002))),
        None,
        Some(dbmodel.JobTitle("Vakkenvuller")),
        None
      ),
      dbmodel.Experience(
        Some(dbmodel.MonthOfYear(dbmodel.Month(6).some, dbmodel.Year(2006))),
        Some(dbmodel.MonthOfYear(dbmodel.Month(12).some, dbmodel.Year(2008))),
        None,
        Some(dbmodel.JobTitle("Recruiter")),
        None
      ),
      dbmodel.Experience(
        Some(dbmodel.MonthOfYear(dbmodel.Month(2).some, dbmodel.Year(2002))),
        Some(dbmodel.MonthOfYear(dbmodel.Month(2).some, dbmodel.Year(2003))),
        None,
        Some(dbmodel.JobTitle("English teacher")),
        None
      )
    )
    val dbProfileWithEmptyArrays = dbProfile.copy(workLevels = None, experiences = Some(experiences), preferredJobs = None)
    val expectedExperiences = List(
      Experience("Recruiter"),
      Experience("UX Designer"),
      Experience("English teacher")
    )

    val actual = dbProfileWithEmptyArrays.transformInto[profilesearch.ProfileSearchResult]

    expect(actual.experiences == expectedExperiences)
  }

// this test is for controller layer, not for service layer
//  test("A profile search result without the optional values should not send them") {
//    val actual = ProfileSearchResult(
//      id = ProfileId.unsafe("56cda6ad-e5b1-4cf1-a83a-0b340eabdcdb"),
//      name = None,
//      updatedDate = UpdatedDate(**********),
//      workingHours = None,
//      workLevels = List(),
//      experiences = List(),
//      preferredJobs = List(),
//      photo = None,
//      city = None
//    ).asJson.noSpaces
//
//    val expected =
//      """{"id":"56cda6ad-e5b1-4cf1-a83a-0b340eabdcdb","updatedDate":**********,"workLevels":[],"experiences":[],"preferredJobs":[]}"""
//
//    expect(actual == expected)
//  }

  test("Experiences with empty job titles should not be included in the search result") {
    val experiences = List(
      dbmodel.Experience(
        fromDate = Some(dbmodel.MonthOfYear(dbmodel.Month(9).some, dbmodel.Year(2002))),
        toDate = Some(dbmodel.MonthOfYear(dbmodel.Month(6).some, dbmodel.Year(2006))),
        companyName = None,
        jobTitle = None, // Empty job title
        description = None
      ),
      dbmodel.Experience(
        fromDate = Some(dbmodel.MonthOfYear(dbmodel.Month(6).some, dbmodel.Year(2006))),
        toDate = Some(dbmodel.MonthOfYear(dbmodel.Month(12).some, dbmodel.Year(2008))),
        companyName = None,
        jobTitle = Some(dbmodel.JobTitle("Recruiter")),
        description = None
      )
    )
    val dbProfileWithEmptyJobTitle = dbProfile.copy(experiences = Some(experiences))
    val expectedExperiences = List(Experience("Recruiter"))

    val actual = dbProfileWithEmptyJobTitle.transformInto[profilesearch.ProfileSearchResult]

    expect(actual.experiences == expectedExperiences)
  }
}
