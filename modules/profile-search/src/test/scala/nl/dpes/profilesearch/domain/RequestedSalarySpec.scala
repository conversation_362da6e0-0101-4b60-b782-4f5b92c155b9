package nl.dpes.profilesearch.domain

import nl.dpes.profilesearch.domain.RequestedSalary.*
import weaver.*

object RequestedSalarySpec extends FunSuite {
  test("Known salaries will always be ordered by their predefined order") {
    val salaries = List(
      UpTo1750,
      From3500To5000,
      From2500To3500,
      From5000To7000,
      MoreThan7000,
      From1750To2500
    )

    expect(
      salaries.sorted == List(
        UpTo1750,
        From1750To2500,
        From2500To3500,
        From3500To5000,
        From5000To7000,
        MoreThan7000
      )
    )
  }
}
