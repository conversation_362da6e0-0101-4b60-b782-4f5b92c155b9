package nl.dpes.profilesearch
package service
package unlockedprofiles

import cats.effect.{IO, Resource}
import cats.implicits.catsSyntaxApplicativeId
import io.scalaland.chimney.dsl.*
import doobie.Transactor
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpes.profilesearch.testcontainers.MySqlDatabaseGenerator.WrappedTransactor
import weaver.{GlobalRead, IOSuite}

import java.time.LocalDate

class UnlockedProfileRepositorySpec(global: GlobalRead) extends IOSuite {
  override type Res = Transactor[IO]
  override def sharedResource: Resource[IO, Res] =
    global.getOrFailR[WrappedTransactor]().map(_.transactor)

  def createRepository(tableName: TableName, xa: Transactor[IO]): IO[UnlockedProfileRepository[IO]] =
    UnlockedProfileRepository.initResource[IO](tableName, xa).use(identity(_).pure)

  val randomTableName: IO[TableName] = IO.randomUUID.map(id => TableName(s"unlocked_profiles_$id".replace("-", "_")))

  def randomRepository(xa: Transactor[IO]): IO[UnlockedProfileRepository[IO]] = for {
    tableName  <- randomTableName
    repository <- createRepository(tableName, xa)
  } yield repository

  private val recruiterId = profilesearch.RecruiterId("1")
  private val profileId = profilesearch.ProfileId.unsafe("id")
  private val profileId2 = profilesearch.ProfileId.unsafe("id2")

  test("Get nothing when there is no unlocked profiles") { xa =>
    for {
      repository     <- randomRepository(xa)
      expirationDate <- repository.getExpirationDate(recruiterId, profileId)
    } yield expect(expirationDate.isEmpty)
  }

  test("Return an expiration date of an unlocked profile") { xa =>
    val expirationDate = LocalDate.of(2000, 1, 1)
    for {
      repository <- randomRepository(xa)
      _ <- repository.setExpirationDate(recruiterId.into[RecruiterId].transform, profileId.into[ProfileId].transform, expirationDate)
      foundExpirationDate <- repository.getExpirationDate(recruiterId, profileId)
    } yield expect(foundExpirationDate.contains(expirationDate))
  }

  test("Updates only a specific item when a fact is updated") { xa =>
    val expirationDateBefore = LocalDate.of(2000, 1, 1)
    val unrelatedExpirationDate = LocalDate.of(2001, 1, 1)
    val expirationDateAfter = LocalDate.of(2000, 2, 1)

    for {
      repository <- randomRepository(xa)
      _ <- repository.setExpirationDate(recruiterId.into[RecruiterId].transform, profileId.into[ProfileId].transform, expirationDateBefore)
      _ <- repository.setExpirationDate(
        recruiterId.into[RecruiterId].transform,
        profileId2.into[ProfileId].transform,
        unrelatedExpirationDate
      )
      _ <- repository.setExpirationDate(recruiterId.into[RecruiterId].transform, profileId.into[ProfileId].transform, expirationDateAfter)
      foundExpirationDate          <- repository.getExpirationDate(recruiterId, profileId)
      foundUnrelatedExpirationDate <- repository.getExpirationDate(recruiterId, profileId2)
    } yield expect(foundExpirationDate.contains(expirationDateAfter) && foundUnrelatedExpirationDate.contains(unrelatedExpirationDate))
  }

}
