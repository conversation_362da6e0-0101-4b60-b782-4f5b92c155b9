package nl.dpes.profilesearch.testcontainers

import cats.effect.{IO, Resource}
import nl.dpes.profilesearch.testcontainers.MySqlDatabaseGenerator.WrappedTransactor
import weaver.{GlobalResource, GlobalWrite}
import nl.dpes.profilesearch.testcontainers.ElasticSearchContainer.elasticClient

object SharedResources extends GlobalResource with MySqlDatabaseGenerator {
  def sharedResources(global: GlobalWrite): Resource[IO, Unit] = for {
    db      <- transactor
    _       <- global.putR(WrappedTransactor(db))
    eClient <- elasticClient
    _       <- global.putR(eClient)
  } yield ()
}
