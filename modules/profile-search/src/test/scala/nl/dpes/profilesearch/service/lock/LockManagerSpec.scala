package nl.dpes.profilesearch
package service.lock

import cats.effect.{IO, Ref, Resource}
import doobie.util.transactor.Transactor
import nl.dpes.profilesearch.service.lock.LockConfig.LockLimit
import nl.dpes.profilesearch.service.lock.LockingResult.*
import nl.dpes.profilesearch.service.viewedprofiles.ViewedProfilesRepositorySpec
import nl.dpes.profilesearch.testcontainers.MySqlDatabaseGenerator
import weaver.IOSuite

import java.util.UUID
import scala.concurrent.duration.*

object LockManagerSpec extends IOSuite with MySqlDatabaseGenerator {

  override type Res = Transactor[IO]

  override def sharedResource: Resource[IO, ViewedProfilesRepositorySpec.Res] = transactor

  test("LockManager should run in lock") { xa =>
    val lockService = new MysqlLockService()
    val config = LockConfig(maximumConcurrentLocks = LockLimit.unsafe(10), lockTimeout = 20.seconds)

    for {
      lockManager <- LockManager(lockService, xa, config)
      randomId    <- IO(UUID.randomUUID().toString)
      result      <- lockManager.runInLock(Key(randomId), IO.pure("Success"))
    } yield expect(result == "Success")
  }

  test("Concurrent requests are run sequentially") { xa =>
    val lockService = new LockService {
      override def acquire(key: Key, id: UUID): doobie.ConnectionIO[LockingResult] =
        LockAcquired(key, id).pure[doobie.ConnectionIO]

      override def release(key: Key, id: UUID): doobie.ConnectionIO[Unit] = ().pure[doobie.ConnectionIO]
    }
    val config = LockConfig(maximumConcurrentLocks = LockLimit.unsafe(10), lockTimeout = 20.seconds)

    def longProcess(results: Ref[IO, List[String]]): IO[Unit] =
      IO.sleep(1.second) >> results.update(_ :+ "Long running process completed")

    def shortProcess(results: Ref[IO, List[String]]): IO[Unit] =
      IO.sleep(100.millis) >> results.update(_ :+ "Short running process completed")

    for {
      lockManager <- LockManager(lockService, xa, config)
      randomId    <- IO(UUID.randomUUID().toString)
      ref         <- IO.ref(List[String]())
      firstProcess <- lockManager
        .runInLock(Key(randomId), longProcess(ref))
        .start
      lastProcess <- IO.sleep(500.millis) >> lockManager
        .runInLock(Key(randomId), shortProcess(ref))
        .start
      _      <- firstProcess.join
      _      <- lastProcess.join
      result <- ref.get
      _      <- IO.println(result)
    } yield expect(result.head == "Long running process completed") && expect(result.last == "Short running process completed")
  }

  test("it should timeout when the process does not finish in time") { xa =>
    val lockService = new LockService {
      override def acquire(key: Key, id: UUID): doobie.ConnectionIO[LockingResult] =
        LockAcquired(key, id).pure[doobie.ConnectionIO]

      override def release(key: Key, id: UUID): doobie.ConnectionIO[Unit] = ().pure[doobie.ConnectionIO]
    }
    val config = LockConfig(maximumConcurrentLocks = LockLimit.unsafe(10), lockTimeout = 1.second)

    for {
      lockManager <- LockManager(lockService, xa, config)
      randomId    <- IO(UUID.randomUUID().toString)
      result      <- lockManager.runInLock(Key(randomId), IO.never >> IO.pure("This should timeout")).attempt
    } yield result match {
      case Left(LockTimedOut(key, duration)) => expect(key == Key(randomId)) && expect(duration == 1.second)
      case _                                 => failure("The lock should have timed out")
    }
  }

  test("Any error in the process should be propagated") { xa =>
    val lockService = new LockService {
      override def acquire(key: Key, id: UUID): doobie.ConnectionIO[LockingResult] =
        LockAcquired(key, id).pure[doobie.ConnectionIO]

      override def release(key: Key, id: UUID): doobie.ConnectionIO[Unit] = ().pure[doobie.ConnectionIO]
    }
    val config = LockConfig(maximumConcurrentLocks = LockLimit.unsafe(10), lockTimeout = 20.seconds)

    for {
      lockManager <- LockManager(lockService, xa, config)
      randomId    <- IO(UUID.randomUUID().toString)
      error       <- lockManager.runInLock(Key(randomId), IO.raiseError(new RuntimeException("Test error"))).attempt
    } yield error match {
      case Left(e: RuntimeException) => expect(e.getMessage == "Test error")
      case _                         => failure("The error should have been propagated")
    }
  }
}
