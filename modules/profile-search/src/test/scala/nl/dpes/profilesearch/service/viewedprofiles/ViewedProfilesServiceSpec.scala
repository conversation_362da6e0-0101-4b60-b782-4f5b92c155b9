package nl.dpes.profilesearch.service.viewedprofiles

import cats.effect.IO
import weaver.SimpleIOSuite
import nl.dpes.profilesearch.service.model.profilesearch

object ViewedProfilesServiceSpec extends SimpleIOSuite {

  case class Error(message: String) extends Throwable(message)

  val unit: Unit = ()
  val profileId: profilesearch.ProfileId = profilesearch.ProfileId("123456789123456789").toOption.get
  val recruiterId: profilesearch.RecruiterId = profilesearch.RecruiterId("123456789123456789123456789123456789")
  val lastViewedDate: LastViewedDate = LastViewedDate(1738845299914L)

  def repo(
    saveFn: => IO[Unit] = IO(unit),
    getLastViewedDateFn: IO[Option[LastViewedDate]] = IO(Some(lastViewedDate))
  ): ViewedProfilesRepository[IO] =
    new ViewedProfilesRepository[IO] {
      override def initialize: IO[Unit] = ???
      override def cleanup(retentionThresholdInDays: Long): IO[Unit] = ???
      override def save(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): IO[Unit] = saveFn
      override def getLastViewedDate(
        recruiterId: profilesearch.RecruiterId,
        profileId: profilesearch.ProfileId
      ): IO[Option[LastViewedDate]] =
        getLastViewedDateFn
    }

  test("It should be able to save a profile view") {
    for {
      service <- IO(ViewedProfilesService.impl[IO](repo()))
      result  <- service.save(recruiterId, profileId)
    } yield expect(result == unit)
  }

  test("It should be able to get the last viewed date for a profile") {
    for {
      service <- IO(ViewedProfilesService.impl[IO](repo()))
      result  <- service.getLastViewedDate(recruiterId, profileId)
    } yield result match {
      case Some(viewedDate) => expect(viewedDate.timestamp == lastViewedDate.timestamp)
      case None             => failure("This test should succeed")
    }
  }
}
