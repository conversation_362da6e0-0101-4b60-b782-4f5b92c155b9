package nl.dpes.profilesearch.service.elastic

import cats.effect.IO
import cats.effect.kernel.Resource
import cats.implicits.*
import com.sksamuel.elastic4s.ElasticClient
import com.sksamuel.elastic4s.cats.effect.instances.*
import nl.dpes.profilesearch.service.ProfileSearchService
import nl.dpes.profilesearch.service.lock.LockManager
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpes.profilesearch.service.viewedprofiles.ViewedProfilesService
import nl.dpes.profilesearch.service.visitedprofiles.ProfileView
import nl.dpes.profilesearch.service.visitedprofiles.VisitedProfilesService
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.*

class BaseElasticSearch(global: GlobalRead) extends IOSuite {
  given LoggerFactory[IO] = Slf4jFactory.create[IO]

  override type Res = ElasticClient

  override def sharedResource: Resource[IO, ElasticClient] =
    global.getOrFailR[ElasticClient]()

  protected val lastViewedDate: profilesearch.LastViewedDate = profilesearch.LastViewedDate(1738845299914L)
  protected val profile: profilesearch.ProfileSearchResult = profilesearch.ProfileSearchResult(
    id = profilesearch.ProfileId.unsafe("7cd63c42-1234-1234-1234-19fb61915119"),
    name = Some(profilesearch.Name(profilesearch.FirstName("John"), profilesearch.LastName("Doe"))),
    updatedDate = profilesearch.UpdatedDate(1288566000),
    workingHours = Some(profilesearch.WorkingHours(36, 40)),
    workLevels = List(profilesearch.WorkLevel("HBO")),
    availability = None,
    experiences =
      List(profilesearch.Experience("Recruiter"), profilesearch.Experience("UX Designer"), profilesearch.Experience("English teacher")),
    preferredJobs = List(profilesearch.PreferredJob("management assistant")),
    photo = Some(profilesearch.Photo("profile-photos/7cd63c42-1234-1234-1234-01fb6a415123/152dea87-1234-1234-1234-dfb501ab1234.jpg")),
    city = Some(profilesearch.City("Amsterdam")),
    lastViewedDate = None
  )

  protected def visitedProfilesService(
    registerFn: (profilesearch.RecruiterId, profilesearch.ProfileId) => IO[ProfileView] = (_, _) => IO(ProfileView.New)
  ): VisitedProfilesService[IO] =
    (recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId) => registerFn(recruiterId, profileId)

  def viewedProfilesService(
    saveFn: (profilesearch.RecruiterId, profilesearch.ProfileId) => IO[Unit] = (_, _) => IO(()),
    getLastViewedDateFn: => IO[Option[profilesearch.LastViewedDate]] = IO(Some(lastViewedDate))
  ): ViewedProfilesService[IO] = new ViewedProfilesService[IO] {
    override def save(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): IO[Unit] = saveFn(recruiterId, profileId)

    override def getLastViewedDate(
      recruiterId: profilesearch.RecruiterId,
      profileId: profilesearch.ProfileId
    ): IO[Option[profilesearch.LastViewedDate]] =
      getLastViewedDateFn
  }

  protected def pagination(number: Int, size: Int): IO[profilesearch.pagination.Pagination] =
    IO.fromEither(
      (profilesearch.pagination.PageNumber(number), profilesearch.pagination.PageSize(size))
        .parMapN(profilesearch.pagination.Pagination.apply)
        .leftMap(msg => new Exception(msg))
    )

  protected def profileSearchService(
    esIndex: String,
    client: ElasticClient,
    visitedProfilesService: VisitedProfilesService[IO],
    viewedProfilesService: ViewedProfilesService[IO],
    lockManager: LockManager[IO]
  ): ProfileSearchService[IO] = ProfileSearchService.impl[IO](esIndex)(client, visitedProfilesService, viewedProfilesService, lockManager)
}
