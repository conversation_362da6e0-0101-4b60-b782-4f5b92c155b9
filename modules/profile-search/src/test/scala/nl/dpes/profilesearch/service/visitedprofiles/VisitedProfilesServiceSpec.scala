package nl.dpes.profilesearch.service.visitedprofiles

import cats.*
import cats.effect.*
import cats.effect.testing.scalatest.AsyncIOSpec
import nl.dpes.profilesearch.creditservice.CreditServiceClient.NotEntitled
import nl.dpes.profilesearch.creditservice.{CorrelationId, CreditService, EntitlementId}
import nl.dpes.profilesearch.service.unlockedprofiles.UnlockedProfileService
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpes.profilesearch.creditservice
import nl.dpes.profilesearch.service.visitedprofiles.VisitedProfilesService.ViewsExceeded
import io.scalaland.chimney.dsl.*
import org.scalamock.scalatest.AsyncMockFactory
import org.scalatest.funsuite.AsyncFunSuite
import org.typelevel.log4cats.testing.StructuredTestingLogger
import org.typelevel.log4cats.testing.StructuredTestingLogger.*
import org.typelevel.log4cats.{LoggerFactory, SelfAwareStructuredLogger}

class VisitedProfilesServiceSpec extends AsyncFunSuite with AsyncMockFactory with AsyncIOSpec {

  private val recruiterId = profilesearch.RecruiterId("1")
  private val profileId = profilesearch.ProfileId.unsafe("1")
  private val profileId2 = profilesearch.ProfileId.unsafe("2")

  test("Registering a profile view will return a new ProfileView") {
    for {
      deps <- getDependencies[IO]()
      // Given
      _ = deps.visitedProfilesRepository.read
        .expects(recruiterId)
        .returns(IO.pure(Set()))
        .atLeastOnce()

      _ = deps.visitedProfilesRepository.update
        .expects(recruiterId, *)
        .returns(IO.unit)
        .atLeastOnce()

      _ = deps.unlockedProfileService.profileIsUnlocked
        .expects(recruiterId, profileId)
        .returns(IO.pure(true))
        .atLeastOnce()
      service = deps.visitedProfileService

      // When
      profileView <- service.registerProfileView(recruiterId, profileId)
      logs        <- deps.logger.logged

      // Then
    } yield assert(
      profileView == ProfileView.New && logs == Seq(
        INFO(s"Recruiter '$recruiterId' has NOT used an entitlement to view profile '${profileId.value}'", None, Map.empty),
        INFO(s"Recruiter '$recruiterId' is allowed to view profile '${profileId.value}': New", None, Map.empty)
      )
    )
  }

  test("Registering the same profile view twice will return an Existing ProfileView") {
    for {
      deps <- getDependencies[IO]()
      // Given
      _ = deps.visitedProfilesRepository.read
        .expects(recruiterId)
        .returns(IO.pure(Set()))
        .noMoreThanOnce()

      _ = deps.visitedProfilesRepository.update
        .expects(recruiterId, *)
        .returns(IO.unit)

      _ = deps.unlockedProfileService.profileIsUnlocked
        .expects(recruiterId, profileId)
        .returns(IO.pure(true))

      _ = deps.visitedProfilesRepository.read
        .expects(recruiterId)
        .returns(IO.pure(Set(profileId)))
        .noMoreThanOnce()

      service = deps.visitedProfileService

      // When
      _           <- service.registerProfileView(recruiterId, profileId)
      profileView <- service.registerProfileView(recruiterId, profileId)
      logs        <- deps.logger.logged
      // Then
    } yield assert(
      profileView == ProfileView.Existing && logs == Seq(
        INFO(s"Recruiter '$recruiterId' has NOT used an entitlement to view profile '${profileId.value}'", None, Map.empty),
        INFO(s"Recruiter '$recruiterId' is allowed to view profile '${profileId.value}': New", None, Map.empty),
        INFO(s"Recruiter '$recruiterId' is allowed to view profile '${profileId.value}': Existing", None, Map.empty)
      )
    )

  }

  test("Registering a profile view when the limit is reached will return an Exceeded ProfileView") {
    given LoggerFactory[IO] = getLoggerFactory(StructuredTestingLogger.impl[IO]())
    val visitedProfilesRepository = mock[VisitedProfilesRepository[IO]]
    val creditService = mock[CreditService[IO]]
    val unlockedProfileService = mock[UnlockedProfileService[IO]]

    val visitedProfileService = VisitedProfilesService
      .resource[IO](
        viewLimit = ProfileViewLimit(1),
        repository = visitedProfilesRepository,
        creditService = creditService,
        unlockedProfileService = unlockedProfileService
      )
      .use(IO.pure)

    for {
      deps <- getDependencies[IO]()
      // Given
      _ = deps.visitedProfilesRepository.read
        .expects(recruiterId)
        .returns(IO.pure(Set()))
        .noMoreThanOnce()

      _ = deps.visitedProfilesRepository.update
        .expects(recruiterId, *)
        .returns(IO.unit)

      _ = deps.unlockedProfileService.profileIsUnlocked
        .expects(recruiterId, profileId)
        .returns(IO.pure(true))

      _ = deps.visitedProfilesRepository.read
        .expects(recruiterId)
        .returns(IO.pure(Set(profileId)))
        .noMoreThanOnce()

      service = deps.visitedProfileService
      // When
      _           <- service.registerProfileView(recruiterId, profileId)
      profileView <- service.registerProfileView(recruiterId, profileId2).attempt
      // Then
    } yield assert(profileView == Left(ViewsExceeded(recruiterId.into[RecruiterId].transform, profileId2.into[ProfileId].transform)))
  }

  test("When the user is not entitled to view a profile an error is returned") {
    val correlationId = CorrelationId("correlation-id")

    for {
      deps <- getDependencies[IO](ProfileViewLimit(10))
      // Given
      _ = deps.visitedProfilesRepository.read
        .expects(recruiterId)
        .returns(IO.pure(Set()))

      _ = deps.unlockedProfileService.profileIsUnlocked
        .expects(recruiterId, profileId)
        .returns(IO.pure(false))

      _ = deps.creditService.useProfileView
        .expects(recruiterId)
        .returns(IO.raiseError(NotEntitled(recruiterId.into[creditservice.RecruiterId].transform, correlationId)))

      service = deps.visitedProfileService
      // When
      profileView <- service.registerProfileView(recruiterId, profileId).attempt
      // Then
    } yield assert(profileView == Left(NotEntitled(recruiterId.into[creditservice.RecruiterId].transform, correlationId)))
  }

  test("An unlocked profile doesn't need to use an entitlement and be unlocked") {
    for {
      deps <- getDependencies[IO]()
      // Given
      _ = deps.visitedProfilesRepository.read
        .expects(recruiterId)
        .returns(IO.pure(Set()))
        .atLeastOnce()

      _ = deps.visitedProfilesRepository.update
        .expects(recruiterId, *)
        .returns(IO.unit)
        .atLeastOnce()

      _ = deps.unlockedProfileService.profileIsUnlocked
        .expects(recruiterId, profileId)
        .returns(IO.pure(true))
        .atLeastOnce()

      // When
      _    <- deps.visitedProfileService.registerProfileView(recruiterId, profileId)
      logs <- deps.logger.logged
      // Then
    } yield assert(
      logs == Seq(
        INFO(s"Recruiter '$recruiterId' has NOT used an entitlement to view profile '${profileId.value}'", None, Map.empty),
        INFO(s"Recruiter '$recruiterId' is allowed to view profile '${profileId.value}': New", None, Map.empty)
      )
    )
  }

  test("A locked profile needs to use an entitlement and be unlocked") {
    for {
      deps <- getDependencies[IO]()
      // Given
      _ = deps.visitedProfilesRepository.read.expects(recruiterId).returns(IO.pure(Set()))
      _ = deps.visitedProfilesRepository.update.expects(recruiterId, *).returns(IO.unit)
      _ = deps.unlockedProfileService.profileIsUnlocked.expects(recruiterId, profileId).returns(IO.pure(false))
      _ = deps.creditService.useProfileView.expects(recruiterId).returns(IO.pure(EntitlementId("entitlement-id"))).once()
      _ = deps.unlockedProfileService.unlockProfile.expects(recruiterId, profileId).returns(IO.unit).atLeastOnce()

      // When
      _    <- deps.visitedProfileService.registerProfileView(recruiterId, profileId)
      logs <- deps.logger.logged
      // Then
    } yield assert(
      logs.toSet == Set(
        INFO(s"Recruiter '$recruiterId' is allowed to view profile '${profileId.value}': New", None, Map.empty),
        INFO(s"Recruiter '$recruiterId' has used entitlement 'entitlement-id' to view profile '${profileId.value}'", None, Map.empty)
      )
    )
  }

  private def getLoggerFactory[F[_]: Monad](logger: StructuredTestingLogger[F]) = new LoggerFactory[F] {
    override def getLoggerFromName(name: String): SelfAwareStructuredLogger[F] = logger
    override def fromName(name: String): F[SelfAwareStructuredLogger[F]] = Monad[F].pure(getLoggerFromName(name))
  }

  private def getDependencies[F[_]: Sync](viewLimit: ProfileViewLimit = ProfileViewLimit(1)): F[Dependencies[F]] = {
    import cats.implicits.toFunctorOps

    val logger = StructuredTestingLogger.impl[F]()
    given LoggerFactory[F] = getLoggerFactory(logger)
    val visitedProfilesRepository = mock[VisitedProfilesRepository[F]]
    val creditService = mock[CreditService[F]]
    val unlockedProfileService = mock[UnlockedProfileService[F]]

    val visitedProfileService: F[VisitedProfilesService[F]] = VisitedProfilesService
      .resource[F](
        viewLimit = viewLimit,
        repository = visitedProfilesRepository,
        creditService = creditService,
        unlockedProfileService = unlockedProfileService
      )
      .use(Monad[F].pure)

    for {
      service <- visitedProfileService
    } yield Dependencies(
      logger,
      visitedProfilesRepository,
      creditService,
      unlockedProfileService,
      service
    )

  }

  case class Dependencies[F[_]](
    logger: StructuredTestingLogger[F],
    visitedProfilesRepository: VisitedProfilesRepository[F],
    creditService: CreditService[F],
    unlockedProfileService: UnlockedProfileService[F],
    visitedProfileService: VisitedProfilesService[F]
  )
}
