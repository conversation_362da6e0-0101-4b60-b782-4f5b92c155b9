package nl.dpes.profilesearch.mapper.aggregation

import cats.data.NonEmptyList
import cats.effect.IO
import cats.effect.unsafe.implicits.global
import com.sksamuel.elastic4s.requests.searches.aggs.{Aggregation, FilterAggregation}
import com.sksamuel.elastic4s.requests.searches.queries.compound.BoolQuery
import com.sksamuel.elastic4s.requests.searches.queries.matches.MatchAllQuery
import com.sksamuel.elastic4s.requests.searches.term.TermsQuery
import nl.dpes.profilesearch.domain.{CareerLevel, FunctionGroup}
import nl.dpes.profilesearch.domain.filter.{CareerLevelFilter, Filter, FunctionGroupFilter}
import nl.dpes.profilesearch.mapper.FilterMapper
import org.scalamock.scalatest.MockFactory
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

// todo: Implement tests
class FilterToAggregationMapperSpec extends AnyFlatSpec with Matchers with MockFactory {
  "Filter aggregations" should "be present in the global aggregation" in {
    val filterExclusion = mock[FilterExclusion]
    val filters = Seq(
      FunctionGroupFilter(NonEmptyList.of(FunctionGroup("AdministratiefSecretarieel"), FunctionGroup("FinancieelAccounting")))
    )

    filterExclusion.getFiltersForAggregation
      .expects(*, *)
      .returns(filters)
      .anyNumberOfTimes()

    val filterToAggregationMapper =
      new FilterToAggregationMapper[IO](
        FilterMapper.default[IO],
        filterExclusion
      )

    val result = filterToAggregationMapper.getAggregations(
      List(
        CareerLevelFilter(NonEmptyList.of(CareerLevel("Directie"))),
        FunctionGroupFilter(NonEmptyList.of(FunctionGroup("AdministratiefSecretarieel"), FunctionGroup("FinancieelAccounting")))
      )
    )

    val aggregations = result.unsafeRunSync()

    assert(filterAggregationExists(aggregations, "availabilities"))
    assert(filterAggregationExists(aggregations, "careerLevels"))
    assert(filterAggregationExists(aggregations, "driverLicenses"))
    assert(filterAggregationExists(aggregations, "functionGroups"))
    assert(filterAggregationExists(aggregations, "languages"))
    assert(filterAggregationExists(aggregations, "provinces"))
    assert(filterAggregationExists(aggregations, "requestedSalaries"))
    assert(filterAggregationExists(aggregations, "updatedDate"))
    assert(filterAggregationExists(aggregations, "workingHours"))
    assert(filterAggregationExists(aggregations, "workLevels"))

    assert(
      checkFiltersOfAggregation(
        aggregations = aggregations,
        aggregationName = "careerLevels",
        filterName = "functionGroups",
        filterValues = Seq(
          "AdministratiefSecretarieel",
          "FinancieelAccounting"
        )
      )
    )
  }

  "For empty aggregations there" should "be a match all query" in {
    val filterExclusion = mock[FilterExclusion]
    val filters = Seq.empty[Filter]

    filterExclusion.getFiltersForAggregation
      .expects(*, *)
      .returns(filters)
      .anyNumberOfTimes()

    val filterToAggregationMapper =
      new FilterToAggregationMapper[IO](
        FilterMapper.default[IO],
        filterExclusion
      )

    val result = filterToAggregationMapper.getAggregations(List.empty)

    val aggregations = result.unsafeRunSync()

    assert(checkForMatchAllQuery(aggregations, "availabilities"))
  }

  private def filterAggregationExists(
    aggregations: List[Aggregation],
    aggregationName: String
  ): Boolean = aggregations.head.subaggs
    .exists { case aggregation: FilterAggregation => aggregation.name == aggregationName }

  private def checkFiltersOfAggregation(
    aggregations: List[Aggregation],
    aggregationName: String,
    filterName: String,
    filterValues: Seq[String]
  ): Boolean = aggregations.head.subaggs
    .map { case aggregation: FilterAggregation => (aggregation.name, aggregation.query) }
    .find(_._1 == aggregationName)
    .map(_._2)
    .exists { case boolQuery: BoolQuery =>
      boolQuery.must
        .collect { case termsQuery @ TermsQuery(field, values, boost, ref, routing, path, queryName) =>
          (termsQuery.field, termsQuery.values)
        }
        .exists { case (field, values) =>
          field == filterName && values == filterValues
        }
    }

  private def checkForMatchAllQuery(
    aggregations: List[Aggregation],
    aggregationName: String
  ): Boolean = aggregations.head.subaggs
    .map { case aggregation: FilterAggregation => (aggregation.name, aggregation.query) }
    .find(_._1 == aggregationName)
    .map(_._2)
    .exists { case boolQuery: BoolQuery =>
      boolQuery.must.collect { case matchAllQuery: MatchAllQuery => matchAllQuery }.nonEmpty
    }
}
