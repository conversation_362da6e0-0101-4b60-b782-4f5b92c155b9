package nl.dpes.profilesearch
package service.dbmodel

import nl.dpes.profilesearch.service.model.dbmodel.{Experience, Month, MonthOfYear, Year}
import weaver.FunSuite

object ExperienceSpec extends FunSuite {
  test("An experience starting after another one will be first in the list") {
    val unsorted = List(
      Experience(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none),
      Experience(MonthOfYear(Month(1).some, Year(2002)).some, MonthOfYear(Month(10).some, Year(2002)).some, none, none, none)
    )
    val sorted = List(
      Experience(MonthOfYear(Month(1).some, Year(2002)).some, MonthOfYear(Month(10).some, Year(2002)).some, none, none, none),
      Experience(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none)
    )

    expect(unsorted.sorted == sorted)
  }

  test("An experience starting during another one but ending after it will be first in the list") {
    val unsorted = List(
      Experience(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none),
      Experience(MonthOfYear(Month(5).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2002)).some, none, none, none)
    )
    val sorted = List(
      Experience(MonthOfYear(Month(5).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2002)).some, none, none, none),
      Experience(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none)
    )

    expect(unsorted.sorted == sorted)
  }

  test("An experience starting during another one and ending before it will be last in the list") {
    val unsorted = List(
      Experience(MonthOfYear(Month(5).some, Year(2001)).some, MonthOfYear(Month(9).some, Year(2001)).some, none, none, none),
      Experience(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none)
    )
    val sorted = List(
      Experience(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none),
      Experience(MonthOfYear(Month(5).some, Year(2001)).some, MonthOfYear(Month(9).some, Year(2001)).some, none, none, none)
    )

    expect(unsorted.sorted == sorted)
  }

  test("An experience without a start date will be last in the list") {
    val unsorted = List(
      Experience(none, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none),
      Experience(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none)
    )
    val sorted = List(
      Experience(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none),
      Experience(none, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none)
    )

    expect(unsorted.sorted == sorted)
  }

  test("An experience without an end date will be first in the list") {
    val unsorted = List(
      Experience(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none),
      Experience(MonthOfYear(Month(1).some, Year(2001)).some, none, none, none, none)
    )
    val sorted = List(
      Experience(MonthOfYear(Month(1).some, Year(2001)).some, none, none, none, none),
      Experience(MonthOfYear(Month(1).some, Year(2001)).some, MonthOfYear(Month(10).some, Year(2001)).some, none, none, none)
    )

    expect(unsorted.sorted == sorted)
  }
}
