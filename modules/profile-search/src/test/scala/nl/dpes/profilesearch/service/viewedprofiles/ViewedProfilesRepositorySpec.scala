package nl.dpes.profilesearch.service.viewedprofiles

import cats.implicits.*
import cats.effect.*
import doobie.*
import doobie.implicits.*
import doobie.util.meta.Meta
import doobie.util.transactor.Transactor
import nl.dpes.profilesearch.service.TableName
import nl.dpes.profilesearch.testcontainers.MySqlDatabaseGenerator
import nl.dpes.profilesearch.service.model.profilesearch.{ProfileId, RecruiterId}
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.IOSuite

import scala.concurrent.duration.*
import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.time.Instant
import java.util.{Date, UUID}

object ViewedProfilesRepositorySpec extends IOSuite with MySqlDatabaseGenerator {

  given Meta[Instant] = Meta[Timestamp].imap(_.toInstant)(Timestamp.from)
  given LoggerFactory[IO] = Slf4jFactory.create[IO]

  override type Res = Transactor[IO]

  override def sharedResource: Resource[IO, ViewedProfilesRepositorySpec.Res] = transactor

  case class Repo(viewedProfilesRepo: ViewedProfilesRepository[IO], tableName: TableName)

  private val recruiterId: RecruiterId = RecruiterId("This a recruiter id")
  private val profileId: ProfileId = ProfileId("profile 1").toOption.get
  private val profileId2: ProfileId = ProfileId("profile 2").toOption.get
  private val profileId3: ProfileId = ProfileId("profile 3").toOption.get
  private val lastViewedDateLong: Long = 1751958970

  def randomRepo(xa: Transactor[IO]): Resource[IO, Repo] = {
    val id = UUID.randomUUID().toString.replace("-", "")
    for {
      repo <- ViewedProfilesRepository.resource(TableName(s"viewed_profiles_$id".trim), 365, xa)
    } yield Repo(repo, TableName(s"viewed_profiles_$id".trim))
  }

  test("It should be able to create the table in an idempotent way") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _       <- repo.viewedProfilesRepo.initialize.attempt
        result  <- repo.viewedProfilesRepo.initialize.attempt
        columns <- getColumnNames(repo.tableName.fragment, xa)
      } yield expect(
        result == Right(()) &&
          columns.sorted == List(
            "lastViewedDate",
            "profileId",
            "recruiterId"
          )
      )
    }
  }

  test("It should be able to save profile views") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _     <- repo.viewedProfilesRepo.save(recruiterId, profileId)
        _     <- repo.viewedProfilesRepo.save(recruiterId, profileId2)
        _     <- repo.viewedProfilesRepo.save(recruiterId, profileId3)
        _     <- repo.viewedProfilesRepo.save(RecruiterId("recruiter 2"), profileId)
        views <- getViews(recruiterId, repo.tableName.fragment, xa)
      } yield expect(views == List(profileId, profileId2, profileId3))
    }
  }

  test("It should update the 'lastViewedDate' with the most recent profile view date") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _          <- repo.viewedProfilesRepo.save(recruiterId, profileId)
        timestamp1 <- getTimestamp(recruiterId, profileId, repo.tableName.fragment, xa)
        _          <- IO.sleep(1.second)
        _          <- repo.viewedProfilesRepo.save(recruiterId, profileId)
        timestamp2 <- getTimestamp(recruiterId, profileId, repo.tableName.fragment, xa)
        _          <- IO.sleep(1.second)
        _          <- repo.viewedProfilesRepo.save(recruiterId, profileId)
        timestamp3 <- getTimestamp(recruiterId, profileId, repo.tableName.fragment, xa)
      } yield expect(timestamp1.isBefore(timestamp2) && timestamp2.isBefore(timestamp3))
    }
  }

  test("There is an event that cleans up the old profile views") { xa =>
    randomRepo(xa).use { _ =>
      for {
        eventName <- sql"""
                          SELECT EVENT_NAME
                          FROM information_schema.EVENTS
                          WHERE EVENT_NAME = 'CleanupViewedProfilesEvent'
                        """.query[String].option.transact(xa)
      } yield expect(eventName.contains("CleanupViewedProfilesEvent"))
    }
  }

  test("It should try get the last viewed date for a profile") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _ <- repo.viewedProfilesRepo.save(recruiterId, profileId)
        _ <- updateLastViewDate(recruiterId, profileId, Instant.ofEpochSecond(lastViewedDateLong), repo.tableName.fragment, xa)
        lastViewedDate1 <- repo.viewedProfilesRepo.getLastViewedDate(recruiterId, profileId)
      } yield lastViewedDate1 match {
        case Some(date) => expect(getStringDate(date.timestamp) == getStringDate(lastViewedDateLong))
        case None       => failure("This test should succeed")
      }
    }
  }

  test("It should return None when trying to get the last viewed date for a non-existent profile") { xa =>
    randomRepo(xa).use { repo =>
      for {
        lastViewedDate1 <- repo.viewedProfilesRepo.getLastViewedDate(recruiterId, profileId)
      } yield expect(lastViewedDate1.isEmpty)
    }
  }

  def getStringDate(millis: Long): String = {
    val date = new Date(millis)
    val formatter = new SimpleDateFormat("yyyy-MM-dd")
    formatter.format(date)
  }

  def getColumnNames(tableName: Fragment, xa: Transactor[IO]): IO[List[String]] =
    sql"""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = '$tableName'
       """
      .query[String]
      .to[List]
      .transact(xa)

  def getViews(recruiterId: RecruiterId, tableName: Fragment, xa: Transactor[IO]): IO[List[ProfileId]] =
    sql"""
        SELECT profileId
        FROM $tableName
        WHERE recruiterId = $recruiterId
        ORDER BY lastViewedDate
       """
      .query[ProfileId]
      .to[List]
      .transact(xa)

  def getTimestamp(recruiterId: RecruiterId, profileId: ProfileId, tableName: Fragment, xa: Transactor[IO]): IO[Instant] =
    sql"""
        SELECT lastViewedDate
        FROM $tableName
        WHERE recruiterId = $recruiterId AND profileId = $profileId
       """
      .query[Instant]
      .unique
      .transact(xa)

  def updateLastViewDate(
    recruiterId: RecruiterId,
    profileId: ProfileId,
    timestamp: Instant,
    tableName: Fragment,
    xa: Transactor[IO]
  ): IO[Unit] =
    sql"""
        UPDATE $tableName
        SET lastViewedDate = $timestamp
        WHERE recruiterId = $recruiterId AND profileId = $profileId
      """.update.run.transact(xa).void

}
