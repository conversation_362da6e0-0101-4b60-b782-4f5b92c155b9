package nl.dpes.profilesearch.mapper.aggregation

import cats.data.NonEmptyList
import nl.dpes.profilesearch.domain.*
import nl.dpes.profilesearch.domain.filter.*
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class FilterExclusionSpec extends AnyFlatSpec with Matchers {
  private val availabilityFilter = AvailabilityFilter(NonEmptyList.of(Availability.InOverleg))
  private val careerLevelFilter = CareerLevelFilter(NonEmptyList.of(CareerLevel("Directie")))
  private val driversLicenseFilter = DriversLicenseFilter(NonEmptyList.of(DriversLicense("B")))
  private val functionGroupFilter = FunctionGroupFilter(
    NonEmptyList.of(FunctionGroup("AdministratiefSecretarieel"), FunctionGroup("FinancieelAccounting"))
  )
  private val languageFilter = LanguageFilter(NonEmptyList.of(Language("Dutch")))
  private val provinceFilter = ProvinceFilter(NonEmptyList.of(Province("Amsterdam")))
  private val requestedSalaryFilter = RequestedSalaryFilter(NonEmptyList.of(RequestedSalary.MoreThan7000))
  private val updateDateFilter = UpdateDateFilter(UpdateDate.All)
  private val workingHourFilter = WorkingHourFilter(NonEmptyList.of(WorkingHours.UpTo16Hours))
  private val workLevelFilter = WorkLevelFilter(NonEmptyList.of(WorkLevel("Mbo")))

  private val filterExclusion = FilterExclusion.default

  "Availability" should "exclude availability filters" in {
    val result = filterExclusion.getFiltersForAggregation(
      aggregation = Aggregation.Availability,
      getAllFilters
    )

    result.toSet shouldBe Set(
      careerLevelFilter,
      driversLicenseFilter,
      functionGroupFilter,
      languageFilter,
      provinceFilter,
      requestedSalaryFilter,
      updateDateFilter,
      workingHourFilter,
      workLevelFilter
    )
  }

  "CareerLevel" should "exclude career level filters" in {
    val result = filterExclusion.getFiltersForAggregation(
      aggregation = Aggregation.CareerLevel,
      getAllFilters
    )

    result.toSet shouldBe Set(
      availabilityFilter,
      driversLicenseFilter,
      functionGroupFilter,
      languageFilter,
      provinceFilter,
      requestedSalaryFilter,
      updateDateFilter,
      workingHourFilter,
      workLevelFilter
    )
  }

  "DriverLicense" should "exclude driver license filters" in {
    val result = filterExclusion.getFiltersForAggregation(
      aggregation = Aggregation.DriverLicense,
      getAllFilters
    )

    result.toSet shouldBe Set(
      availabilityFilter,
      careerLevelFilter,
      functionGroupFilter,
      languageFilter,
      provinceFilter,
      requestedSalaryFilter,
      updateDateFilter,
      workingHourFilter,
      workLevelFilter
    )
  }

  "FunctionGroup" should "exclude function group filters" in {
    val result = filterExclusion.getFiltersForAggregation(
      aggregation = Aggregation.FunctionGroup,
      getAllFilters
    )

    result.toSet shouldBe Set(
      availabilityFilter,
      careerLevelFilter,
      driversLicenseFilter,
      languageFilter,
      provinceFilter,
      requestedSalaryFilter,
      updateDateFilter,
      workingHourFilter,
      workLevelFilter
    )
  }

  "Language" should "exclude language filters" in {
    val result = filterExclusion.getFiltersForAggregation(
      aggregation = Aggregation.Language,
      getAllFilters
    )

    result.toSet shouldBe Set(
      availabilityFilter,
      careerLevelFilter,
      driversLicenseFilter,
      functionGroupFilter,
      provinceFilter,
      requestedSalaryFilter,
      updateDateFilter,
      workingHourFilter,
      workLevelFilter
    )
  }

  "Province" should "exclude province filters" in {
    val result = filterExclusion.getFiltersForAggregation(
      aggregation = Aggregation.Province,
      getAllFilters
    )

    result.toSet shouldBe Set(
      availabilityFilter,
      careerLevelFilter,
      driversLicenseFilter,
      functionGroupFilter,
      languageFilter,
      requestedSalaryFilter,
      updateDateFilter,
      workingHourFilter,
      workLevelFilter
    )
  }

  "RequestedSalary" should "exclude requested salary filters" in {
    val result = filterExclusion.getFiltersForAggregation(
      aggregation = Aggregation.RequestedSalary,
      getAllFilters
    )

    result.toSet shouldBe Set(
      availabilityFilter,
      careerLevelFilter,
      driversLicenseFilter,
      functionGroupFilter,
      languageFilter,
      provinceFilter,
      updateDateFilter,
      workingHourFilter,
      workLevelFilter
    )
  }

  "UpdatedDate" should "exclude updated date filters" in {
    val result = filterExclusion.getFiltersForAggregation(
      aggregation = Aggregation.UpdatedDate,
      getAllFilters
    )

    result.toSet shouldBe Set(
      availabilityFilter,
      careerLevelFilter,
      driversLicenseFilter,
      functionGroupFilter,
      languageFilter,
      provinceFilter,
      requestedSalaryFilter,
      workingHourFilter,
      workLevelFilter
    )
  }

  "WorkingHours" should "exclude working hours filters" in {
    val result = filterExclusion.getFiltersForAggregation(
      aggregation = Aggregation.WorkingHours,
      getAllFilters
    )

    result.toSet shouldBe Set(
      availabilityFilter,
      careerLevelFilter,
      driversLicenseFilter,
      functionGroupFilter,
      languageFilter,
      provinceFilter,
      requestedSalaryFilter,
      updateDateFilter,
      workLevelFilter
    )
  }

  "WorkLevel" should "exclude work level filters" in {
    val result = filterExclusion.getFiltersForAggregation(
      aggregation = Aggregation.WorkLevel,
      getAllFilters
    )

    result.toSet shouldBe Set(
      availabilityFilter,
      careerLevelFilter,
      driversLicenseFilter,
      functionGroupFilter,
      languageFilter,
      provinceFilter,
      requestedSalaryFilter,
      updateDateFilter,
      workingHourFilter
    )
  }

  private def getAllFilters =
    List(
      availabilityFilter,
      careerLevelFilter,
      driversLicenseFilter,
      functionGroupFilter,
      languageFilter,
      provinceFilter,
      requestedSalaryFilter,
      updateDateFilter,
      workingHourFilter,
      workLevelFilter
    )
}
