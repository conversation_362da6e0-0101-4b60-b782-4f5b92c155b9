package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.requests.searches.queries.Query
import com.sksamuel.elastic4s.requests.searches.term.TermsQuery
import nl.dpes.profilesearch.domain.filter.*

object RequestedSalaryFilterMapper extends FilterToQueryMapper {
  private def toQuery(filter: RequestedSalaryFilter): Query =
    TermsQuery("requestedSalary", filter.salaries.map(_.value).toList)

  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = { case filter: RequestedSalaryFilter =>
    Seq(toQuery(filter))
  }
}
