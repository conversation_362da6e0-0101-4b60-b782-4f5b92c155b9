package nl.dpes.profilesearch
package database

import cats.effect.*
import doobie.ExecutionContexts
import doobie.hikari.HikariTransactor
import nl.dpes.profilesearch.database.config.*

object Database {
  def resource[F[_]: Async](config: DbConfig): Resource[F, HikariTransactor[F]] =
    for {
      ec <- ExecutionContexts.fixedThreadPool(config.threadCount.value)
      xa <- HikariTransactor.newHikariTransactor[F](
        "com.mysql.cj.jdbc.Driver",
        config.url.value,
        config.username.value,
        config.password.value,
        ec
      )
    } yield xa
}
