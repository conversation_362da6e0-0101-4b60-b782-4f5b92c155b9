package nl.dpes.profilesearch.service.model
package dbmodel

import nl.dpes.profilesearch.service.model.profilesearch

opaque type UpdatedDate = Long
object UpdatedDate {
  def apply(updatedDate: Long): UpdatedDate = updatedDate
  extension (updatedDate: UpdatedDate) def value: Long = updatedDate

  given io.circe.Decoder[UpdatedDate] = io.circe.Decoder.decodeLong

  given io.scalaland.chimney.Transformer[UpdatedDate, profilesearch.UpdatedDate] = (date: UpdatedDate) =>
    profilesearch.UpdatedDate(date.value)
}
