package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type TrainingDescription = String
object TrainingDescription {
  extension (description: TrainingDescription) def value: String = description
  given Decoder[TrainingDescription] = Decoder.decodeString
  given Transformer[TrainingDescription, detailpage.TrainingDescription] =
    (value: TrainingDescription) => detailpage.TrainingDescription(value)
}
