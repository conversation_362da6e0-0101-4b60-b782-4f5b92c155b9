package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import nl.dpes.profilesearch.service.model.dbmodel.AggregationResult.Aggregations
import nl.dpes.profilesearch.service.model.dbmodel.FacetsResult.TermFacets

case class AggregationResult(aggregations: Aggregations)

object AggregationResult {
  case class Aggregations(
    availabilities: CommonAggregation,
    careerLevels: CommonAggregation,
    driverLicenses: CommonAggregation,
    functionGroups: CommonAggregation,
    languages: CommonAggregation,
    provinces: CommonAggregation,
    requestedSalaries: CommonAggregation,
    updatedDate: CommonAggregation,
    workingHours: CommonAggregation,
    workLevels: CommonAggregation,
    commuteDistance: Option[CommonAggregation]
  )

  case class CommonAggregation(
    aggregation: TermFacets
  )

  given Decoder[CommonAggregation] = io.circe.generic.semiauto.deriveDecoder
  given Decoder[Aggregations] = io.circe.generic.semiauto.deriveDecoder
  given Decoder[AggregationResult] = io.circe.generic.semiauto.deriveDecoder
}
