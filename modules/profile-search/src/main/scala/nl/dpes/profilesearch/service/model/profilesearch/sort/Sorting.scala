package nl.dpes.profilesearch.service.model.profilesearch.sort

import com.sksamuel.elastic4s.requests.searches.sort.{FieldSort, ScoreSort, Sort}

case class Sorting(field: SortField, order: SortOrder) {
  def toElasticSort: List[Sort] = field match {
    case SortField.relevance => List(ScoreSort(order.elasticOrder), FieldSort(field = "updatedDate").order(order.elasticOrder))
    case _                   => List(FieldSort(field = field.name).order(order.elasticOrder))
  }
}

object Sorting {
  private val defaultSorting = new Sorting(SortField.relevance, SortOrder.desc)

  private val defaultSortings: Map[SortField, SortOrder] = Map(
    SortField.relevance   -> SortOrder.desc,
    SortField.updatedDate -> SortOrder.desc
  )

  def apply(field: Option[SortField], order: Option[SortOrder]): Sorting = {
    val fieldValue = field.getOrElse(defaultSorting.field)
    val orderValue = order.getOrElse(defaultSortings(fieldValue))
    new Sorting(fieldValue, orderValue)
  }
}
