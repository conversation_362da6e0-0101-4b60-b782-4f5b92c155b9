package nl.dpes.profilesearch.service.model.detailpage

case class Profile(
  site: SiteName,
  name: Option[Name],
  updatedDate: UpdatedDate,
  photo: Option[PhotoPath],
  introductionText: Option[Introduction],
  availability: Option[Availability],
  requestedSalary: Option[RequestedSalary],
  educationLevels: List[WorkLevel],
  preferredJobs: List[PreferredJob],
  functionGroups: List[FunctionGroup],
  emailAddress: EmailAddress,
  phoneNumber: Option[PhoneNumber],
  commute: Option[Commute],
  workingHours: Option[WorkingHours],
  experiences: List[Experience],
  educations: List[Education],
  training: List[Training],
  driversLicenses: List[DriversLicense],
  attachments: List[Attachment],
  isFavorite: Boolean = false
)
