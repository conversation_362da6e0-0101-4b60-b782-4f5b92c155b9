package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type EmailAddress = String
object EmailAddress {
  def apply(email: String): EmailAddress = email
  extension (emailAddress: EmailAddress) def value: String = emailAddress

  given Decoder[EmailAddress] = Decoder.decodeString

  given Transformer[EmailAddress, detailpage.EmailAddress] = (value: EmailAddress) => detailpage.EmailAddress(value)
}
