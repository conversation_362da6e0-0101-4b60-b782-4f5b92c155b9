package nl.dpes.profilesearch.service.unlockedprofiles

import doobie.Meta
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch

opaque type RecruiterId = String

object RecruiterId {
  def apply(value: String): RecruiterId = value

  given Meta[RecruiterId] = Meta.StringMeta

  given Transformer[profilesearch.RecruiterId, RecruiterId] = (id: profilesearch.RecruiterId) => RecruiterId(id.value)
}
