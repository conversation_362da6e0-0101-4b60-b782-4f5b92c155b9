package nl.dpes.profilesearch.mapper

import cats.Monad
import cats.data.NonEmptyList
import cats.effect.Clock
import com.sksamuel.elastic4s.requests.searches.queries.Query
import nl.dpes.profilesearch.domain.filter.*
import nl.dpes.profilesearch.mapper.*
import nl.dpes.profilesearch.mapper.filter.*

class FilterMapper[F[_]: Clock: Monad](mappers: NonEmptyList[FilterToQueryMapper | RealTimeFilterToQueryMapper[F]]) {
  private val mappingFunctions: PartialFunction[Filter, F[Seq[Query]]] =
    mappers.toList
      .map {
        case mapper: FilterToQueryMapper =>
          mapper.mappingFunction.andThen(Monad[F].pure(_))
        case mapper: RealTimeFilterToQueryMapper[F] =>
          mapper.mappingFunction
      }
      .reduce(_ orElse _)

  def mapToQuery(filter: Filter): F[Seq[Query]] =
    mappingFunctions
      .lift(filter)
      .getOrElse(Monad[F].pure(Seq.empty[Query]))
}

object FilterMapper {
  def default[F[_]: {Clock, Monad}]: FilterMapper[F] =
    new FilterMapper(
      NonEmptyList.of(
        FindableQueryMapper,
        AvailabilityFilterMapper,
        CareerLevelFilterMapper,
        CityFilterMapper,
        DriversLicenseFilterMapper,
        GeoDistanceFilterMapper,
        LanguageFilterMapper,
        ProvinceFilterMapper,
        RequestedSalaryFilterMapper,
        SearchTermFilterMapper,
        UpdateDateFilterMapper[F],
        FunctionGroupFilterMapper,
        WorkLevelFilterMapper,
        WorkingHourFilterMapper
      )
    )
}
