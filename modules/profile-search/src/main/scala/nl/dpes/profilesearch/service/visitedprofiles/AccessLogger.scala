package nl.dpes.profilesearch
package service
package visitedprofiles

import nl.dpes.profilesearch.creditservice.EntitlementId
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpes.profilesearch.service.unlockedprofiles
import org.typelevel.log4cats.{Logger, LoggerFactory, LoggerName}

trait AccessLogger[F[_]] {
  def profileViewEntitlementUsed(
    recruiterId: profilesearch.RecruiterId,
    profileId: profilesearch.ProfileId,
    entitlementId: EntitlementId
  ): F[Unit]
  def doNotUseEntitlement(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit]
  def allowProfileView(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId, result: ProfileView): F[Unit]
  def profileViewed(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit]
  def profileViewFailed(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId, reason: Throwable): F[Unit]
  def unlockProfile(recruiterId: unlockedprofiles.RecruiterId, profileId: unlockedprofiles.ProfileId): F[Unit]
  def profileIsUnlocked(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit]
  def profileViewStorageFailed(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId, reason: Throwable): F[Unit]
}
object AccessLogger {
  def apply[F[_]: LoggerFactory](using logger: AccessLogger[F]): AccessLogger[F] = logger

  given [F[_]](using LoggerFactory[F]): AccessLogger[F] = new AccessLogger[F] {
    val logger: Logger[F] = LoggerFactory[F].getLogger(using LoggerName("PROFILE ACCESS LOG"))

    override def profileViewEntitlementUsed(
      recruiterId: profilesearch.RecruiterId,
      profileId: profilesearch.ProfileId,
      entitlementId: EntitlementId
    ): F[Unit] =
      logger.info(s"Recruiter '$recruiterId' has used entitlement '$entitlementId' to view profile '${profileId.value}'")

    override def doNotUseEntitlement(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit] =
      logger.info(s"Recruiter '$recruiterId' has NOT used an entitlement to view profile '${profileId.value}'")

    override def allowProfileView(
      recruiterId: profilesearch.RecruiterId,
      profileId: profilesearch.ProfileId,
      result: ProfileView
    ): F[Unit] =
      logger.info(s"Recruiter '$recruiterId' is allowed to view profile '${profileId.value}': $result")

    override def profileViewed(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit] =
      logger.info(s"Recruiter '$recruiterId' has viewed profile '${profileId.value}'")

    override def profileViewFailed(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId, reason: Throwable): F[Unit] =
      logger.error(reason)(s"Recruiter '$recruiterId' failed to view profile '${profileId.value}'")

    override def unlockProfile(recruiterId: unlockedprofiles.RecruiterId, profileId: unlockedprofiles.ProfileId): F[Unit] =
      logger.info(s"Recruiter '$recruiterId' has unlocked a profile '${profileId.value}'")

    override def profileIsUnlocked(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit] =
      logger.info(s"Recruiter '$recruiterId' has an unlocked profile '${profileId.value}'")

    override def profileViewStorageFailed(
      recruiterId: profilesearch.RecruiterId,
      profileId: profilesearch.ProfileId,
      reason: Throwable
    ): F[Unit] = logger.error(reason)(s"Unable to store profile view '${profileId.value}' for recruiter '$recruiterId'.")
  }
}
