package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type FieldOfStudy = String
object FieldOfStudy {
  extension (field: FieldOfStudy) def value: String = field

  given Decoder[FieldOfStudy] = Decoder.decodeString

  given Transformer[FieldOfStudy, detailpage.FieldOfStudy] = (value: FieldOfStudy) => detailpage.FieldOfStudy(value)
}
