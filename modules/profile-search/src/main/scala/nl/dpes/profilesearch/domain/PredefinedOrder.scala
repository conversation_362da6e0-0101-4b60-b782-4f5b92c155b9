package nl.dpes.profilesearch.domain

object PredefinedOrder:
  private def getPredefinedIndex[A](orderedValues: Seq[A])(value: A): Int =
    orderedValues.map(_.toLowerCase).indexOf(value.toLowerCase) match
      case -1    => orderedValues.length // If not found, return length to place it at the end
      case index => index

  extension [A](value: A) def toLowerCase: String = value.toString.toLowerCase

  // Create an Ordering that uses the predefined order of values.
  // Unknown values will be placed at the end of the list, ordered alphabetically.
  def apply[A](orderedValues: Seq[A]): Ordering[A] =
    Ordering[Int].on(getPredefinedIndex(orderedValues)).orElseBy(_.toLowerCase)
