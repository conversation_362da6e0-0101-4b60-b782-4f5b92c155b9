package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type EducationDescription = String
object EducationDescription {
  extension (description: EducationDescription) def value: String = description

  given Decoder[EducationDescription] = Decoder.decodeString

  given Transformer[EducationDescription, detailpage.EducationDescription] =
    (value: EducationDescription) => detailpage.EducationDescription(value)
}
