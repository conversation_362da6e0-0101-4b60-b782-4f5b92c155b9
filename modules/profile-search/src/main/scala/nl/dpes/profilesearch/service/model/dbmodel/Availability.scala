package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.{detailpage, profilesearch}

opaque type Availability = String
object Availability {
  extension (availability: Availability) {
    def value: String = availability
  }
  given Decoder[Availability] = Decoder.decodeString
  given Transformer[Availability, detailpage.Availability] =
    (value: Availability) => detailpage.Availability(value)
  given searchTransformer: Transformer[Availability, profilesearch.Availability] =
    (value: Availability) => profilesearch.Availability(value)
}
