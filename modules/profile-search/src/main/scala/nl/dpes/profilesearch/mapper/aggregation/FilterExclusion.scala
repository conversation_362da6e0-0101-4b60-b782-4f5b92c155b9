package nl.dpes.profilesearch.mapper.aggregation

import nl.dpes.profilesearch.domain.Aggregation
import nl.dpes.profilesearch.domain.filter.*

import scala.reflect.ClassTag

trait FilterExclusion {
  def getFiltersForAggregation(aggregation: Aggregation, filters: Seq[Filter]): Seq[Filter]
}

object FilterExclusion {
  val default: FilterExclusion = new FilterExclusion {
    override def getFiltersForAggregation(aggregation: Aggregation, filters: Seq[Filter]): Seq[Filter] = aggregation match {
      case Aggregation.Availability    => filterNot[AvailabilityFilter](filters)
      case Aggregation.CareerLevel     => filterNot[CareerLevelFilter](filters)
      case Aggregation.DriverLicense   => filterNot[DriversLicenseFilter](filters)
      case Aggregation.FunctionGroup   => filterNot[FunctionGroupFilter](filters)
      case Aggregation.CommuteDistance => modify[GeoDistanceFilter](filters)(_.asInstanceOf[GeoDistanceFilter].copy(maxDistance = None))
      case Aggregation.Language        => filterNot[LanguageFilter](filters)
      case Aggregation.Province        => filterNot[ProvinceFilter](filters)
      case Aggregation.RequestedSalary => filterNot[RequestedSalaryFilter](filters)
      case Aggregation.UpdatedDate     => filterNot[UpdateDateFilter](filters)
      case Aggregation.WorkingHours    => filterNot[WorkingHourFilter](filters)
      case Aggregation.WorkLevel       => filterNot[WorkLevelFilter](filters)
    }

    private def filterNot[A: ClassTag](filters: Seq[Filter]): Seq[Filter] =
      filters.filterNot(filter => implicitly[ClassTag[A]].runtimeClass.isInstance(filter))

    private def modify[A <: Filter: ClassTag](filters: Seq[Filter])(update: Filter => Filter): Seq[Filter] =
      filters.find(filter => implicitly[ClassTag[A]].runtimeClass.isInstance(filter)).map(update).toList ++
        filters.filterNot(filter => implicitly[ClassTag[A]].runtimeClass.isInstance(filter))
  }
}
