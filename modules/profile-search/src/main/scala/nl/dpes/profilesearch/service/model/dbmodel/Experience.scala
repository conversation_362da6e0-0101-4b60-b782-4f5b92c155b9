package nl.dpes.profilesearch.service.model
package dbmodel

import io.circe.Decoder
import io.scalaland.chimney.partial.Result
import io.scalaland.chimney.{PartialTransformer, Transformer}
import nl.dpes.profilesearch.service.model.{detailpage, profilesearch}

case class Experience(
  fromDate: Option[MonthOfYear],
  toDate: Option[MonthOfYear],
  companyName: Option[CompanyName],
  jobTitle: Option[JobTitle],
  description: Option[JobDescription]
) derives Decoder {
  def period: Period[MonthOfYear] = Period(fromDate, toDate)
}
object Experience {
  given Ordering[Experience] = Ordering.by((e: Experience) => e.period).reverse

  given searchTransformer: PartialTransformer[Experience, profilesearch.Experience] = PartialTransformer
    .define[Experience, profilesearch.Experience]
    .withConstructorPartial {
      (
        fromDate: Option[MonthOfYear],
        toDate: Option[MonthOfYear],
        companyName: Option[CompanyName],
        jobTitle: Option[JobTitle],
        description: Option[JobDescription]
      ) =>
        Result.fromOption(jobTitle).map(_.value)
    }
    .buildTransformer

  given detailTransformer: Transformer[Experience, detailpage.Experience] = Transformer
    .define[Experience, detailpage.Experience]
    .withFieldComputed(_.fromDate, e => e.fromDate.map(MonthOfYear.fromDateTransformer.transform))
    .withFieldComputed(_.toDate, e => e.toDate.map(MonthOfYear.toDateTransformer.transform))
    .buildTransformer
}
