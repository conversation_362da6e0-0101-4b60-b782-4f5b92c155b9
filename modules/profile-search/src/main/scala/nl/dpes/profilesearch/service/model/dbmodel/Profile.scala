package nl.dpes.profilesearch
package service.model
package dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import io.scalaland.chimney.partial.Result
import io.scalaland.chimney.dsl.*
import Attachment.given
import nl.dpes.profilesearch.service.model.{detailpage, profilesearch}

case class Profile(
  id: ProfileId,
  site: SiteName,
  firstName: Option[FirstName],
  lastName: Option[LastName],
  photo: Option[PhotoPath],
  introductionText: Option[Introduction],
  availability: Option[Availability],
  requestedSalary: Option[RequestedSalary],
  workLevels: Option[List[WorkLevel]],
  preferredJobs: Option[List[PreferredJob]],
  functionGroups: Option[List[FunctionGroup]],
  emailAddress: EmailAddress,
  phoneNumber: Option[PhoneNumber],
  commute: Option[Commute],
  minWorkingHours: Option[MinWorkingHours],
  maxWorkingHours: Option[MaxWorkingHours],
  experiences: Option[List[Experience]],
  education: Option[List[Education]],
  training: Option[List[Training]],
  driverLicenses: Option[List[DriversLicense]],
  attachments: Option[List[Attachment]],
  updatedDate: UpdatedDate
) derives Decoder

object Profile {
  // Elasticsearch returns null for empty lists, so we need to wrap the input into an option.
  // We want to treat the None case as an Empty list which will reduce complexity on the client side.
  given optionalListTransformer[A, B](using Transformer[A, B]): Transformer[Option[List[A]], List[B]] =
    _.getOrElse(List()).map(_.transformInto[B])

  given Transformer[Profile, detailpage.Profile] = Transformer
    .define[Profile, detailpage.Profile]
    .withFieldComputed(
      _.name,
      p =>
        (
          p.firstName.filterNot(name => name.value.isEmpty).map((name: FirstName) => detailpage.FirstName(name.value)),
          p.lastName.filterNot(name => name.value.isEmpty).map((name: LastName) => detailpage.LastName(name.value))
        )
          .mapN(detailpage.Name.apply)
    )
    .withFieldComputed(_.updatedDate, profile => detailpage.UpdatedDate(profile.updatedDate.value))
    .withFieldRenamed(_.workLevels, _.educationLevels)
    .withFieldComputed(
      _.workingHours,
      p => (p.minWorkingHours, p.maxWorkingHours).mapN(WorkingHours.apply).into[Option[detailpage.WorkingHours]].transform
    )
    .withFieldComputed(
      _.experiences,
      _.experiences.getOrElse(List()).sorted.map(_.transformInto[detailpage.Experience](using Experience.detailTransformer))
    )
    .withFieldComputed(
      _.educations,
      _.education.map(_.sorted).transformInto[List[detailpage.Education]]
    )
    .withFieldComputed(
      _.commute,
      _.commute.flatMap(c =>
        (
          c.city.map(city => city.transformInto[detailpage.City](using City.detailTransformer)),
          c.maxTravelDistance.map(distance =>
            distance.transformInto[detailpage.MaxTravelDistance](using MaxTravelDistance.detailTransformer)
          )
        )
          .mapN(detailpage.Commute.apply)
      )
    )
    .withFieldComputed(_.training, _.training.getOrElse(List()).sorted.map(_.into[detailpage.Training].transform))
    .withFieldRenamed(_.driverLicenses, _.driversLicenses)
    .withFieldComputed(_.attachments, _.attachments.getOrElse(List()).sorted.map(_.transformInto[detailpage.Attachment]))
    .withFieldConst(_.isFavorite, false)
    .buildTransformer

  given searchTransFormer: Transformer[Profile, profilesearch.ProfileSearchResult] = Transformer
    .define[Profile, profilesearch.ProfileSearchResult]
    .withFieldComputed(
      _.name,
      p =>
        (
          p.firstName.filterNot(name => name.value.isEmpty).map(name => profilesearch.FirstName(name.value)),
          p.lastName.filterNot(name => name.value.isEmpty).map(name => profilesearch.LastName(name.value))
        )
          .mapN(profilesearch.Name.apply)
    )
    .withFieldComputed(
      _.workingHours,
      p => (p.minWorkingHours.map(_.value), p.maxWorkingHours.map(_.value)).mapN(profilesearch.WorkingHours.apply)
    )
    .withFieldComputed(
      _.workLevels,
      _.workLevels.getOrElse(List()).map(_.transformInto[profilesearch.WorkLevel](using WorkLevel.searchTransformer))
    )
    .withFieldComputed(
      _.availability,
      _.availability.map(_.transformInto[profilesearch.Availability](using Availability.searchTransformer))
    )
    .withFieldComputed(
      _.experiences,
      _.experiences
        .getOrElse(List())
        .sorted
        .take(3)
        .map(
          _.transformIntoPartial[profilesearch.Experience](using Experience.searchTransformer)
        )
        .collect { case Result.Value(exp) => exp }
    )
    .withFieldComputed(
      _.city,
      _.commute.flatMap(_.city.map(_.transformInto[profilesearch.City](using City.searchTransformer)))
    )
    .withFieldConst(_.lastViewedDate, None)
    .buildTransformer
}
