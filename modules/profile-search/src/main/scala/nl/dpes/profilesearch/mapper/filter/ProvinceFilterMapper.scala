package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.requests.searches.queries.Query
import com.sksamuel.elastic4s.requests.searches.term.TermsQuery
import nl.dpes.profilesearch.domain.filter.{Filter, ProvinceFilter}

object ProvinceFilterMapper extends FilterToQueryMapper {
  private def toQuery(filter: ProvinceFilter): Query = TermsQuery("commute.province", filter.provinces.toList)

  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = { case filter: ProvinceFilter =>
    Seq(toQuery(filter))
  }
}
