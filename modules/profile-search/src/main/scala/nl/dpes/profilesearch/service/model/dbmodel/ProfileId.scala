package nl.dpes.profilesearch
package service
package model
package dbmodel

import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch
import sttp.tapir.Schema

opaque type ProfileId = String
object ProfileId {
  def apply(id: String): ProfileId = id

  extension (profileId: ProfileId) {
    def value: String = profileId
  }

  given Encoder[ProfileId] = Encoder.encodeString
  given Decoder[ProfileId] = Decoder.decodeString
  given Schema[ProfileId] = Schema.schemaForString

  given Transformer[ProfileId, profilesearch.ProfileId] =
    (id: ProfileId) => profilesearch.ProfileId.unsafe(id.value)
}
