package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.requests.searches.queries.Query
import com.sksamuel.elastic4s.requests.searches.term.TermsQuery
import nl.dpes.profilesearch.domain.filter.{DriversLicenseFilter, Filter}

object DriversLicenseFilterMapper extends FilterToQueryMapper {
  private def toQuery(filter: DriversLicenseFilter): Query =
    TermsQuery("driverLicenses.group", filter.driversLicenses.toList)

  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = { case filter: DriversLicenseFilter =>
    Seq(toQuery(filter))
  }
}
