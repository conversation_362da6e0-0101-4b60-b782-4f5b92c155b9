package nl.dpes.profilesearch.creditservice

import CreditServiceConfig.*
import pureconfig.ConfigReader
import pureconfig.generic.derivation.default.*

case class CreditServiceConfig(host: Host, port: Port) derives ConfigReader
object CreditServiceConfig {
  opaque type Host = String
  object Host {
    def apply(value: String): Host = value
    given ConfigReader[Host] = ConfigReader.stringConfigReader
  }

  opaque type Port = Int
  object Port {
    def apply(value: Int): Port = value
    given ConfigReader[Port] = ConfigReader.intConfigReader
  }
}
