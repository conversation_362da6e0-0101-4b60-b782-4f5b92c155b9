package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type RequestedSalary = String
object RequestedSalary {
  extension (requestedSalary: RequestedSalary) def value: String = requestedSalary

  given Decoder[RequestedSalary] = Decoder.decodeString

  given Transformer[RequestedSalary, detailpage.RequestedSalary] = (value: RequestedSalary) => detailpage.RequestedSalary(value)
}
