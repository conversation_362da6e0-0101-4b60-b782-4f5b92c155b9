package nl.dpes.profilesearch.service.model.profilesearch

import nl.dpes.profilesearch.service.model.dbmodel.FacetsResult.TermBucket
import nl.dpes.profilesearch.service.model.profilesearch.ProfileSearchResult

case class SearchResult(
  profiles: List[ProfileSearchResult],
  // todo: Use domain type for aggregations
  aggregations: Map[AggregationKeyName, List[TermBucket]],
  totalNumberOfProfiles: Long
)
