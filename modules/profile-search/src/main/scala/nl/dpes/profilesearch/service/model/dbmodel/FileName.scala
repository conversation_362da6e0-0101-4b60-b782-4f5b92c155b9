package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type FileName = String
object FileName {
  extension (name: FileName) def value: String = name

  given Decoder[FileName] = Decoder.decodeString

  given Transformer[FileName, detailpage.FileName] = (value: FileName) => detailpage.FileName(value)
}
