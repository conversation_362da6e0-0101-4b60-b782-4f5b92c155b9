package nl.dpes.profilesearch.service.viewedprofiles

import doobie.Meta
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch

opaque type ProfileId = String

object ProfileId {
  given Meta[ProfileId] = Meta[String].imap(apply)(_.value)
  given Transformer[profilesearch.ProfileId, ProfileId] = (profileId: profilesearch.ProfileId) => profileId.value
  def apply(id: String): ProfileId = id
  extension (id: ProfileId) def value: String = id
}
