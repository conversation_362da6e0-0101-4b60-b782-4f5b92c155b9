package nl.dpes.profilesearch.service.model
package dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.{detailpage, profilesearch}

opaque type PhotoPath = String
object PhotoPath {
  def apply(path: String): PhotoPath = path
  extension (photoPath: PhotoPath) def value: String = photoPath

  given Decoder[PhotoPath] = Decoder.decodeString

  given Transformer[PhotoPath, detailpage.PhotoPath] = (value: PhotoPath) => detailpage.PhotoPath(value)
  given Transformer[PhotoPath, profilesearch.Photo] = (path: PhotoPath) => profilesearch.Photo(path.value)
}
