package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder

case class Training(
  year: Option[Year],
  month: Option[Month],
  name: Option[TrainingName],
  institute: Option[Institute],
  description: Option[TrainingDescription]
) derives Decoder
object Training {
  given Ordering[Training] = Ordering.by(training => (training.year.getOrElse(Year.min), training.month.getOrElse(Month.min)))
}
