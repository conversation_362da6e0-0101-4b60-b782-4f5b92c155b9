package nl.dpes.profilesearch
package service.unlockedprofiles

import doobie.Meta
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch

opaque type ProfileId = String

object ProfileId {
  def apply(value: String): Either[String, ProfileId] =
    if (value == null || value == "") "Profile id cannot be empty".asLeft
    else value.asRight

  extension (id: ProfileId) def value: String = id

  given Meta[ProfileId] = Meta.StringMeta.tiemap(ProfileId(_))(_.value)

  given Transformer[profilesearch.ProfileId, ProfileId] = (id: profilesearch.ProfileId) => id.value
}
