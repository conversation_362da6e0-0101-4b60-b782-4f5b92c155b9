package nl.dpes.profilesearch.service.model.profilesearch.pagination

case class PageNumber private (value: Int) extends AnyVal

object PageNumber {
  val firstPage = new PageNumber(1)

  def apply(value: Int): Either[String, PageNumber] =
    if (value < 1) Left(s"Page number ($value) must be positive")
    else Right(new PageNumber(value))

  def unsafe(value: Int): PageNumber = new PageNumber(value)
}
