package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type PhoneNumber = String
object PhoneNumber {
  def apply(phoneNumber: String): PhoneNumber = phoneNumber
  extension (phoneNumber: PhoneNumber) def value: String = phoneNumber

  given Decoder[PhoneNumber] = Decoder.decodeString

  given Transformer[PhoneNumber, detailpage.PhoneNumber] = (value: PhoneNumber) => detailpage.PhoneNumber(value)
}
