package nl.dpes.profilesearch.domain

import nl.dpes.profilesearch.service.model.profilesearch.{EmailAddress, ProfileId}

import scala.util.control.NoStackTrace

sealed class Error(message: String) extends Throwable(message)

object Error {
  case class SearchError(message: String) extends Error(message)
  case class GetProfileFailure(profileId: ProfileId, message: String) extends Error(s"Failed to load profile $profileId: $message")
  case class ProfileNotFound(profileId: ProfileId) extends Error(s"Failed to load profile $profileId: Not Found") with NoStackTrace
}
