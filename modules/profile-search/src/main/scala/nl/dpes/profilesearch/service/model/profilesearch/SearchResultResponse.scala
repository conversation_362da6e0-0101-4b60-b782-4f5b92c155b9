package nl.dpes.profilesearch
package service.model.profilesearch

import nl.dpes.profilesearch.service.model.profilesearch.SearchResult

case class SearchResultResponse(
  profiles: List[ProfileSearchResult],
  aggregations: Map[String, List[AggregationBucket]],
  totalNumberOfProfiles: Long = 0
)

object SearchResultResponse {
  def fromSearchResult(
    searchResult: SearchResult
  ): SearchResultResponse =
    SearchResultResponse(
      profiles = searchResult.profiles,
      aggregations = searchResult.aggregations.map { case (k, v) => k.value -> v.map(b => AggregationBucket(b.`key`, b.`doc_count`)) },
      totalNumberOfProfiles = searchResult.totalNumberOfProfiles
    )
}
