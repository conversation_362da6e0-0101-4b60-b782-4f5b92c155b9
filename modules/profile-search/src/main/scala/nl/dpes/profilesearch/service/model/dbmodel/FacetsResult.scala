package nl.dpes.profilesearch.service.model.dbmodel

import cats.Applicative
import cats.implicits.*
import io.circe.*
import io.circe.generic.semiauto.*
import nl.dpes.profilesearch.service.model.dbmodel.FacetsResult.TermFacets
import sttp.tapir.Schema

case class FacetsResult(
  `province_facets`: Option[TermFacets],
  `updatedDates_facets`: Option[TermFacets],
  `functionGroups_facets`: Option[TermFacets],
  `workLevels_facets`: Option[TermFacets],
  `workingHours_facets`: Option[TermFacets],
  `careerLevels_facets`: Option[TermFacets],
  `requestedSalaries_facets`: Option[TermFacets],
  `availabilities_facets`: Option[TermFacets],
  `driverLicenses_facets`: Option[TermFacets],
  `languages_facets`: Option[TermFacets]
)

object FacetsResult {

  case class LongBucket(`key`: Long, `doc_count`: Long)
  object LongBucket {
    given Encoder[LongBucket] = deriveEncoder
    given Decoder[LongBucket] = deriveDecoder
    given Schema[LongBucket] = Schema.derived[LongBucket]
  }

  case class TermBucket(`key`: String, `doc_count`: Long)
  object TermBucket {
    given Encoder[TermBucket] = deriveEncoder
    given Decoder[TermBucket] = deriveDecoder
  }

  case class TermFacets(`buckets`: List[TermBucket])
  object TermFacets {
    given Encoder[TermFacets] = deriveEncoder
    given Decoder[TermFacets] = deriveDecoder
  }
  case class LongFacets(`buckets`: List[LongBucket])

  given Decoder[FacetsResult] = deriveDecoder

  extension [F[_]: Applicative](facets: Option[TermFacets]) {
    def sortBuckets(orderingMap: Map[String, Int]): F[List[TermBucket]] = {
      val buckets = facets.map(_.`buckets`).getOrElse(List.empty)
      buckets
        .sortBy(bucket => orderingMap.getOrElse(bucket.key, buckets.size))
        .pure[F]
    }
  }

  extension (buckets: List[TermBucket]) {
    def sortBuckets(orderingMap: Map[String, Int]): List[TermBucket] =
      buckets
        .sortBy(bucket => orderingMap.getOrElse(bucket.key, buckets.size))
  }
}
