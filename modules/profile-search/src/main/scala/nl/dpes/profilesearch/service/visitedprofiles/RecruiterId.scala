package nl.dpes.profilesearch.service.visitedprofiles

import doobie.Meta
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch

opaque type RecruiterId = String

object RecruiterId {
  def apply(value: String): RecruiterId = value
  given Meta[RecruiterId] = Meta.StringMeta

  given toSearchTransformer: Transformer[RecruiterId, profilesearch.RecruiterId] = (recruiterId: RecruiterId) =>
    profilesearch.RecruiterId(recruiterId)

  given fromSearchTransformer: Transformer[profilesearch.RecruiterId, RecruiterId] = (recruiterId: profilesearch.RecruiterId) =>
    recruiterId.value
}
