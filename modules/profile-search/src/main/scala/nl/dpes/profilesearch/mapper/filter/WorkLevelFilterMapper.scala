package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.requests.searches.queries.Query
import com.sksamuel.elastic4s.requests.searches.term.TermsQuery
import nl.dpes.profilesearch.domain.filter.{Filter, WorkLevelFilter}

object WorkLevelFilterMapper extends FilterToQueryMapper {

  private def toQuery(filter: WorkLevelFilter): Query = TermsQuery("workLevels", filter.workLevels.toList.map(_.value))

  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = { case filter: WorkLevelFilter =>
    Seq(toQuery(filter))
  }
}
