package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.ElasticApi.termsQuery
import com.sksamuel.elastic4s.requests.searches.queries.Query
import nl.dpes.profilesearch.domain.filter.{Filter, FindableFilter}

object FindableQueryMapper extends FilterToQueryMapper {
  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = { case FindableFilter =>
    Seq(termsQuery("findable", true))
  }
}
