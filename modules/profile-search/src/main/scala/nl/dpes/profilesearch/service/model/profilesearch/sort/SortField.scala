package nl.dpes.profilesearch
package service.model.profilesearch.sort

import cats.ApplicativeThrow
import scala.util.control.NoStackTrace

opaque type SortField = String

object SortField {
  val relevance: SortField = "relevance"
  val updatedDate: SortField = "updatedDate"

  private val allowedFields: List[SortField] = List(relevance, updatedDate)
  case class InvalidSortField(sortField: String) extends Throwable(s"Invalid sort field: '$sortField''") with NoStackTrace

  def apply[F[_]: ApplicativeThrow](sortField: String): F[SortField] =
    if (allowedFields.contains(sortField)) sortField.pure
    else InvalidSortField(sortField).raiseError

  extension (sortField: SortField) def name: String = sortField

  def unsafe(sortField: String): SortField = sortField
}
