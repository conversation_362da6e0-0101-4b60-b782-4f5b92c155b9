package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type CompanyName = String
object CompanyName {
  extension (jobTitle: CompanyName) def value: String = jobTitle

  given Decoder[CompanyName] = Decoder.decodeString

  given Transformer[CompanyName, detailpage.CompanyName] = (value: CompanyName) => detailpage.CompanyName(value)
}
