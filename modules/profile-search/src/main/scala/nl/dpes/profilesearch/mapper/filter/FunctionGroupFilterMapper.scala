package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.requests.searches.queries.Query
import com.sksamuel.elastic4s.requests.searches.term.TermsQuery
import nl.dpes.profilesearch.domain.filter.{Filter, FunctionGroupFilter}

object FunctionGroupFilterMapper extends FilterToQueryMapper {

  private def toQuery(filter: FunctionGroupFilter): Query = TermsQuery("functionGroups", filter.functionGroups.toList)

  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = { case filter: FunctionGroupFilter =>
    Seq(toQuery(filter))
  }
}
