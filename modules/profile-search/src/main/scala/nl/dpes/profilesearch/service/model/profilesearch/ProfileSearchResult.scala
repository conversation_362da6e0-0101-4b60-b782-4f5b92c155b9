package nl.dpes.profilesearch.service.model.profilesearch

case class ProfileSearchResult(
  id: ProfileId,
  name: Option[Name],
  updatedDate: UpdatedDate,
  workingHours: Option[WorkingHours],
  workLevels: List[WorkLevel],
  availability: Option[Availability],
  experiences: List[Experience],
  preferredJobs: List[PreferredJob],
  photo: Option[Photo],
  city: Option[City],
  lastViewedDate: Option[LastViewedDate]
) {

  def withLastViewedDate(lastViewedDate: Option[LastViewedDate]): ProfileSearchResult =
    this.copy(lastViewedDate = lastViewedDate)
}

object ProfileSearchResult {
  extension (results: List[ProfileSearchResult]) {
    def sortByIds(ids: List[ProfileId]): List[ProfileSearchResult] =
      ProfileSearchResult.sortResultsByIds(results, ids)
  }

  private def sortResultsByIds(results: List[ProfileSearchResult], ids: List[ProfileId]): List[ProfileSearchResult] =
    ids.flatMap(id => results.find(_.id == id))
}
