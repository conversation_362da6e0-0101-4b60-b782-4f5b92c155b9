package nl.dpes.profilesearch.domain

enum RequestedSalary(val value: String) {
  case UpTo1750 extends RequestedSalary("< 1.750")
  case From1750To2500 extends RequestedSalary("1.750 - 2.500")
  case From2500To3500 extends RequestedSalary("2.500 - 3.500")
  case From3500To5000 extends RequestedSalary("3.500 - 5.000")
  case From5000To7000 extends RequestedSalary("5.000 - 7.000")
  case MoreThan7000 extends RequestedSalary("> 7.000")

  override def toString: String = value
}

object RequestedSalary {
  def fromString(value: String): Option[RequestedSalary] = RequestedSalary.values.find(_.value.toLowerCase == value.toLowerCase)

  given Ordering[RequestedSalary] = PredefinedOrder(RequestedSalary.values)
}
