package nl.dpes.profilesearch
package service.model
package dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

import scala.math.Ordered.orderingToOrdered

case class MonthOfYear(month: Option[Month], year: Year)
object MonthOfYear {
  val min: MonthOfYear = MonthOfYear(Month.min.some, Year.min)
  val max: MonthOfYear = MonthOfYear(Month.max.some, Year.max)

  given BoundedOrdering[MonthOfYear] with
    def compare(x: MonthOfYear, y: MonthOfYear): Int = (x.year, x.month).compare((y.year, y.month))
    def min: MonthOfYear = MonthOfYear.min
    def max: MonthOfYear = MonthOfYear.max

  private val datePattern = "^([0-9]{4})(-([0-9]{1,2}))?$".r
  given Decoder[MonthOfYear] = Decoder.decodeString.emap {
    case datePattern(year, _, month) => MonthOfYear(Option(month).map(_.toInt).map(Month.apply), Year(year.toInt)).asRight
    case incorrectInput              => s"Cannot parse '$incorrectInput' into a MonthOfYear".asLeft
  }

  def fromDateTransformer: Transformer[MonthOfYear, detailpage.MonthOfYear] =
    Transformer
      .define[MonthOfYear, detailpage.MonthOfYear]
      .withFieldComputed(_.month, m => detailpage.Month(m.month.getOrElse(Month.min).value))
      .buildTransformer

  def toDateTransformer: Transformer[MonthOfYear, detailpage.MonthOfYear] =
    Transformer
      .define[MonthOfYear, detailpage.MonthOfYear]
      .withFieldComputed(_.month, m => detailpage.Month(m.month.getOrElse(Month.max).value))
      .buildTransformer
}
