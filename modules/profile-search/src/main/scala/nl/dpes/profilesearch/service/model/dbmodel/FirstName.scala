package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type FirstName = String
object FirstName {
  def apply(name: String): FirstName = name

  extension (firstName: FirstName) def value: String = firstName

  given Decoder[FirstName] = Decoder.decodeString

  given Transformer[FirstName, detailpage.FirstName] = (name: FirstName) => detailpage.FirstName(name.value)
}
