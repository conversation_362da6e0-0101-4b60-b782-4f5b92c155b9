package nl.dpes.profilesearch.domain

enum CommuteDistance {
  case Distance(value: Int) extends CommuteDistance
  case WholeCountry extends CommuteDistance
  override def toString: String = this match {
    case Distance(value) => s"${value}km"
    case WholeCountry    => "Heel Nederland"
  }
}
object CommuteDistance {
  def fromString(distance: String): CommuteDistance =
    if (distance.equalsIgnoreCase("Heel Nederland")) {
      WholeCountry
    } else {
      distance
        .replace("km", "")
        .toIntOption
        .map(Distance(_))
        .getOrElse(WholeCountry)
    }

  given Ordering[CommuteDistance] = Ordering.by {
    case Distance(value) => value
    case WholeCountry    => Int.MaxValue
  }
}
