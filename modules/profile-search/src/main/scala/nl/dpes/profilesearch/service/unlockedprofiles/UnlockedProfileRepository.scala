package nl.dpes.profilesearch
package service
package unlockedprofiles

import cats.effect.kernel.{MonadCancelThrow, Resource}
import cats.syntax.all.toFunctorOps
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.profilesearch
import doobie.*
import doobie.implicits.*
import doobie.implicits.javatimedrivernative.JavaLocalDateMeta
import doobie.util.*
import doobie.util.transactor.Transactor

import java.time.LocalDate

trait UnlockedProfileRepository[F[_]]:
  def getExpirationDate(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Option[LocalDate]]
  def setExpirationDate(recruiterId: RecruiterId, profileId: ProfileId, expirationDate: LocalDate): F[Unit]

object UnlockedProfileRepository {
  def initResource[F[_]: MonadCancelThrow](tableName: TableName, xa: Transactor[F]): Resource[F, UnlockedProfileRepository[F]] =
    val table = tableName.fragment

    Resource.eval {
      object DbIO {
        def initializeTable: ConnectionIO[Unit] =
          sql"""
            CREATE TABLE IF NOT EXISTS $table (
              recruiterId VARCHAR(36),
              profileId VARCHAR(36),
              expirationDate DATE NOT NULL,
              PRIMARY KEY (recruiterId, profileId)
            );
          """.update.run.void

        def initializeCleanUpFunctionality =
          for {
            _ <-
              sql"""
              CREATE PROCEDURE IF NOT EXISTS CleanUpExpiredUnlockedProfiles()
              BEGIN
                DELETE FROM $table
                WHERE expirationDate < CURRENT_DATE;
              END;
            """.update.run.void
            _ <-
              sql"""
                CREATE EVENT IF NOT EXISTS CleanUpExpiredUnlockedProfilesEvent
                ON SCHEDULE EVERY 1 DAY
                STARTS CURRENT_TIMESTAMP
                ON COMPLETION NOT PRESERVE
                ENABLE
                DO BEGIN
                	DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
                	BEGIN
                	  DO RELEASE_LOCK('cleanup_expired_events_lock');
                	END;
                	IF (GET_LOCK('cleanup_expired_events_lock', 0)) THEN
                      CALL CleanUpExpiredUnlockedProfiles();
                	END IF;
                	DO RELEASE_LOCK('cleanup_expired_events_lock');
                END
              """.update.run.void
          } yield ()
      }

      val repository = new UnlockedProfileRepository[F]:
        override def getExpirationDate(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Option[LocalDate]] =
          sql"""
            SELECT expirationDate FROM $table 
            WHERE recruiterId = ${recruiterId.into[RecruiterId].transform} AND profileId = ${profileId.into[ProfileId].transform}
            FOR UPDATE
          """.query[LocalDate].option.transact(xa)

        override def setExpirationDate(
          recruiterId: RecruiterId,
          profileId: ProfileId,
          expirationDate: LocalDate
        ): F[Unit] =
          sql"""
            INSERT INTO $table (recruiterId, profileId, expirationDate) 
            VALUES (${recruiterId.into[RecruiterId].transform}, ${profileId.into[ProfileId].transform}, $expirationDate)
            ON DUPLICATE KEY UPDATE expirationDate = $expirationDate
          """.update.run.void.transact(xa)

      (for {
        _ <- DbIO.initializeTable
        _ <- DbIO.initializeCleanUpFunctionality
      } yield ())
        .transact(xa)
        .as(repository)
    }

}
