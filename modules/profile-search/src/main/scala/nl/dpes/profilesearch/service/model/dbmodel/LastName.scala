package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type LastName = String
object LastName {
  def apply(lastName: String): LastName = lastName
  extension (lastName: LastName) def value: String = lastName

  given Decoder[LastName] = Decoder.decodeString

  given Transformer[LastName, detailpage.LastName] = (value: LastName) => detailpage.LastName(value)
}
