package nl.dpes.profilesearch.mapper.filter

import cats.Monad
import cats.effect.Clock
import cats.syntax.functor.*
import com.sksamuel.elastic4s.ElasticApi.rangeQuery
import com.sksamuel.elastic4s.requests.searches.queries.Query
import nl.dpes.profilesearch.domain.UpdateDate
import nl.dpes.profilesearch.domain.filter.{Filter, UpdateDateFilter}

import java.time.Instant

class UpdateDateFilterMapper[F[_]: Clock: Monad] extends RealTimeFilterToQueryMapper[F] {

  override val mappingFunction: PartialFunction[Filter, F[Seq[Query]]] = { case filter: UpdateDateFilter =>
    for {
      now <- Clock[F].realTime
      timestamp = UpdateDate.toTimestamp(filter.updateDate, Instant.ofEpochMilli(now.toMillis)).getEpochSecond
      query = rangeQuery("updatedDate").gte(timestamp)
    } yield Seq(query)
  }
}
