package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type Extension = String
object Extension {
  extension (extensionName: Extension) def value: String = extensionName

  given Decoder[Extension] = Decoder.decodeString

  given Transformer[Extension, detailpage.Extension] = (value: Extension) => detailpage.Extension(value)
}
