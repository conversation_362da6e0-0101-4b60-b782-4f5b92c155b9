package nl.dpes.profilesearch
package service
package unlockedprofiles

import cats.Monad
import cats.effect.kernel.Clock
import cats.effect.{Async, Resource}
import io.scalaland.chimney.dsl.into
import nl.dpes.profilesearch.service.visitedprofiles.AccessLogger
import nl.dpes.profilesearch.service.model.profilesearch
import org.typelevel.log4cats.LoggerFactory

import java.time.{Instant, LocalDate, ZoneId}

class UnlockedProfileService[F[_]: Async: LoggerFactory](
  unlockedProfileRepository: UnlockedProfileRepository[F],
  accessDurationInDays: Long
) {
  def profileIsUnlocked(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Boolean] =
    for {
      now            <- getNow
      expirationDate <- unlockedProfileRepository.getExpirationDate(recruiterId, profileId)
      isUnlocked = expirationDate.exists(date => !now.isAfter(date))
      _ <- if (isUnlocked) AccessLogger[F].profileIsUnlocked(recruiterId, profileId) else Monad[F].unit
    } yield isUnlocked

  def unlockProfile(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit] =
    for {
      now <- getNow
      _ <- unlockedProfileRepository.setExpirationDate(
        recruiterId.into[RecruiterId].transform,
        profileId.into[ProfileId].transform,
        now.plusDays(accessDurationInDays)
      )
      _ <- AccessLogger[F].unlockProfile(recruiterId.into[RecruiterId].transform, profileId.into[ProfileId].transform)
    } yield ()

  private def getNow: F[LocalDate] =
    Clock[F].realTime.map(time => LocalDate.ofInstant(Instant.ofEpochMilli(time.toMillis), ZoneId.of("UTC")))
}

object UnlockedProfileService {
  def resource[F[_]: Async: LoggerFactory](
    unlockedProfileRepository: UnlockedProfileRepository[F],
    accessDurationInDays: Long
  ): Resource[F, UnlockedProfileService[F]] = Resource.pure(new UnlockedProfileService(unlockedProfileRepository, accessDurationInDays))
}
