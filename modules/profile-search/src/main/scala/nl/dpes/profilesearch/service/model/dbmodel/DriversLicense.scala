package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

case class DriversLicense(`type`: DriversLicenseType) derives Decoder
object DriversLicense {
  given Transformer[DriversLicense, detailpage.DriversLicense] = (license: DriversLicense) =>
    detailpage.DriversLicense(license.`type`.value)
}
