package nl.dpes.profilesearch
package service
package visitedprofiles

import cats.effect.Resource
import cats.effect.kernel.MonadCancelThrow
import com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException
import io.scalaland.chimney.dsl.*
import doobie.*
import doobie.implicits.*
import doobie.util.*
import doobie.util.transactor.Transactor
import nl.dpes.profilesearch.service.model.profilesearch

trait VisitedProfilesRepository[F[_]] {
  def store(recruiterId: profilesearch.RecruiterId, profileIds: Set[profilesearch.ProfileId]): F[Unit]
  def read(recruiterId: profilesearch.RecruiterId): F[Set[profilesearch.ProfileId]]
  def update(recruiterId: profilesearch.RecruiterId, f: Set[profilesearch.ProfileId] => Set[profilesearch.ProfileId]): F[Unit]
}

object VisitedProfilesRepository {
  def resource[F[_]: MonadCancelThrow](tableName: TableName, xa: Transactor[F]): Resource[F, VisitedProfilesRepository[F]] = Resource.eval {
    object DbIO {
      def initialize: ConnectionIO[Unit] =
        sql"""
          CREATE TABLE IF NOT EXISTS ${tableName.fragment} (
            recruiterId VARCHAR(36) PRIMARY KEY,
            profileIds JSON NOT NULL,
            date DATE NOT NULL
          );
        """.update.run.void

      def upsert(recruiterId: RecruiterId, profileIds: ProfileIds): ConnectionIO[Unit] =
        sql"""
          INSERT INTO ${tableName.fragment} (recruiterId, profileIds, date)
          VALUES ($recruiterId, $profileIds, CURRENT_DATE)
          ON DUPLICATE KEY UPDATE profileIds = $profileIds, date = CURRENT_DATE;
        """.update.run.void

      def select(recruiterId: RecruiterId): ConnectionIO[ProfileIds] =
        sql"""
          SELECT profileIds
          FROM ${tableName.fragment}
          WHERE recruiterId = $recruiterId
          AND date = CURRENT_DATE
          FOR UPDATE;
        """.query[ProfileIds].option.map(_.getOrElse(ProfileIds.empty))
    }

    val initialize: F[Unit] = DbIO.initialize.transact(xa)

    val repository: VisitedProfilesRepository[F] = new VisitedProfilesRepository[F] {
      override def store(recruiterId: profilesearch.RecruiterId, profileIds: Set[profilesearch.ProfileId]): F[Unit] =
        DbIO.upsert(recruiterId.into[RecruiterId].transform, profileIds.into[ProfileIds].transform).transact(xa)

      override def read(recruiterId: profilesearch.RecruiterId): F[Set[profilesearch.ProfileId]] =
        DbIO.select(recruiterId.into[RecruiterId].transform).map(_.into[Set[profilesearch.ProfileId]].transform).transact(xa)

      override def update(
        recruiterId: profilesearch.RecruiterId,
        f: Set[profilesearch.ProfileId] => Set[profilesearch.ProfileId]
      ): F[Unit] = (for {
        profileIds <- DbIO.select(recruiterId.into[RecruiterId].transform)
        _ <- DbIO.upsert(
          recruiterId.into[RecruiterId].transform,
          f(profileIds.into[Set[profilesearch.ProfileId]].transform).into[ProfileIds].transform
        )
      } yield ())
        .transact(xa)
        .recoverWith { case _: MySQLTransactionRollbackException =>
          update(recruiterId, f)
        }
    }

    initialize.as(repository)
  }
}
