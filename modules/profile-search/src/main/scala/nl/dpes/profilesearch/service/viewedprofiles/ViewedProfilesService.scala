package nl.dpes.profilesearch.service.viewedprofiles

import cats.implicits.*
import cats.effect.kernel.Async
import nl.dpes.profilesearch.service.model.profilesearch

import io.scalaland.chimney.dsl.into

trait ViewedProfilesService[F[_]] {
  def save(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit]
  def getLastViewedDate(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Option[profilesearch.LastViewedDate]]
}

object ViewedProfilesService {

  def impl[F[_]: Async](viewedProfilesRepository: ViewedProfilesRepository[F]): ViewedProfilesService[F] = new ViewedProfilesService[F] {

    override def save(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit] =
      viewedProfilesRepository.save(recruiterId, profileId)

    override def getLastViewedDate(
      recruiterId: profilesearch.RecruiterId,
      profileId: profilesearch.ProfileId
    ): F[Option[profilesearch.LastViewedDate]] =
      viewedProfilesRepository
        .getLastViewedDate(recruiterId, profileId)
        .map(_.map(_.into[profilesearch.LastViewedDate].transform))
  }
}
