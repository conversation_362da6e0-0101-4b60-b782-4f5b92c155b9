package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type Year = Int
object Year {
  val min: Year = 1000
  val max: Year = 9999

  def apply(month: Int): Year = month

  given BoundedOrdering[Year] with
    def compare(x: Year, y: Year): Int = x.compare(y)
    def min: Year = Year.min
    def max: Year = Year.max

  given Decoder[Year] = Decoder.decodeInt

  given Transformer[Year, detailpage.Year] = (value: Year) => detailpage.Year(value)
}
