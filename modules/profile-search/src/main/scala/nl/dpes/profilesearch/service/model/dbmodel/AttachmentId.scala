package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type AttachmentId = String
object AttachmentId {
  extension (id: AttachmentId) {
    def value: String = id
  }
  given Decoder[AttachmentId] = Decoder.decodeString
  given Transformer[AttachmentId, detailpage.AttachmentId] =
    (value: AttachmentId) => detailpage.AttachmentId(value)
}
