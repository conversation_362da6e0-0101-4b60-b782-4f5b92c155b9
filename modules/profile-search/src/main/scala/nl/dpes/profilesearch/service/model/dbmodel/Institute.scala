package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type Institute = String
object Institute {
  extension (institute: Institute) def value: String = institute

  given Decoder[Institute] = Decoder.decodeString

  given Transformer[Institute, detailpage.Institute] = (value: Institute) => detailpage.Institute(value)
}
