package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.requests.searches.queries.Query
import com.sksamuel.elastic4s.requests.searches.term.TermsQuery
import nl.dpes.profilesearch.domain.filter.{CityFilter, Filter}

object CityFilterMapper extends FilterToQueryMapper {
  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = { case filter: CityFilter => Seq(toQuery(filter)) }

  private def toQuery(filter: CityFilter): Query =
    TermsQuery("commute.city", List(filter.city.value))
}
