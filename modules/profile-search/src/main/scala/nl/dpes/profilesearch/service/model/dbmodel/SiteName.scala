package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type SiteName = String
object SiteName {
  def apply(name: String): SiteName = name
  extension (site: SiteName) def value: String = site

  given Decoder[SiteName] = Decoder.decodeString

  given Transformer[SiteName, detailpage.SiteName] = (value: SiteName) => detailpage.SiteName(value)
}
