package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type JobTitle = String
object JobTitle {
  def apply(jobTitle: String): JobTitle = jobTitle
  extension (jobTitle: JobTitle) def value: String = jobTitle

  given Decoder[JobTitle] = Decoder.decodeString

  given Transformer[JobTitle, detailpage.JobTitle] = (value: JobTitle) => detailpage.JobTitle(value)
}
