package nl.dpes.profilesearch.domain

enum WorkingHours(val min: Int, max: Int) {
  case UpTo16Hours extends WorkingHours(0, 15)
  case From16To24 extends WorkingHours(16, 23)
  case From24To32 extends WorkingHours(24, 31)
  case From32To40 extends WorkingHours(32, 40)

  override def toString: String = WorkingHours.toEntryName(this)
}

object WorkingHours {
  def fromString(value: String): Option[WorkingHours] =
    WorkingHours.values.find(workingHours => toEntryName(workingHours) == value)

  def toEntryName(workingHours: WorkingHours): String = workingHours match {
    case UpTo16Hours => "Tot 16 uur"
    case From16To24  => "16 tot 24 uur"
    case From24To32  => "24 tot 32 uur"
    case From32To40  => "32 tot en met 40 uur"
  }

  given Ordering[WorkingHours] = Ordering.by {
    case UpTo16Hours => 1
    case From16To24  => 2
    case From24To32  => 3
    case From32To40  => 4
  }
}
