package nl.dpes.profilesearch
package service
package visitedprofiles

import doobie.util.meta.Meta
import io.circe.*
import io.circe.parser.*
import io.circe.syntax.*
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch

import scala.annotation.targetName

opaque type ProfileIds = Set[ProfileId]

object ProfileIds {
  val empty: ProfileIds = Set.empty[ProfileId]
  def apply(profileIds: ProfileId*): ProfileIds = Set(profileIds*)

  extension (profileIds: ProfileIds)
    def reachedLimit(limit: ProfileViewLimit): Boolean = limit <= profileIds.size
    def contains(profileId: ProfileId): Boolean = profileIds.contains(profileId)
    @targetName("addProfileId")
    def +(profileId: ProfileId): ProfileIds = profileIds + profileId

  given Encoder[ProfileIds] = Encoder.encodeSet[ProfileId]
  given Decoder[ProfileIds] = Decoder.decodeSet[ProfileId]

  given Meta[ProfileIds] = Meta[String].tiemap(decode[ProfileIds](_).leftMap(_.getMessage))(_.asJson.noSpaces)

  given Transformer[Set[profilesearch.ProfileId], ProfileIds] = { (profileIds: Set[profilesearch.ProfileId]) =>
    val seqProfileId: Seq[profilesearch.ProfileId] = profileIds.toSeq
    val seqVisitedProfileId: Seq[ProfileId] = seqProfileId.map(_.value).map(ProfileId.unsafe)
    ProfileIds(seqVisitedProfileId*)
  }

  given Transformer[ProfileIds, Set[profilesearch.ProfileId]] =
    (profileIds: ProfileIds) => profileIds.map(profileId => profilesearch.ProfileId.unsafe(profileId.value))
}
