package nl.dpes.profilesearch.service.model
package dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

case class Education(
  fromDate: Option[MonthOfYear],
  toDate: Option[MonthOfYear],
  school: Option[Institute],
  fieldOfStudy: Option[FieldOfStudy],
  grade: Option[EducationLevel],
  description: Option[EducationDescription]
) derives Decoder {
  def period: Period[MonthOfYear] = Period(fromDate, toDate)
}
object Education {
  given Ordering[Education] = Ordering.by((e: Education) => e.period).reverse
  given detailTransformer: Transformer[Education, detailpage.Education] = Transformer
    .define[Education, detailpage.Education]
    .withFieldRenamed(_.grade, _.educationLevel)
    .withFieldComputed(_.fromDate, e => e.fromDate.map(MonthOfYear.fromDateTransformer.transform))
    .withFieldComputed(_.toDate, e => e.toDate.map(MonthOfYear.toDateTransformer.transform))
    .buildTransformer
}
