package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type TrainingName = String
object TrainingName {
  extension (name: TrainingName) def value: String = name

  given Decoder[TrainingName] = Decoder.decodeString

  given Transformer[TrainingName, detailpage.TrainingName] = (value: TrainingName) => detailpage.TrainingName(value)
}
