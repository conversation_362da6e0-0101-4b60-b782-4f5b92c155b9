package nl.dpes.profilesearch.utils

object CommaSeparatedStrings {
  def stringify[T, U](list: List[T])(toString: T => String, fromString: String => U): Option[U] =
    if (list.isEmpty) None else Some(fromString(list.map(toString).mkString(",")))

  def extractList[T, U](optValue: Option[U])(toString: U => String, fromString: String => T): List[T] =
    optValue
      .map(toString)
      .map(_.split(",").map(_.trim).toList.filterNot(_.isEmpty).map(fromString))
      .getOrElse(List.empty)
}
