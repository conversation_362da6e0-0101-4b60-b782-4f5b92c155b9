package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

case class Attachment(
  id: AttachmentId,
  fileName: FilePath,
  name: FileName,
  extension: Extension,
  uploadDate: Timestamp
) derives Decoder
object Attachment {
  given Ordering[Attachment] = Ordering.by((a: Attachment) => a.uploadDate).reverse
  given Transformer[Attachment, detailpage.Attachment] =
    Transformer.define[Attachment, detailpage.Attachment].withFieldUnused(_.uploadDate).buildTransformer
}
