package nl.dpes.profilesearch.utils

import scala.annotation.tailrec
import scala.collection.mutable

object SimpleSearchQueryTranslator {
  sealed abstract class TranslationError(message: String) extends Throwable(message)
  case class UnmatchedQuoteOrBracket(query: String) extends TranslationError(s"Unmatched quotes or brackets in the search string: $query")

  def translate(input: String): Either[TranslationError, String] =
    val regex = "\"([^\"]*)\"|(?i)\\b(AND|OR|NOT|EN|OF|NIET)\\b|(&|!)".r

    val result = new StringBuilder
    var lastEnd = 0

    val matches = regex.findAllMatchIn(input)
    for (m <- matches) {
      result.append(input.substring(lastEnd, m.start))
      if (m.group(1) != null) {
        // Quoted string - keep as-is
        result.append(m.group(0))
      } else {
        val translated = Option(m.group(2)) match {
          case Some(op) =>
            op.toUpperCase match {
              case "AND" | "and" | "EN" | "en"     => "+"
              case "OR" | "or" | "OF" | "of"       => "|"
              case "NOT" | "not" | "NIET" | "niet" => "-"
            }
          case None =>
            m.group(3) match {
              case "&" => "+"
              case "!" => "-"
              case _   => ""
            }
        }
        result.append(translated)
      }
      lastEnd = m.end
    }

    result.append(input.substring(lastEnd))

    val isSimple = result.forall(c => c.isLetterOrDigit || c.isSpaceChar)
    if (matches.isEmpty && isSimple) {
      result.append("*") // If no operators found, treat as a prefix search
    }

    val finalResult = result
      .toString()
      .replace("- ", "-")

    if (areQuotesAndBracketsClosed(finalResult)) {
      Right(
        finalResult
      ) // Ensure no space after NOT operator
    } else {
      Left(UnmatchedQuoteOrBracket(input))
    }

  def areQuotesAndBracketsClosed(input: String): Boolean = {
    @tailrec
    def inner(input: String, stack: List[Char], inQuote: Boolean): Boolean =
      if (input.isEmpty) {
        !inQuote && stack.isEmpty
      } else {
        val c = input.head
        c match {
          case '"' =>
            inner(input.tail, stack, !inQuote)
          case '(' | '[' | '{' if !inQuote =>
            inner(input.tail, c :: stack, inQuote)
          case ')' if !inQuote =>
            stack match {
              case h :: t if h == '(' => inner(input.tail, t, inQuote)
              case _                  => false
            }
          case ']' if !inQuote =>
            stack match {
              case h :: t if h == '[' => inner(input.tail, t, inQuote)
              case _                  => false
            }
          case '}' if !inQuote =>
            stack match {
              case h :: t if h == '{' => inner(input.tail, t, inQuote)
              case _                  => false
            }
          case _ =>
            inner(input.tail, stack, inQuote) // ignore other characters
        }
      }

    inner(input, List.empty, false)
  }
}
