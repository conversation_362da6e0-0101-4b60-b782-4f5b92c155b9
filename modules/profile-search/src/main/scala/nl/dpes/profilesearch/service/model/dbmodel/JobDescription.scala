package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type JobDescription = String
object JobDescription {
  extension (jobTitle: JobDescription) def value: String = jobTitle

  given Decoder[JobDescription] = Decoder.decodeString

  given Transformer[JobDescription, detailpage.JobDescription] = (value: JobDescription) => detailpage.JobDescription(value)
}
