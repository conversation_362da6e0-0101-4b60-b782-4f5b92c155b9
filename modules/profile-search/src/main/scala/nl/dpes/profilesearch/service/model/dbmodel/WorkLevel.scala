package nl.dpes.profilesearch.service.model
package dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.{detailpage, profilesearch}

opaque type WorkLevel = String
object WorkLevel {
  def apply(level: String): WorkLevel = level
  extension (workLevel: WorkLevel) {
    def value: String = workLevel
  }
  given Decoder[WorkLevel] = Decoder.decodeString
  given detailTransformer: Transformer[WorkLevel, detailpage.WorkLevel] =
    (value: WorkLevel) => detailpage.WorkLevel(value)
  given searchTransformer: Transformer[WorkLevel, profilesearch.WorkLevel] =
    (value: WorkLevel) => profilesearch.WorkLevel(value)
}
