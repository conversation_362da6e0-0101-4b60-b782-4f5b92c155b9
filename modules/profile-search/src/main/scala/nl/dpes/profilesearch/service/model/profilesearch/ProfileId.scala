package nl.dpes.profilesearch
package service.model.profilesearch

import doobie.Meta

opaque type ProfileId = String

object ProfileId {
  def apply(value: String): Either[String, ProfileId] =
    if (value == null || value == "") "Profile id cannot be empty".asLeft
    else value.asRight

  def unsafe(value: String): ProfileId = value

  extension (id: ProfileId) def value: String = id

  given Meta[ProfileId] = Meta.StringMeta
    .tiemap(ProfileId(_))(_.value)
}
