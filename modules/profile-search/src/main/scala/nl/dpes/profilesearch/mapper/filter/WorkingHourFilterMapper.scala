package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.requests.searches.queries.Query
import com.sksamuel.elastic4s.requests.searches.term.TermsQuery
import nl.dpes.profilesearch.domain.WorkingHours
import nl.dpes.profilesearch.domain.filter.{Filter, WorkingHourFilter}

object WorkingHourFilterMapper extends FilterToQueryMapper {

  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = { case WorkingHourFilter(workingHours) =>
    Seq(TermsQuery("workingHours", workingHours.map(WorkingHours.toEntryName).toList))
  }
}
