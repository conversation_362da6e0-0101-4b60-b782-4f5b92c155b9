package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.requests.searches.queries.Query
import com.sksamuel.elastic4s.requests.searches.term.TermsQuery
import nl.dpes.profilesearch.domain.filter.{AvailabilityFilter, CareerLevelFilter, Filter, LanguageFilter}

object LanguageFilterMapper extends FilterToQueryMapper {
  private def toQuery(filter: LanguageFilter): Query =
    TermsQuery("languages.name", filter.languages.toList)

  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = { case filter: LanguageFilter =>
    Seq(toQuery(filter))
  }
}
