package nl.dpes.profilesearch.service.viewedprofiles

import doobie.Meta
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch

opaque type RecruiterId = String

object RecruiterId {
  given Meta[RecruiterId] = Meta[String].imap(apply)(_.value)
  given Transformer[profilesearch.RecruiterId, RecruiterId] = (recruiterId: profilesearch.RecruiterId) => recruiterId.value
  def apply(id: String): RecruiterId = id
  extension (id: RecruiterId) def value: String = id
}
