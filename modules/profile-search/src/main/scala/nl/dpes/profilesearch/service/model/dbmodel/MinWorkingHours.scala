package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type MinWorkingHours = Int
object MinWorkingHours {
  def apply(minWorkingHours: Int): MinWorkingHours = minWorkingHours
  extension (maxWorkingHours: MinWorkingHours) {
    def value: Int = maxWorkingHours
  }
  given Decoder[MinWorkingHours] = Decoder.decodeInt
//  given Transformer[MinWorkingHours, Int] = Transformer.define[MinWorkingHours, Int].buildTransformer
  given Transformer[MinWorkingHours, detailpage.MinWorkingHours] =
    (minWorkingHours: MinWorkingHours) => detailpage.MinWorkingHours(minWorkingHours.value)
}
