package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.ElasticApi.boolQuery
import com.sksamuel.elastic4s.requests.searches.queries.matches.MatchQuery
import com.sksamuel.elastic4s.requests.searches.queries.{NestedQuery, Query}
import com.sksamuel.elastic4s.requests.searches.term.WildcardQuery
import nl.dpes.profilesearch.domain.filter.{Filter, SearchTermFilter}
import com.sksamuel.elastic4s.requests.searches.queries.SimpleStringQuery

object SearchTermFilterMapper extends FilterToQueryMapper {

  private val targetSearchFields: Seq[String] = Seq(
    "attachmentContents.content",
    "functionGroups.raw",
    "preferredJobs.raw",
    "workLevels",
    "commute.city",
    "commute.province",
    "commute.zipCode",
    "experiences.city",
    "experiences.jobTitle.raw",
    "education.school.raw"
  )

  private def targetSearchFieldQueries(term: String): Query =
    SimpleStringQuery(
      query = term,
      fields = targetSearchFields.map(fieldName => (fieldName, None)),
      operator = Some("AND"),
      analyzeWildcard = Some(true)
    )

  private def toQueries(filter: SearchTermFilter): Seq[Query] =
    Seq(
      boolQuery().should(
        targetSearchFieldQueries(filter.query.value)
      )
    )

  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = { case filter: SearchTermFilter =>
    toQueries(filter)
  }
}
