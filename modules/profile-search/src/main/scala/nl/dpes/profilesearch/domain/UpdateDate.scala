package nl.dpes.profilesearch.domain

import java.time.{Instant, Period, ZoneId, ZonedDateTime}

enum UpdateDate(val entryName: String) {
  case Last24Hours extends UpdateDate("Afgelopen 24 uur")
  case Last3Days extends UpdateDate("Afgelopen 3 dagen")
  case LastWeek extends UpdateDate("Afgelopen week")
  case LastMonth extends UpdateDate("Afgelopen maand")
  case Last3Months extends UpdateDate("Afgelopen 3 maanden")
  case Last6Months extends UpdateDate("Afgelopen 6 maanden")
  case LastYear extends UpdateDate("Afgelopen jaar")
  case All extends UpdateDate("Alles")

  override def toString: String = entryName
}

object UpdateDate {
  def fromString(value: String): Option[UpdateDate] =
    UpdateDate.values.find(_.entryName.toLowerCase == value.toLowerCase)

  def toTimestamp(updateDate: UpdateDate, now: Instant): Instant =
    val zonedNow: ZonedDateTime = now.atZone(ZoneId.of("UTC"))

    updateDate match {
      case Last24Hours => zonedNow.minus(Period.ofDays(1)).toInstant
      case Last3Days   => zonedNow.minus(Period.ofDays(3)).toInstant
      case LastWeek    => zonedNow.minus(Period.ofWeeks(1)).toInstant
      case LastMonth   => zonedNow.minus(Period.ofMonths(1)).toInstant
      case Last3Months => zonedNow.minus(Period.ofMonths(3)).toInstant
      case Last6Months => zonedNow.minus(Period.ofMonths(6)).toInstant
      case LastYear    => zonedNow.minus(Period.ofYears(1)).toInstant
      case All         => Instant.MIN
    }

  given Ordering[UpdateDate] = Ordering.by(
    List(
      Last24Hours,
      Last3Days,
      LastWeek,
      LastMonth,
      Last3Months,
      Last6Months,
      LastYear,
      All
    ).zipWithIndex.toMap
  )
}
