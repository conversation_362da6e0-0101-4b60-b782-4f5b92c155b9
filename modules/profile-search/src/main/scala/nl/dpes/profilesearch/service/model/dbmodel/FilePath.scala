package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type FilePath = String
object FilePath {
  extension (path: FilePath) def value: String = path

  given Decoder[FilePath] = Decoder.decodeString

  given Transformer[FilePath, detailpage.FilePath] = (value: FilePath) => detailpage.FilePath(value)
}
