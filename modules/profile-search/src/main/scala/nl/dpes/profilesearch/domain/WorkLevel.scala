package nl.dpes.profilesearch.domain

opaque type WorkLevel = String

object WorkLevel:
  def apply(value: String): WorkLevel = value
  extension (workLevel: WorkLevel) def value: String = workLevel

  private val workLevelOrder: List[WorkLevel] = List(
    "Postdoctoraal",
    "WO",
    "HBO",
    "MBO",
    "VWO",
    "HAVO",
    "VMBO/Mavo",
    "LBO",
    "Lagere school"
  )

  given Ordering[WorkLevel] = PredefinedOrder(workLevelOrder)
