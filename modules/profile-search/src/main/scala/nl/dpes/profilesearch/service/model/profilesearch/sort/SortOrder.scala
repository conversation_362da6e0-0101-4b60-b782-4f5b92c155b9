package nl.dpes.profilesearch
package service.model.profilesearch.sort

import cats.ApplicativeThrow
import com.sksamuel.elastic4s.requests.searches.sort.SortOrder as ElasticSortOrder

import scala.util.control.NoStackTrace

opaque type SortOrder = String

object SortOrder {
  val desc: SortOrder = "desc"
  val asc: SortOrder = "asc"

  case class InvalidSortOrder(order: String) extends Throwable(s"Invalid sort order: '$order''") with NoStackTrace

  def apply[F[_]: ApplicativeThrow](sortOrder: String): F[SortOrder] =
    if (List(desc, asc).contains(sortOrder)) sortOrder.pure
    else InvalidSortOrder(sortOrder).raiseError

  extension (sortOrder: SortOrder)
    def order: String = sortOrder
    def elasticOrder: ElasticSortOrder = s"$order" match {
      case "asc"  => ElasticSortOrder.ASC
      case "desc" => ElasticSortOrder.DESC
    }

  def unsafe(order: String): SortOrder = order
}
