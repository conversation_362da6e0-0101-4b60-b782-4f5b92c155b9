package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type MaxTravelDistance = Int
object MaxTravelDistance {
  def apply(maxTravelDistance: Int): MaxTravelDistance = maxTravelDistance
  extension (maxTravelDistance: MaxTravelDistance) {
    def value: Int = maxTravelDistance
  }
  given Decoder[MaxTravelDistance] = Decoder.decodeInt
  given detailTransformer: Transformer[MaxTravelDistance, detailpage.MaxTravelDistance] =
    (value: MaxTravelDistance) => detailpage.MaxTravelDistance(value)
}
