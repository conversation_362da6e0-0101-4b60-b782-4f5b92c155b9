package nl.dpes.profilesearch
package creditservice

import cats.*
import cats.effect.*
import cats.effect.std.UUIDGen
import io.scalaland.chimney.dsl.*
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpgr.webshoppproduct.ProductConfiguration.ProfileViewConfiguration
import org.typelevel.log4cats.LoggerFactory

trait CreditService[F[_]] {
  def useProfileView(recruiterId: profilesearch.RecruiterId): F[EntitlementId]
}
object CreditService {
  def impl[F[_]: UUIDGen: MonadThrow: LoggerFactory](client: CreditServiceClient[F]): CreditService[F] = new CreditService[F] {
    def useProfileView(recruiterId: profilesearch.RecruiterId): F[EntitlementId] = for {
      correlationId <- CorrelationId.generate
      response      <- client.useEntitlement(recruiterId.into[RecruiterId].transform, ProfileViewConfiguration, correlationId)
    } yield response.id
  }

  def resource[F[_]: Async: LoggerFactory: UUIDGen](config: CreditServiceConfig): Resource[F, CreditService[F]] =
    CreditServiceClient.resource(config).map(impl[F])
}
