package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type Introduction = String
object Introduction {
  extension (introduction: Introduction) def value: String = introduction

  given Decoder[Introduction] = Decoder.decodeString

  given Transformer[Introduction, detailpage.Introduction] = (value: Introduction) => detailpage.Introduction(value)
}
