package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.requests.searches.queries.Query
import com.sksamuel.elastic4s.requests.searches.term.TermsQuery
import nl.dpes.profilesearch.domain.filter.{CareerLevelFilter, Filter}

object CareerLevelFilterMapper extends FilterToQueryMapper {
  private def toQuery(filter: CareerLevelFilter): Query =
    TermsQuery("careerLevel", filter.values.toList)

  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = { case filter: CareerLevelFilter =>
    Seq(toQuery(filter))
  }
}
