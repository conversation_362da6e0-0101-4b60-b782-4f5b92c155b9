package nl.dpes.profilesearch
package creditservice

import cats.Functor
import cats.effect.std.UUIDGen

opaque type CorrelationId = String
object CorrelationId {
  extension (correlationId: CorrelationId) def value: String = correlationId

  def apply(correlationId: String): CorrelationId = correlationId

  def generate[F[_]: UUIDGen: Functor]: F[CorrelationId] = UUIDGen[F].randomUUID.map(_.toString)
}
