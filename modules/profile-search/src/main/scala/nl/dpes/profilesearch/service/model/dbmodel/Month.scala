package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type Month = Int
object Month {
  val min: Month = 1
  val max: Month = 12

  extension (month: Month) {
    def value: Int = month
  }

  def apply(month: Int): Month = month

  given BoundedOrdering[Month] with
    def compare(x: Month, y: Month): Int = x.compare(y)
    def min: Month = Month.min
    def max: Month = Month.max

  given Decoder[Month] = Decoder.decodeInt

  given Transformer[Month, detailpage.Month] = (value: Month) => detailpage.Month(value)
}
