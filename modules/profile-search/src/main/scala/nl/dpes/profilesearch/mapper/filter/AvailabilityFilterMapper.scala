package nl.dpes.profilesearch.mapper.filter

import com.sksamuel.elastic4s.requests.searches.queries.Query
import com.sksamuel.elastic4s.requests.searches.term.TermsQuery
import nl.dpes.profilesearch.domain.filter.{AvailabilityFilter, Filter}

object AvailabilityFilterMapper extends FilterToQueryMapper {
  private def toQuery(filter: AvailabilityFilter): Query =
    TermsQuery("availability", filter.values.map(_.entryName).toList)

  override val mappingFunction: PartialFunction[Filter, Seq[Query]] = { case filter: AvailabilityFilter =>
    Seq(toQuery(filter))
  }
}
