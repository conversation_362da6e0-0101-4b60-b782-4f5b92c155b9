package nl.dpes.profilesearch.domain

enum Availability(val entryName: String) {
  case InOverleg extends Availability("In overleg")
  case PerDirect extends Availability("Per direct")

  override def toString: String = entryName
}

object Availability {
  def fromString(value: String): Option[Availability] =
    Availability.values.find(_.entryName.toLowerCase == value.toLowerCase)

  given Ordering[Availability] = PredefinedOrder(Availability.values)
}
