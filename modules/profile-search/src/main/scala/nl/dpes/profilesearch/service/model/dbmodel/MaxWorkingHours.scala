package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type MaxWorkingHours = Int
object MaxWorkingHours {
  def apply(maxWorkingHours: Int): MaxWorkingHours = maxWorkingHours
  extension (maxWorkingHours: MaxWorkingHours) {
    def value: Int = maxWorkingHours
  }
  given Decoder[MaxWorkingHours] = Decoder.decodeInt
  given Transformer[MaxWorkingHours, detailpage.MaxWorkingHours] =
    (maxWorkingHours: MaxWorkingHours) => detailpage.MaxWorkingHours(maxWorkingHours.value)
}
