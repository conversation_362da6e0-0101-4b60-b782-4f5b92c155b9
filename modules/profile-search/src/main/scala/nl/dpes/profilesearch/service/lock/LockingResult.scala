package nl.dpes.profilesearch.service.lock

import java.util.UUID
import scala.concurrent.duration.Duration
import scala.util.control.NoStackTrace

sealed trait LockingResult
object LockingResult {
  case class LockAcquired(key: Key, id: UUID) extends LockingResult
  sealed abstract class LockingError(message: String) extends Throwable(message) with NoStackTrace with LockingResult
  case class LockAlreadyHeld(key: Key, id: UUID) extends LockingError(s"Lock already held for key: $key with id: $id")
  case class LockAcquisitionFailed(key: Key, id: UUID) extends LockingError(s"Failed to acquire lock for key: $key with id: $id")
  case class LockTimedOut(key: Key, duration: Duration) extends LockingError(s"Lock timed out for key: $key after duration: $duration")
}
