package nl.dpes.profilesearch.service.model
package dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.{detailpage, profilesearch}

opaque type City = String
object City {
  def apply(city: String): City = city
  extension (city: City) {
    def value: String = city
  }
  given Decoder[City] = Decoder.decodeString
  given detailTransformer: Transformer[City, detailpage.City] =
    (value: City) => detailpage.City(value)
  given searchTransformer: Transformer[City, profilesearch.City] =
    (value: City) => profilesearch.City(value)
}
