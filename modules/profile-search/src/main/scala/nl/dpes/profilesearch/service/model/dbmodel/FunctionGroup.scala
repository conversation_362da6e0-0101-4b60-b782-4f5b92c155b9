package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type FunctionGroup = String
object FunctionGroup {
  extension (functionGroup: FunctionGroup) def value: String = functionGroup

  given Decoder[FunctionGroup] = Decoder.decodeString

  given Transformer[FunctionGroup, detailpage.FunctionGroup] = (value: FunctionGroup) => detailpage.FunctionGroup(value)
}
