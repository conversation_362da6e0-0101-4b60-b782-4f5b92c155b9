package nl.dpes.profilesearch.service.viewedprofiles

import cats.effect.MonadCancelThrow
import cats.effect.*
import cats.implicits.*
import doobie.implicits.*
import doobie.util.transactor.Transactor
import doobie.Fragment
import doobie.util.meta.Meta
import nl.dpes.profilesearch.service.TableName
import nl.dpes.profilesearch.service.model.profilesearch
import io.scalaland.chimney.dsl.*
import org.typelevel.log4cats.LoggerFactory

import java.sql.Timestamp
import java.time.Instant

trait ViewedProfilesRepository[F[_]] {
  def initialize: F[Unit]
  def cleanup(retentionThresholdInDays: Long): F[Unit]
  def save(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit]
  def getLastViewedDate(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Option[LastViewedDate]]
}

object ViewedProfilesRepository {

  given Meta[Instant] = Meta[Timestamp].imap(_.toInstant)(Timestamp.from)

  def impl[F[_]: {MonadCancelThrow, LoggerFactory}](tableName: TableName, xa: Transactor[F]): ViewedProfilesRepository[F] =
    new ViewedProfilesRepository[F] {

      val viewedProfilesTable: Fragment = tableName.fragment

      override def initialize: F[Unit] =
        sql"""
       CREATE TABLE IF NOT EXISTS $viewedProfilesTable (
          recruiterId      VARCHAR(36),
          profileId        VARCHAR(36),
          lastViewedDate   TIMESTAMP NOT NULL,
          PRIMARY KEY (recruiterId, profileId),
          INDEX (recruiterId),
          INDEX (profileId)
       )
      """.update.run.transact(xa).void

      override def save(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit] = {
        val dbRecruiterId = getDbRecruiterId(recruiterId)
        val dbProfileId = getDbProfileId(profileId)

        sql"""
          REPLACE INTO $viewedProfilesTable (recruiterId, profileId, lastViewedDate)
          VALUES ($dbRecruiterId, $dbProfileId, ${Instant.now()})
      """.update.run.transact(xa).void
      }

      override def cleanup(retentionThresholdInDays: Long): F[Unit] =
        for {
          _ <-
            sql"""
            CREATE PROCEDURE IF NOT EXISTS CleanupViewedProfiles()
            BEGIN
              DELETE FROM $viewedProfilesTable
              WHERE lastViewedDate < (NOW() - INTERVAL $retentionThresholdInDays DAY);
            END;
            """.update.run.transact(xa).void
          _ <-
            sql"""
            CREATE EVENT IF NOT EXISTS CleanupViewedProfilesEvent
            ON SCHEDULE EVERY 1 DAY
            STARTS CURRENT_TIMESTAMP
            ON COMPLETION NOT PRESERVE
            ENABLE
            DO BEGIN
               DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
               BEGIN
                  DO RELEASE_LOCK('cleanup_viewed_profiles_events_lock');
               END;
               IF (GET_LOCK('cleanup_viewed_profiles_events_lock', 0)) THEN
                  CALL CleanupViewedProfiles();
               END IF;
               DO RELEASE_LOCK('cleanup_viewed_profiles_events_lock');
            END
            """.update.run.transact(xa).void
        } yield ()

      private def getDbRecruiterId(recruiterId: profilesearch.RecruiterId) = recruiterId.into[RecruiterId].transform

      private def getDbProfileId(profileId: profilesearch.ProfileId) = profileId.into[ProfileId].transform

      override def getLastViewedDate(
        recruiterId: profilesearch.RecruiterId,
        profileId: profilesearch.ProfileId
      ): F[Option[LastViewedDate]] =
        sql"""
            SELECT lastViewedDate
            FROM $viewedProfilesTable
            WHERE recruiterId = $recruiterId AND profileId = $profileId
           """
          .query[LastViewedDate]
          .option
          .transact(xa)
    }

  def resource[F[_]: {MonadCancelThrow, LoggerFactory}](
    tableName: TableName,
    retentionThresholdInDays: Long,
    transactor: Transactor[F]
  ): Resource[F, ViewedProfilesRepository[F]] =
    Resource.eval(
      for {
        repo <- ViewedProfilesRepository.impl[F](tableName, transactor).pure
        _    <- repo.initialize
        _    <- repo.cleanup(retentionThresholdInDays)
        _    <- LoggerFactory[F].getLogger.info("Scheduling the cleanup for old profile views")
      } yield repo
    )
}
