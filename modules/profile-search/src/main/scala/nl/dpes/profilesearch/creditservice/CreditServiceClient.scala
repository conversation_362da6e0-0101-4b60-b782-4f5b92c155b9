package nl.dpes.profilesearch
package creditservice

import cats.effect.*
import nl.dpgr.webshoppproduct.ProductConfiguration
import nl.dpes.profilesearch.service.model.profilesearch
import sttp.client3.*
import sttp.client3.circe.*
import sttp.client3.httpclient.cats.HttpClientCatsBackend
import sttp.model.StatusCode

import scala.util.control.NoStackTrace

trait CreditServiceClient[F[_]] {
  def useEntitlement(recruiterId: RecruiterId, productConfiguration: ProductConfiguration, correlationId: CorrelationId): F[UsageResponse]
}
object CreditServiceClient {
  case class NotEntitled(recruiterId: RecruiterId, correlationId: CorrelationId)
      extends Throwable(s"No product available for recruiter '$recruiterId' ($correlationId)")
      with NoStackTrace

  def impl[F[_]: MonadCancelThrow](
    creditService: CreditServiceConfig,
    sttpBackend: Resource[F, SttpBackend[F, Any]]
  ): CreditServiceClient[F] =
    new CreditServiceClient[F] {
      def useEntitlement(
        recruiterId: RecruiterId,
        productConfiguration: ProductConfiguration,
        correlationId: CorrelationId
      ): F[UsageResponse] =
        sttpBackend.use { backend =>
          basicRequest
            .post(uri"http://${creditService.host}:${creditService.port}/recruiter/$recruiterId/entitlements/use")
            .header("Correlation-Id", correlationId.value)
            .body(productConfiguration)
            .response(asJson[UsageResponse])
            .send(backend)
            .map(_.body)
            .rethrow
            .adaptErr { case HttpError(_, StatusCode.Conflict) => NotEntitled(recruiterId, correlationId) }
        }
    }

  def resource[F[_]: Async](config: CreditServiceConfig): Resource[F, CreditServiceClient[F]] =
    Resource.pure(impl(config, HttpClientCatsBackend.resource[F]()))
}
