package nl.dpes.profilesearch.service.model
package dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.{detailpage, profilesearch}

opaque type PreferredJob = String
object PreferredJob {
  def apply(job: String): PreferredJob = job
  extension (job: PreferredJob) def value: String = job

  given Decoder[PreferredJob] = Decoder.decodeString

  given detailTransformer: Transformer[PreferredJob, detailpage.PreferredJob] = (value: PreferredJob) => detailpage.PreferredJob(value)
  given searchTransformer: Transformer[PreferredJob, profilesearch.PreferredJob] = (value: PreferredJob) =>
    profilesearch.PreferredJob(value)
}
