package nl.dpes.profilesearch.domain

opaque type CareerLevel = String

object CareerLevel:
  def apply(value: String): CareerLevel = value
  extension (careerLevel: CareerLevel) def value: String = careerLevel

  private val careerLevelOrder: List[CareerLevel] = List(
    "Starter",
    "<PERSON><PERSON><PERSON>",
    "Leidinggevend",
    "Senior management",
    "Directie"
  )

  given Ordering[CareerLevel] = PredefinedOrder(careerLevelOrder)
