package nl.dpes.profilesearch.service.model.dbmodel

import cats.effect.Sync
import com.sksamuel.elastic4s.requests.searches.SearchResponse
import io.circe.parser.decode
import nl.dpes.profilesearch.domain
import nl.dpes.profilesearch.service.model.dbmodel.AggregationResult
import nl.dpes.profilesearch.service.model.dbmodel.FacetsResult.TermBucket
import cats.implicits.*
import nl.dpes.profilesearch.domain.{Aggregation, AggregationBucket, WorkingHours}
import org.typelevel.log4cats.{Logger, LoggerFactory, LoggerName}

import scala.reflect.ClassTag

object AggregationDecoder {
  def decodeAllAggregationResultFromSearchResponse[F[+_]: Sync: LoggerFactory](
    response: SearchResponse
  ): F[Map[domain.Aggregation, List[Aggregations]]] = {
    val aggregationResult = decode[AllAggregationResult](response.aggregationsAsString)
      .map(AllAggregationResult.toAggregationResult)
    decodeAggregationResult(aggregationResult)
  }

  def decodeAggregationResultFromSearchResponse[F[+_]: Sync: LoggerFactory](
    response: SearchResponse
  ): F[Map[domain.Aggregation, List[Aggregations]]] =
    decodeAggregationResult(decode[AggregationResult](response.aggregationsAsString))

  private def decodeAggregationResult[F[+_]: Sync: LoggerFactory](aggregationResult: Either[Throwable, AggregationResult]) =
    aggregationResult match {
      case Right(aggregationResult) =>
        for {
          workLevel <- decodeTermBuckets(domain.Aggregation.WorkLevel, aggregationResult.aggregations.workLevels.aggregation.buckets)
          availability <- decodeTermBuckets(
            domain.Aggregation.Availability,
            aggregationResult.aggregations.availabilities.aggregation.buckets
          )
          careerLevels <- decodeTermBuckets(
            domain.Aggregation.CareerLevel,
            aggregationResult.aggregations.careerLevels.aggregation.buckets
          )
          driverLicenses <- decodeTermBuckets(
            domain.Aggregation.DriverLicense,
            aggregationResult.aggregations.driverLicenses.aggregation.buckets
          )
          functionGroups <- decodeTermBuckets(
            domain.Aggregation.FunctionGroup,
            aggregationResult.aggregations.functionGroups.aggregation.buckets
          )
          languages <- decodeTermBuckets(
            domain.Aggregation.Language,
            aggregationResult.aggregations.languages.aggregation.buckets
          )
          provinces <- decodeTermBuckets(
            domain.Aggregation.Province,
            aggregationResult.aggregations.provinces.aggregation.buckets
          )
          requestedSalaries <- decodeTermBuckets(
            domain.Aggregation.RequestedSalary,
            aggregationResult.aggregations.requestedSalaries.aggregation.buckets
          )
          updatedDates <- decodeTermBuckets(
            domain.Aggregation.UpdatedDate,
            aggregationResult.aggregations.updatedDate.aggregation.buckets
          )
          workingHours <- decodeTermBuckets(
            domain.Aggregation.WorkingHours,
            aggregationResult.aggregations.workingHours.aggregation.buckets
          )
          commuteDistances <- aggregationResult.aggregations.commuteDistance match {
            case Some(commuteDistance) =>
              decodeTermBuckets(
                domain.Aggregation.CommuteDistance,
                commuteDistance.aggregation.buckets
              )
            case None => Sync[F].pure(List.empty[Aggregations])
          }
        } yield Map(
          domain.Aggregation.WorkLevel       -> workLevel,
          domain.Aggregation.Availability    -> availability,
          domain.Aggregation.CareerLevel     -> careerLevels,
          domain.Aggregation.DriverLicense   -> driverLicenses,
          domain.Aggregation.FunctionGroup   -> functionGroups,
          domain.Aggregation.Language        -> languages,
          domain.Aggregation.Province        -> provinces,
          domain.Aggregation.RequestedSalary -> requestedSalaries,
          domain.Aggregation.UpdatedDate     -> updatedDates,
          domain.Aggregation.WorkingHours    -> workingHours,
          domain.Aggregation.CommuteDistance -> commuteDistances
        )
      case Left(thr) => thr.raiseError[F, Map[domain.Aggregation, List[Aggregations]]]
    }

  type Aggregations = AggregationBucket[domain.Availability] | AggregationBucket[domain.CareerLevel] |
    AggregationBucket[domain.RequestedSalary] | AggregationBucket[domain.UpdateDate] | AggregationBucket[domain.WorkingHours] |
    AggregationBucket[domain.WorkLevel] | AggregationBucket[String] | AggregationBucket[domain.CommuteDistance]

  def aggregationBucketToTermBucket(aggregationBucket: Aggregations): TermBucket =
    TermBucket(aggregationBucket.name.toString, aggregationBucket.count)

  private def decodeTermBuckets[F[+_]: Sync: LoggerFactory](
    aggregation: domain.Aggregation,
    buckets: List[TermBucket]
  ): F[List[Aggregations]] =
    aggregation match {
      case Aggregation.Availability =>
        transformToAggregationBuckets[F, domain.Availability](
          aggregation,
          buckets,
          b => domain.Availability.fromString(b.key).map(AggregationBucket(_, b.`doc_count`))
        )
      case Aggregation.CareerLevel =>
        transformToAggregationBuckets[F, domain.CareerLevel](
          aggregation,
          buckets,
          b => Some(AggregationBucket(domain.CareerLevel.apply(b.`key`), b.`doc_count`))
        )
      case Aggregation.DriverLicense => transformToStringAggregationBuckets[F](aggregation, buckets)
      case Aggregation.FunctionGroup => transformToStringAggregationBuckets[F](aggregation, buckets)
      case Aggregation.Language      => transformToStringAggregationBuckets[F](aggregation, buckets)
      case Aggregation.Province      => transformToStringAggregationBuckets[F](aggregation, buckets)
      case Aggregation.RequestedSalary =>
        transformToAggregationBuckets[F, domain.RequestedSalary](
          aggregation,
          buckets,
          b => domain.RequestedSalary.fromString(b.`key`).map(AggregationBucket(_, b.`doc_count`))
        )
      case Aggregation.UpdatedDate =>
        transformToAggregationBuckets[F, domain.UpdateDate](
          aggregation,
          buckets,
          b => domain.UpdateDate.fromString(b.key).map(AggregationBucket(_, b.`doc_count`))
        )
      case Aggregation.WorkingHours =>
        transformToAggregationBuckets[F, domain.WorkingHours](
          aggregation,
          buckets,
          b => domain.WorkingHours.fromString(b.key).map(AggregationBucket(_, b.`doc_count`))
        )
      case Aggregation.WorkLevel =>
        transformToAggregationBuckets[F, domain.WorkLevel](
          aggregation,
          buckets,
          b => Some(AggregationBucket(domain.WorkLevel.apply(b.key), b.`doc_count`))
        )
      case Aggregation.CommuteDistance =>
        transformToAggregationBuckets[F, domain.CommuteDistance](
          aggregation,
          buckets,
          b => Some(AggregationBucket(domain.CommuteDistance.fromString(b.key), b.`doc_count`))
        )
    }

  private def transformToStringAggregationBuckets[F[+_]: Sync: LoggerFactory](aggregation: Aggregation, buckets: List[TermBucket]) =
    transformToAggregationBuckets[F, String](
      aggregation,
      buckets,
      b => Some(AggregationBucket(b.key, b.`doc_count`))
    )

  private def transformToAggregationBuckets[F[_]: Sync: LoggerFactory, A: Ordering](
    aggregation: domain.Aggregation,
    buckets: List[TermBucket],
    transformation: TermBucket => Option[AggregationBucket[A]]
  ): F[List[AggregationBucket[A]]] = {
    val logger: Logger[F] = LoggerFactory[F].getLogger(using LoggerName("Profile search service"))

    val list = buckets
      .map(bucket =>
        transformation(bucket) match {
          case Some(value) => Right(value.pure[F])
          case None        => Left(logger.warn(s"Unable to parse a value ${bucket.key} for an aggregation ${aggregation.name}"))
        }
      )

    val transformedBuckets: F[List[AggregationBucket[A]]] = list.collect { case Right(bucket: F[AggregationBucket[A]]) => bucket }.sequence
    val errors: F[List[Unit]] = list.collect { case Left(loggedError: F[Unit]) => loggedError }.sequence

    for {
      _      <- errors
      result <- transformedBuckets
    } yield result.sorted
  }
}
