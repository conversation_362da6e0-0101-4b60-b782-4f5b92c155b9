package nl.dpes.profilesearch
package service.lock

import cats.effect.*
import cats.effect.kernel.Async
import cats.effect.std.{<PERSON><PERSON><PERSON>, <PERSON><PERSON>ph<PERSON>, UUIDGen}
import cats.syntax.all.*
import doobie.*
import doobie.free.connection.ConnectionOp.*
import doobie.free.connection.{commit, embed, rollback, setAutoCommit, ConnectionOp}
import doobie.implicits.*
import doobie.util.transactor.Transactor
import nl.dpes.profilesearch.service.lock.LockingResult.*

import java.sql.Connection
import java.util.UUID
import java.util.concurrent.TimeoutException
import scala.concurrent.duration.*

trait LockManager[F[_]] {
  def runInLock[A](key: Key, fa: F[A]): F[A]
}
object LockManager {
  def apply[F[_]: {Async, UUIDGen}](lockService: LockService, xa: Transactor[F], config: LockConfig): F[LockManager[F]] =
    for {
      semaphore <- Semaphore[F](config.maximumConcurrentLocks.value) // make sure not all connections are blocked by locks
      mutexes   <- Async[F].ref(Map.empty[Key, Mutex[F]])
    } yield new LockManager[F] {

      def lockForConnection(key: Key, id: UUID)(connection: Connection): Resource[F, Unit] =
        Resource.make {
          embed(connection, setAutoCommit(false) *> lockService.acquire(key, id) *> commit).void.transact(xa)
        } { _ =>
          embed(connection, lockService.release(key, id) *> commit *> setAutoCommit(true))
            .handleErrorWith(_ => embed(connection, rollback *> setAutoCommit(true)).void)
            .void
            .transact(xa)
        }

      def lock(key: Key, id: UUID): Resource[F, Unit] =
        xa.connect(xa.kernel).flatMap(lockForConnection(key, id))

      def retryInLock[A](key: Key, fa: F[A], retries: Int = 5): F[A] = for {
        id <- UUIDGen[F].randomUUID
        result <- lock(key, id)
          .surround(fa)
          .handleErrorWith {
            case _: LockingError if retries > 0 => Async[F].sleep(100.millis) >> retryInLock(key, fa, retries - 1)
            case e                              => Async[F].raiseError(e)
          }
      } yield result

      def runInLock[A](key: Key, fa: F[A]): F[A] = semaphore.permit.use(_ =>
        for {
          newMutex <- Mutex[F]
          mutex <- mutexes.modify { m =>
            m.get(key) match {
              case Some(mutex) => (m, mutex)
              case None        => (m + (key -> newMutex), newMutex)
            }
          }
          result <- Async[F].timeout(mutex.lock.surround(retryInLock(key, fa)), config.lockTimeout).adaptError { case _: TimeoutException =>
            LockTimedOut(key, config.lockTimeout)
          }
        } yield result
      )
    }
}
