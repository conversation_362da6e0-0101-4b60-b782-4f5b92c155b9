package nl.dpes.profilesearch
package service.lock

import cats.{ApplicativeThrow, MonadThrow}
import nl.dpes.profilesearch.service.lock.LockConfig.LockLimit
import pureconfig.ConfigReader
import pureconfig.error.ExceptionThrown
import pureconfig.generic.derivation.*

import scala.concurrent.duration.Duration
import scala.util.Try
import scala.util.control.NoStackTrace

case class LockConfig(maximumConcurrentLocks: LockLimit, lockTimeout: Duration) derives ConfigReader
object LockConfig {
  sealed abstract class LockConfigError(message: String) extends Throwable(message) with NoStackTrace

  case class LockLimitTooLow(value: Int) extends LockConfigError(s"Lock limit must be greater than zero, but was: $value")

  case class LockLimit private (value: Int) extends AnyVal

  object LockLimit {
    def apply[F[_]: ApplicativeThrow](value: Int): F[LockLimit] =
      ApplicativeThrow[F].fromTry(Try(unsafe(value)))

    def unsafe(value: Int): LockLimit =
      if (value <= 0) throw LockLimitTooLow(value)
      else new LockLimit(value)

    type ErrorType[T] = Either[Throwable, T]

    given ConfigReader[LockLimit] = ConfigReader[Int].emap(LockLimit[ErrorType](_).leftMap(ExceptionThrown(_)))
  }
}
