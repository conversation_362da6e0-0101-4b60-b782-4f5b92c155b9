package nl.dpes.profilesearch.service.viewedprofiles

import doobie.Meta
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch

import java.sql.Timestamp

case class LastViewedDate(timestamp: Long)

object LastViewedDate {
  given Meta[LastViewedDate] = Meta[Timestamp].imap(ts => LastViewedDate(ts.toInstant.getEpochSecond))(lvd => new Timestamp(lvd.timestamp))

  given Transformer[LastViewedDate, profilesearch.LastViewedDate] = (lastViewedDate: LastViewedDate) =>
    profilesearch.LastViewedDate(lastViewedDate.timestamp)
}
