package nl.dpes.profilesearch.service.model.dbmodel

import io.circe.Decoder
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.detailpage

opaque type EducationLevel = String
object EducationLevel {
  extension (educationLevel: EducationLevel) def value: String = educationLevel

  given Decoder[EducationLevel] = Decoder.decodeString

  given Transformer[EducationLevel, detailpage.EducationLevel] =
    (value: EducationLevel) => detailpage.EducationLevel(value)
}
