package nl.dpes.profilesearch
package service.visitedprofiles

import doobie.Meta
import io.circe.{Decoder, Encoder}
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.service.model.profilesearch

opaque type ProfileId = String

object ProfileId {
  def apply(value: String): Either[String, ProfileId] =
    if (value == null || value == "") "Profile id cannot be empty".asLeft
    else value.asRight

  def unsafe(value: String): ProfileId = value

  extension (id: ProfileId) {
    def value: String = id
  }

  given Decoder[ProfileId] = Decoder.decodeString
  given Encoder[ProfileId] = Encoder.encodeString

  given Meta[ProfileId] = Meta.StringMeta
    .tiemap(ProfileId(_))(_.value)

//  given Transformer[ProfileId, search.ProfileId] = (profileId: ProfileId) => search.ProfileId.unsafe(profileId)
  given Transformer[profilesearch.ProfileId, ProfileId] = (profileId: profilesearch.ProfileId) => profileId.value
}
