package nl.dpes.profilesearch
package service
package visitedprofiles

import cats.MonadThrow
import cats.effect.Resource
import io.scalaland.chimney.dsl.into
import nl.dpes.profilesearch.creditservice.CreditService
import nl.dpes.profilesearch.service.model.profilesearch
import nl.dpes.profilesearch.service.unlockedprofiles.UnlockedProfileService
import org.typelevel.log4cats.LoggerFactory

import scala.util.control.NoStackTrace

trait VisitedProfilesService[F[_]] {
  def registerProfileView(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[ProfileView]
}

object VisitedProfilesService {
  case class ViewsExceeded(recruiterId: RecruiterId, profileId: ProfileId)
      extends Throwable(s"Profile '$profileId' cannot be displayed for recruiter '$recruiterId' because the view limit has been reached")
      with NoStackTrace
  case class ProfileAlreadyVisited(recruiterId: RecruiterId, profileId: ProfileId)
      extends Throwable(s"Profile '$profileId' has already been visited by recruiter '$recruiterId'")
      with NoStackTrace

  def resource[F[_]: MonadThrow: LoggerFactory](
    viewLimit: ProfileViewLimit,
    repository: VisitedProfilesRepository[F],
    creditService: CreditService[F],
    unlockedProfileService: UnlockedProfileService[F]
  ): Resource[F, VisitedProfilesService[F]] = Resource.pure {
    def useEntitlement(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[Unit] =
      for {
        entitlementId <- creditService.useProfileView(recruiterId)
        _             <- AccessLogger[F].profileViewEntitlementUsed(recruiterId, profileId, entitlementId)
        _             <- unlockedProfileService.unlockProfile(recruiterId, profileId)
      } yield ()

    new VisitedProfilesService[F] {
      override def registerProfileView(recruiterId: profilesearch.RecruiterId, profileId: profilesearch.ProfileId): F[ProfileView] = (for {
        visited <- repository.read(recruiterId)
        _ <-
          if (visited.contains(profileId))
            ProfileAlreadyVisited(recruiterId.into[RecruiterId].transform, profileId.into[ProfileId].transform).raiseError
          else ().pure
        _ <-
          if (visited.into[ProfileIds].transform.reachedLimit(viewLimit))
            ViewsExceeded(recruiterId.into[RecruiterId].transform, profileId.into[ProfileId].transform).raiseError
          else ().pure
        profileIsUnlocked <- unlockedProfileService.profileIsUnlocked(recruiterId, profileId)
        _ <- if (profileIsUnlocked) AccessLogger[F].doNotUseEntitlement(recruiterId, profileId) else useEntitlement(recruiterId, profileId)
        _ <- repository.update(recruiterId, profileIds => profileIds + profileId)
      } yield ProfileView.New)
        .recoverWith { case _: ProfileAlreadyVisited => ProfileView.Existing.pure }
        .flatTap(result => AccessLogger[F].allowProfileView(recruiterId, profileId, result))
    }
  }

}
