package nl.dpes.savedsearch
package domain

import cats.data.NonEmptyList
import cats.implicits.{catsSyntaxEitherId, catsSyntaxOptionId}
import doobie.{Get, Read}
import io.circe.Decoder.decodeString
import io.scalaland.chimney.Transformer
import nl.dpes.profilesearch.domain.*
import nl.dpes.profilesearch.domain.filter.*

case class SavedSearchFilters(
  searchTerm: Option[SearchTermFilter],
  city: Option[CityFilter],
  geoDistance: Option[GeoDistanceFilter],
  provinces: Option[ProvinceFilter],
  updatedDate: Option[UpdateDateFilter],
  functionGroups: Option[FunctionGroupFilter],
  workLevels: Option[WorkLevelFilter],
  workingHours: Option[WorkingHourFilter],
  careerLevels: Option[CareerLevelFilter],
  requestedSalaries: Option[RequestedSalaryFilter],
  availabilities: Option[AvailabilityFilter],
  driversLicenses: Option[DriversLicenseFilter],
  languages: Option[LanguageFilter]
)

// todo: [Maybe] move it into a special object/class (domain -> json conversion)
object SavedSearchFilters {
  import io.circe.*
  import io.circe.generic.semiauto.*

  given Encoder[SearchTermFilter] = (filter: SearchTermFilter) => Json.fromString(filter.query.value)
  given Encoder[CityFilter] = (filter: CityFilter) => Json.fromString(filter.city.toString)
  given Encoder[CommuteDistance] = Encoder.encodeString.contramap(_.toString)
  given Encoder[GeoDistanceFilter] = deriveEncoder
  given Encoder[ProvinceFilter] = (filter: ProvinceFilter) =>
    Json.fromValues(filter.provinces.toList.map(province => Json.fromString(province.toString)))
  given Encoder[UpdateDateFilter] = (filter: UpdateDateFilter) => Json.fromString(filter.updateDate.toString)
  given Encoder[FunctionGroupFilter] = (filter: FunctionGroupFilter) =>
    Json.fromValues(filter.functionGroups.toList.map(functionGroup => Json.fromString(functionGroup.toString)))
  given Encoder[WorkLevelFilter] = (filter: WorkLevelFilter) =>
    Json.fromValues(filter.workLevels.toList.map(workLevel => Json.fromString(workLevel.toString)))
  given Encoder[WorkingHourFilter] = (filter: WorkingHourFilter) =>
    Json.fromValues(filter.workingHours.toList.map(workingHours => Json.fromString(WorkingHours.toEntryName(workingHours))))
  given Encoder[CareerLevelFilter] = (filter: CareerLevelFilter) =>
    Json.fromValues(filter.values.toList.map(careerLevel => Json.fromString(careerLevel.toString)))
  given Encoder[RequestedSalaryFilter] = (filter: RequestedSalaryFilter) =>
    Json.fromValues(filter.salaries.toList.map(requestedSalary => Json.fromString(requestedSalary.toString)))
  given Encoder[AvailabilityFilter] = (availabilityFilter: AvailabilityFilter) =>
    Json.fromValues(availabilityFilter.values.toList.map(availability => Json.fromString(availability.toString)))
  given Encoder[DriversLicenseFilter] = (filter: DriversLicenseFilter) =>
    Json.fromValues(filter.driversLicenses.toList.map(driversLicense => Json.fromString(driversLicense.toString)))
  given Encoder[LanguageFilter] = (filter: LanguageFilter) =>
    Json.fromValues(filter.languages.toList.map(language => Json.fromString(language.toString)))

  given Encoder[SavedSearchFilters] = deriveEncoder

  given Decoder[SearchTermFilter] =
    Decoder.decodeString.emap(str => SearchTerm.fromString(str).toRight("Invalid SearchTerm value")).map(SearchTermFilter(_))

  given Decoder[CityFilter] = Decoder.decodeString.map(str => CityFilter(City(str)))
  given Decoder[CommuteDistance] = Decoder.decodeString.map(str => CommuteDistance.fromString(str))
  given Decoder[GeoDistanceFilter] = deriveDecoder
  given Decoder[Province] = Decoder.decodeString.map(Province(_))
  given Decoder[ProvinceFilter] = Decoder.decodeSeq[Province].map { provinces =>
    NonEmptyList.fromList(provinces.toList).map(ProvinceFilter(_)).getOrElse(ProvinceFilter(NonEmptyList.one(Province("Unknown"))))
  }
  given Decoder[UpdateDateFilter] = Decoder.decodeString
    .emap(str => UpdateDate.fromString(str).toRight("Invalid UpdateDate value"))
    .map(UpdateDateFilter(_))
  given Decoder[FunctionGroup] = Decoder.decodeString.map(FunctionGroup(_))
  given Decoder[WorkLevel] = Decoder.decodeString.map(WorkLevel(_))
  given Decoder[WorkingHours] = Decoder.decodeString.emap(WorkingHours.fromString(_).toRight("Invalid WorkingHours value"))
  given Decoder[CareerLevel] = Decoder.decodeString.map(CareerLevel(_))
  given Decoder[RequestedSalary] = Decoder.decodeString.emap(RequestedSalary.fromString(_).toRight("Invalid RequestedSalary value"))
  given Decoder[Availability] = Decoder.decodeString.emap(Availability.fromString(_).toRight("Invalid Availability value"))
  given Decoder[DriversLicense] = Decoder.decodeString.map(DriversLicense(_))
  given Decoder[Language] = Decoder.decodeString.map(Language(_))

  given Decoder[SavedSearchFilters] = Decoder.instance(cursor =>
    for {
      searchTerm  <- cursor.downField("searchTerm").as[Option[SearchTermFilter]]
      city        <- cursor.downField("city").as[Option[CityFilter]]
      geoDistance <- cursor.downField("geoDistance").as[Option[GeoDistanceFilter]]
      provinces <- cursor.downField("provinces").as[Option[List[Province]]].flatMap {
        case Some(provinces) =>
          NonEmptyList
            .fromList(provinces)
            .map(ProvinceFilter(_))
            .asRight
        case None => Right(None)
      }
      updatedDate <- cursor.downField("updatedDate").as[Option[UpdateDateFilter]]
      functionGroups <- cursor.downField("functionGroups").as[Option[List[FunctionGroup]]].flatMap {
        case Some(functionGroups) =>
          NonEmptyList
            .fromList(functionGroups)
            .map(FunctionGroupFilter(_))
            .asRight
        case None => Right(None)
      }
      workLevels <- cursor.downField("workLevels").as[Option[List[WorkLevel]]].flatMap {
        case Some(workLevels) =>
          NonEmptyList
            .fromList(workLevels)
            .map(WorkLevelFilter(_))
            .asRight
        case None => Right(None)
      }
      workingHours <- cursor.downField("workingHours").as[Option[List[WorkingHours]]].flatMap {
        case Some(workingHours) =>
          NonEmptyList
            .fromList(workingHours)
            .map(WorkingHourFilter(_))
            .asRight
        case None => Right(None)
      }
      careerLevels <- cursor.downField("careerLevels").as[Option[List[CareerLevel]]].flatMap {
        case Some(careerLevels) =>
          NonEmptyList
            .fromList(careerLevels)
            .map(CareerLevelFilter(_))
            .asRight
        case None => Right(None)
      }
      requestedSalaries <- cursor.downField("requestedSalaries").as[Option[List[RequestedSalary]]].flatMap {
        case Some(requestedSalaries) =>
          NonEmptyList
            .fromList(requestedSalaries)
            .map(RequestedSalaryFilter(_))
            .asRight
        case None => Right(None)
      }
      availabilities <- cursor.downField("availabilities").as[Option[List[Availability]]].flatMap {
        case Some(availabilities) =>
          NonEmptyList
            .fromList(availabilities)
            .map(AvailabilityFilter(_))
            .asRight
        case None => Right(None)
      }
      driversLicenses <- cursor.downField("driversLicenses").as[Option[List[DriversLicense]]].flatMap {
        case Some(driversLicenses) =>
          NonEmptyList
            .fromList(driversLicenses)
            .map(DriversLicenseFilter(_))
            .asRight
        case None => Right(None)
      }
      languages <- cursor.downField("languages").as[Option[List[Language]]].flatMap {
        case Some(languages) =>
          NonEmptyList
            .fromList(languages)
            .map(LanguageFilter(_))
            .asRight
        case None => Right(None)
      }
    } yield SavedSearchFilters(
      searchTerm,
      city,
      geoDistance,
      provinces,
      updatedDate,
      functionGroups,
      workLevels,
      workingHours,
      careerLevels,
      requestedSalaries,
      availabilities,
      driversLicenses,
      languages
    )
  )
}
