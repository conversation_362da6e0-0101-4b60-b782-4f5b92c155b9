package nl.dpes.savedsearch.storage.dbmodel

enum Frequency {
  case Daily, Weekly, Never
}

object Frequency {
  def fromString(value: String): Option[Frequency] = value.toLowerCase match {
    case "dagelijks" => Some(Daily)
    case "wekelijks" => Some(Weekly)
    case "nooit"     => Some(Never)
    case _           => None
  }

  def toEntryName(frequency: Frequency): String = frequency match {
    case Daily  => "Dagelijks"
    case Weekly => "Wekelijks"
    case Never  => "Nooit"
  }

  given doobie.Meta[Frequency] =
    doobie.Meta[String].tiemap(string => fromString(string).toRight(s"Unknown value for frequency: $string"))(toEntryName)
}
