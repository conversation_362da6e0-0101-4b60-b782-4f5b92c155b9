package nl.dpes.savedsearch.storage.dbmodel

import nl.dpes.savedsearch.domain
import io.scalaland.chimney.Transformer

opaque type SavedSearchId = String

object SavedSearchId {
  def apply(value: String): SavedSearchId = value

  given doobie.Meta[SavedSearchId] = doobie.Meta.StringMeta

  given recruiterIdTransformer: Transformer[SavedSearchId, domain.SavedSearchId] =
    id => nl.dpes.savedsearch.domain.SavedSearchId(id)

  given domainSavedSearchIdTransformer: Transformer[domain.SavedSearchId, SavedSearchId] =
    id => SavedSearchId(id.value)
}
