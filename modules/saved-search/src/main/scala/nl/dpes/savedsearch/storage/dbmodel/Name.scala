package nl.dpes.savedsearch.storage.dbmodel

import io.scalaland.chimney.Transformer

opaque type Name = String

object Name:
  def apply(value: String): Name = value
  extension (name: Name) def value: String = name
  given doobie.Meta[Name] = doobie.Meta.StringMeta
  given nameTransformer: Transformer[Name, nl.dpes.savedsearch.domain.Name] =
    name => nl.dpes.savedsearch.domain.Name(name)
  given domainNameTransformer: Transformer[nl.dpes.savedsearch.domain.Name, Name] =
    name => Name(name.value)
