package nl.dpes.savedsearch.domain

import java.util.UUID

case class SavedSearch(id: UUID, name: Name, recruiterId: RecruiterId, frequency: Frequency, filters: SavedSearchFilters)

object SavedSearch {
  def from(id: UUID, name: String, recruiterId: String, frequency: Frequency, filters: SavedSearchFilters): SavedSearch =
    new SavedSearch(id, Name(name), RecruiterId(recruiterId), frequency, filters)
}
