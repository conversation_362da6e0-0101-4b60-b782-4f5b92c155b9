package nl.dpes.savedsearch.service

import cats.Monad
import cats.effect.Resource
import cats.implicits.*
import cats.effect.kernel.MonadCancelThrow
import nl.dpes.savedsearch.domain.*
import nl.dpes.savedsearch.service.SavedSearchService.SavedSearchServiceException
import org.typelevel.log4cats.LoggerFactoryGen

import java.util.UUID

trait SavedSearchService[F[_]] {
  def create(
    recruiterId: RecruiterId,
    name: Name,
    frequency: Frequency,
    filters: SavedSearchFilters
  ): F[SavedSearchId]

  def updateFrequency(
    recruiterId: RecruiterId,
    id: SavedSearchId,
    frequency: Frequency
  ): F[Unit]

  def getById(recruiterId: RecruiterId, savedSearchId: SavedSearchId): F[Option[SavedSearch]]

  def getAllForRecruiter(recruiterId: RecruiterId): F[List[SavedSearch]]

  def delete(recruiterId: RecruiterId, savedSearchId: SavedSearchId): F[Unit]
}

object SavedSearchService {
  sealed class SavedSearchServiceException(message: String) extends Exception(message)
  case class SavedSearchAlreadyExistsException(message: String) extends SavedSearchServiceException(message)
  case class SavedSearchNotFoundException(message: String) extends SavedSearchServiceException(message)
  case class UnknownException(message: String) extends SavedSearchServiceException(message)

  def resource[F[_]: {MonadCancelThrow, LoggerFactoryGen}](repository: SavedSearchRepository[F]): Resource[F, SavedSearchService[F]] =
    Resource.pure(new SavedSearchService[F] {
      override def create(
        recruiterId: RecruiterId,
        name: Name,
        frequency: Frequency,
        filters: SavedSearchFilters
      ): F[SavedSearchId] =
        for {
          existingSavedSearches <- repository.getAllForRecruiter(recruiterId)
          result <-
            if (existingSavedSearches.exists(_.filters == filters)) {
              MonadCancelThrow[F].raiseError(
                SavedSearchAlreadyExistsException(s"Saved search with filters $filters already exists for recruiter $recruiterId")
              )
            } else {
              repository.create(recruiterId, name, frequency, filters)
            }
        } yield result

      override def updateFrequency(recruiterId: RecruiterId, id: SavedSearchId, frequency: Frequency): F[Unit] =
        repository
          .updateFrequency(recruiterId, id, frequency)
          .adaptErr { case e =>
            SavedSearchService.UnknownException(s"Failed to update frequency for saved search $id: ${e.getMessage}")
          }
          .flatMap(updated =>
            if (!updated)
              MonadCancelThrow[F].raiseError(
                SavedSearchNotFoundException(s"Saved search with id $id not found for recruiter $recruiterId")
              )
            else
              Monad[F].unit
          )

      override def getAllForRecruiter(recruiterId: RecruiterId): F[List[SavedSearch]] =
        repository
          .getAllForRecruiter(recruiterId)
          .adaptErr { case e =>
            SavedSearchServiceException(s"Failed to retrieve saved searches for recruiter $recruiterId: ${e.getMessage}")
          }

      override def delete(recruiterId: RecruiterId, savedSearchId: SavedSearchId): F[Unit] =
        repository
          .delete(recruiterId, savedSearchId)
          .adaptErr { case e =>
            SavedSearchServiceException(s"Failed to delete saved search '$savedSearchId' for recruiter '$recruiterId': ${e.getMessage}")
          }

      override def getById(recruiterId: RecruiterId, savedSearchId: SavedSearchId): F[Option[SavedSearch]] =
        repository
          .getById(recruiterId, savedSearchId)
          .adaptErr { case e =>
            SavedSearchServiceException(s"Failed to retrieve saved search '$savedSearchId' for recruiter '$recruiterId': ${e.getMessage}")
          }
    })
}
