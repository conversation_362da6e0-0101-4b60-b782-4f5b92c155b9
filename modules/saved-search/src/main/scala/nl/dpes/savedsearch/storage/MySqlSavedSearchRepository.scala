package nl.dpes.savedsearch.storage

import cats.effect.*
import cats.effect.std.UUIDGen
import cats.implicits.*
import doobie.implicits.*
import doobie.Fragment
import doobie.util.transactor.Transactor
import io.circe.syntax.EncoderOps
import nl.dpes.savedsearch.config.SavedSearchConfig.TableName
import nl.dpes.savedsearch.domain.*
import nl.dpes.savedsearch.service.SavedSearchRepository
import io.scalaland.chimney.dsl.*
import dbmodel.SavedSearchFilters.given
import nl.dpes.savedsearch.domain
import org.typelevel.log4cats.LoggerFactory

import java.util.UUID

class MySqlSavedSearchRepository[F[_]: {MonadCancelThrow, UUIDGen, LoggerFactory}](tableName: TableName, xa: Transactor[F])
    extends SavedSearchRepository[F] {

  val savedSearchTable: Fragment = Fragment.const(tableName.value)

  override def initialize: F[Unit] =
    sql"""
       CREATE TABLE IF NOT EXISTS $savedSearchTable (
          id            VARCHAR(36)  NOT NULL,
          name          VARCHAR(1000) NOT NULL,
          recruiterId   VARCHAR(36)  NOT NULL,
          filters       JSON         NOT NULL,
          frequency     VARCHAR(36)  NOT NULL,
          PRIMARY KEY (id),
          INDEX (recruiterId)
       )
      """.update.run.transact(xa).void

  override def create(recruiterId: RecruiterId, name: Name, frequency: Frequency, filters: SavedSearchFilters): F[SavedSearchId] =
    for {
      id <- UUIDGen[F].randomUUID
      _ <-
        sql"""
      INSERT INTO $savedSearchTable (id, name, recruiterId, filters, frequency)
      VALUES (
        ${id.toString}, ${name.into[dbmodel.Name].transform}, ${recruiterId.into[dbmodel.RecruiterId].transform},
        ${filters.asJson.noSpaces}, ${frequency.into[dbmodel.Frequency].transform}
      )
    """.update.run.transact(xa)
    } yield SavedSearchId(id.toString)

  override def updateFrequency(recruiterId: RecruiterId, id: SavedSearchId, frequency: Frequency): F[Boolean] =
    sql"""
      UPDATE $savedSearchTable
      SET frequency = ${frequency.into[dbmodel.Frequency].transform}
      WHERE recruiterId = ${recruiterId.into[dbmodel.RecruiterId].transform} AND id = ${id.toString}
    """.update.run.transact(xa).map(_ > 0)

  override def getAllForRecruiter(recruiterId: RecruiterId): F[List[domain.SavedSearch]] =
    sql"""
        SELECT id, name, recruiterId, frequency, filters
        FROM $savedSearchTable
        WHERE recruiterId = ${recruiterId.into[dbmodel.RecruiterId].transform}
        """
      .query[dbmodel.SavedSearch]
      .to[List]
      .transact(xa)
      .map(_.map(transformSavedSearch))

  override def delete(recruiterId: RecruiterId, savedSearchId: SavedSearchId): F[Unit] =
    sql"""
        DELETE FROM $savedSearchTable
        WHERE id = ${savedSearchId.into[dbmodel.SavedSearchId].transform} AND recruiterId = ${recruiterId
        .into[dbmodel.RecruiterId]
        .transform}
        """.update.run.transact(xa).flatMap { affectedRows =>
      if (affectedRows == 0)
        LoggerFactory[F].getLogger.warn(s"No saved search '${savedSearchId.value}' was found for recruiter '${recruiterId.value}'")
      else
        MonadCancelThrow[F].unit
    }

  override def getAllByFrequency(frequency: Frequency): F[List[domain.SavedSearch]] =
    sql"""
            SELECT id, name, recruiterId, frequency, filters
            FROM $savedSearchTable
            WHERE frequency = ${frequency.into[dbmodel.Frequency].transform}
            """
      .query[dbmodel.SavedSearch]
      .to[List]
      .transact(xa)
      .map(_.map(transformSavedSearch))

  private def transformSavedSearch(dbSavedSearch: dbmodel.SavedSearch): domain.SavedSearch =
    domain.SavedSearch(
      id = UUID.fromString(dbSavedSearch.id),
      name = dbSavedSearch.name.into[domain.Name].transform,
      recruiterId = dbSavedSearch.recruiterId.into[domain.RecruiterId].transform,
      frequency = dbSavedSearch.frequency.into[domain.Frequency].transform,
      filters = dbSavedSearch.filters.into[domain.SavedSearchFilters].transform
    )

  override def getById(recruiterId: RecruiterId, savedSearchId: SavedSearchId): F[Option[SavedSearch]] =
    sql"""
        SELECT id, name, recruiterId, frequency, filters
        FROM $savedSearchTable
        WHERE id = ${savedSearchId.into[dbmodel.SavedSearchId].transform}
        AND recruiterId = ${recruiterId.into[dbmodel.RecruiterId].transform}
     """
      .query[dbmodel.SavedSearch]
      .option
      .transact(xa)
      .map(_.map(transformSavedSearch))
}

object MySqlSavedSearchRepository {
  def resource[F[_]: {MonadCancelThrow, UUIDGen, LoggerFactory}](
    tableName: TableName,
    transactor: Transactor[F]
  ): Resource[F, SavedSearchRepository[F]] =
    Resource.eval(for {
      repo <- new MySqlSavedSearchRepository[F](tableName, transactor).pure
      _    <- repo.initialize
    } yield repo)

}
