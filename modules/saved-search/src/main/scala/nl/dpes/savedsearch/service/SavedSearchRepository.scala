package nl.dpes.savedsearch.service

import nl.dpes.savedsearch.domain.*

trait SavedSearchRepository[F[_]] {
  def initialize: F[Unit]
  def create(recruiterId: RecruiterId, name: Name, frequency: Frequency, filters: SavedSearchFilters): F[SavedSearchId]
  def updateFrequency(recruiterId: RecruiterId, id: SavedSearchId, frequency: Frequency): F[Boolean]
  def getAllForRecruiter(recruiterId: RecruiterId): F[List[SavedSearch]]
  def delete(recruiterId: RecruiterId, savedSearchId: SavedSearchId): F[Unit]
  def getAllByFrequency(frequency: Frequency): F[List[SavedSearch]]
  def getById(recruiterId: RecruiterId, savedSearchId: SavedSearchId): F[Option[SavedSearch]]
}
