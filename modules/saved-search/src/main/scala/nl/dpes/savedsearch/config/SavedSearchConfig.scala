package nl.dpes.savedsearch.config

import doobie.Fragment
import nl.dpes.savedsearch.config.SavedSearchConfig.TableName
import pureconfig.ConfigReader

case class SavedSearchConfig(tableName: TableName) derives ConfigReader

object SavedSearchConfig {

  case class TableName(value: String)

  given ConfigReader[TableName] = ConfigReader[String].map(TableName.apply)

  given Conversion[TableName, Fragment] = tableName => Fragment.const(tableName.value)
}
