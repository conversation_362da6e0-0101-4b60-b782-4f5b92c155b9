package nl.dpes.savedsearch.storage

import cats.effect.{IO, Resource}
import doobie.implicits.*
import doobie.{Fragment, Get, Transactor}
import io.scalaland.chimney.dsl.into
import nl.dpes.profilesearch.domain.SearchTerm
import nl.dpes.profilesearch.domain.filter.SearchTermFilter
import nl.dpes.savedsearch.config.SavedSearchConfig.TableName
import nl.dpes.savedsearch.domain.{Frequency, Name, RecruiterId, SavedSearchFilters}
import nl.dpes.savedsearch.domain.Frequency.Daily
import nl.dpes.savedsearch.service.SavedSearchRepository
import nl.dpes.savedsearch.storage.MySqlSavedSearchRepository
import nl.dpes.savedsearch.storage.dbmodel.SavedSearchId
import nl.dpes.savedsearch.domain
import nl.dpes.savedsearch.testcontainers.MySqlDatabaseGenerator
import nl.dpes.savedsearch.testcontainers.MySqlDatabaseGenerator.WrappedTransactor
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.{GlobalRead, IOSuite}

import java.util.UUID

class MysqlSavedSearchRepositorySpec(global: GlobalRead) extends IOSuite with MySqlDatabaseGenerator {

  given LoggerFactory[IO] = Slf4jFactory.create[IO]

  override type Res = Transactor[IO]

  override def sharedResource: Resource[IO, Res] =
    global.getOrFailR[WrappedTransactor]().map(_.transactor)

  case class Repo(savedSearchRepo: SavedSearchRepository[IO], tableName: TableName)

  val recruiterId: RecruiterId = RecruiterId("recruiter-123")
  val savedSearchName: Name = Name("My Saved Search")

  def randomRepo(xa: Transactor[IO]): Resource[IO, Repo] = {
    val id = UUID.randomUUID().toString.replace("-", "")
    for {
      repo <- MySqlSavedSearchRepository.resource(TableName(s"saved_search_$id"), xa)
    } yield Repo(repo, TableName(s"saved_search_$id"))
  }

  test("It should be able to create the table in an idempotent way") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _       <- repo.savedSearchRepo.initialize.attempt
        result  <- repo.savedSearchRepo.initialize.attempt
        columns <- getColumnNames(Fragment.const(repo.tableName.value), xa)
      } yield expect(
        result == Right(()) &&
          columns.sorted == List(
            "filters",
            "frequency",
            "id",
            "name",
            "recruiterId"
          )
      )
    }
  }

  given Get[SavedSearchFilters] = Get[String].temap(str => io.circe.parser.decode[SavedSearchFilters](str).left.map(_.getMessage))

  private val filters = SavedSearchFilters(
    searchTerm = Some(SearchTermFilter(SearchTerm.unsafe("developer"))),
    city = None,
    geoDistance = None,
    provinces = None,
    updatedDate = None,
    functionGroups = None,
    workLevels = None,
    workingHours = None,
    careerLevels = None,
    requestedSalaries = None,
    availabilities = None,
    driversLicenses = None,
    languages = None
  )

  test("It should be able to create a saved search") { xa =>
    val recruiterId = RecruiterId("recruiter-123")
    val name = Name("My Saved Search")

    randomRepo(xa).use { repo =>
      for {
        _ <- repo.savedSearchRepo.initialize
        _ <- repo.savedSearchRepo.create(
          recruiterId = recruiterId,
          name = name,
          frequency = Frequency.Daily,
          filters = filters
        )
        savedSearches <- getSavedSearches(recruiterId, xa, repo)
      } yield expect.same(1, savedSearches.size) &&
        expect.same(name, savedSearches.head._1) &&
        expect.same(Daily, savedSearches.head._2) &&
        expect.same(
          filters,
          savedSearches.head._3
        )
    }
  }

  test("It should return all saved searches for a recruiter") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _ <- repo.savedSearchRepo.initialize
        _ <- repo.savedSearchRepo.create(
          recruiterId = recruiterId,
          name = savedSearchName,
          frequency = Frequency.Daily,
          filters = filters
        )
        _ <- repo.savedSearchRepo.create(
          recruiterId = recruiterId,
          name = Name("Another Search"),
          frequency = Frequency.Weekly,
          filters = filters
        )
        savedSearches <- repo.savedSearchRepo.getAllForRecruiter(recruiterId)
      } yield expect.same(2, savedSearches.size) &&
        expect(savedSearches.exists(_.name == savedSearchName)) &&
        expect(savedSearches.exists(_.frequency == Frequency.Daily)) &&
        expect(savedSearches.exists(_.filters == filters))
    }
  }

  test("It should be able to delete a saved search") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _             <- repo.savedSearchRepo.initialize
        savedSearchId <- repo.savedSearchRepo.create(recruiterId, savedSearchName, Frequency.Daily, filters)
        count         <- countSavedSearch(savedSearchId.value, recruiterId.value, xa, repo)
        _ <- repo.savedSearchRepo.delete(
          recruiterId,
          savedSearchId.into[domain.SavedSearchId].transform
        )
        _ <- repo.savedSearchRepo.delete(
          recruiterId,
          savedSearchId.into[domain.SavedSearchId].transform
        )
        noData <- countSavedSearch(savedSearchId.value, recruiterId.value, xa, repo)
      } yield expect(count == 1) and expect(noData == 0)
    }
  }

  test("It should be able to update the frequency of a saved search") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _ <- repo.savedSearchRepo.initialize
        savedSearchId <- repo.savedSearchRepo.create(
          recruiterId = recruiterId,
          name = savedSearchName,
          frequency = Frequency.Daily,
          filters = filters
        )
        _ <- repo.savedSearchRepo.updateFrequency(
          recruiterId,
          savedSearchId.into[domain.SavedSearchId].transform,
          Frequency.Weekly
        )
        savedSearches <- getSavedSearches(recruiterId, xa, repo)
      } yield expect.same(1, savedSearches.size) &&
        expect.same(Frequency.Weekly, savedSearches.head._2)
    }
  }

  test("It should not update the saved search if it does not belong to the recruiter") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _ <- repo.savedSearchRepo.initialize
        savedSearchId <- repo.savedSearchRepo.create(
          recruiterId = recruiterId,
          name = savedSearchName,
          frequency = Frequency.Daily,
          filters = filters
        )
        result <- repo.savedSearchRepo
          .updateFrequency(
            RecruiterId("another-recruiter"),
            savedSearchId.into[domain.SavedSearchId].transform,
            Frequency.Weekly
          )
          .attempt
      } yield expect.same(Right(false), result)
    }
  }

  test("whan a savessearch exists for a recruiter we can retrieve it by its id") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _ <- repo.savedSearchRepo.initialize
        savedSearchId <- repo.savedSearchRepo.create(
          recruiterId = recruiterId,
          name = savedSearchName,
          frequency = Frequency.Daily,
          filters = filters
        )
        retrievedSearch <- repo.savedSearchRepo.getById(
          recruiterId,
          savedSearchId.into[domain.SavedSearchId].transform
        )
      } yield retrievedSearch match {
        case Some(value) => expect(clue(value.id.toString) == clue(savedSearchId.into[domain.SavedSearchId].transform.toString))
        case None        => failure("Expected to retrieve a saved search but got None")
      }
    }
  }

  test("When a saved search exists for one recruiter but not for another it will not be retrieved for the other recruiter") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _ <- repo.savedSearchRepo.initialize
        savedSearchId <- repo.savedSearchRepo.create(
          recruiterId = recruiterId,
          name = savedSearchName,
          frequency = Frequency.Daily,
          filters = filters
        )
        retrievedSearch <- repo.savedSearchRepo.getById(
          RecruiterId("another-recruiter"),
          savedSearchId.into[domain.SavedSearchId].transform
        )
      } yield expect.same(retrievedSearch, None)
    }
  }

  test("When a saved search does not exist at all it cannot be retrieved") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _ <- repo.savedSearchRepo.initialize
        retrievedSearch <- repo.savedSearchRepo.getById(
          recruiterId,
          nl.dpes.savedsearch.domain.SavedSearchId("non-existing-id")
        )
      } yield expect.same(retrievedSearch, None)
    }
  }

  def getColumnNames(tableName: Fragment, xa: Transactor[IO]): IO[List[String]] =
    sql"""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = '$tableName'
       """
      .query[String]
      .to[List]
      .transact(xa)

  def getSavedSearches(recruiterId: RecruiterId, xa: Transactor[IO], repo: Repo): IO[List[(String, Frequency, SavedSearchFilters)]] =
    sql"""
        SELECT name, frequency, filters
        FROM ${Fragment.const(repo.tableName.value)}
        WHERE recruiterId = ${recruiterId.into[dbmodel.RecruiterId].transform}
       """
      .query[(String, dbmodel.Frequency, SavedSearchFilters)]
      .to[List]
      .transact(xa)
      .map(_.map { case (name, frequency, filters) =>
        (name, frequency.into[Frequency].transform, filters)
      })

  def countSavedSearch(id: String, recruiterId: String, xa: Transactor[IO], repo: Repo): IO[Int] =
    sql"""SELECT COUNT(*) 
            FROM ${Fragment.const(repo.tableName.value)}
            WHERE id = $id AND recruiterId = $recruiterId
            """
      .query[Int]
      .unique
      .transact(xa)
}
