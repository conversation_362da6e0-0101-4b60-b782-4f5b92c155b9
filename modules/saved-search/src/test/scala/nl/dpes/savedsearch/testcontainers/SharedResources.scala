package nl.dpes.savedsearch.testcontainers

import cats.effect.{IO, Resource}
import nl.dpes.savedsearch.testcontainers.MySqlDatabaseGenerator.WrappedTransactor
import weaver.{GlobalResource, GlobalWrite}

object SharedResources extends GlobalResource with MySqlDatabaseGenerator {
  def sharedResources(global: GlobalWrite): Resource[IO, Unit] = for {
    db <- transactor
    _  <- global.putR(WrappedTransactor(db))
  } yield ()
}
