package nl.dpes.savedsearch.storage.dbmodel

import nl.dpes.savedsearch.storage.dbmodel.Frequency
import weaver.FunSuite

object FrequencySpec extends FunSuite {
  test("Frequency should be parsed from string") {
    expect.same(Some(Frequency.Daily), Frequency.fromString("Da<PERSON>ijk<PERSON>"))
    expect.same(Some(Frequency.Weekly), Frequency.fromString("Wekelijks"))
    expect.same(Some(Frequency.Never), Frequency.fromString("Nooit"))
    expect.same(None, Frequency.fromString("unknown"))
  }

  test("Frequency should convert to entry name") {
    expect.same("Dagelijk<PERSON>", Frequency.toEntryName(Frequency.Daily))
    expect.same("Wekelijks", Frequency.toEntryName(Frequency.Weekly))
    expect.same("Nooit", Frequency.toEntryName(Frequency.Never))
  }
}
