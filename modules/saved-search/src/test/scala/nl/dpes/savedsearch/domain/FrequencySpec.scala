package nl.dpes.savedsearch.domain

import weaver.FunSuite

object FrequencySpec extends FunSuite {
  test("It should be possible to parse frequencies from strings") {
    expect.same(Right(Frequency.Daily), Frequency.fromString("daily")) &&
    expect.same(Right(Frequency.Weekly), Frequency.fromString("weekly")) &&
    expect.same(Right(Frequency.Never), Frequency.fromString("never")) &&
    expect.same(Right(Frequency.Daily), Frequency.fromString("DAILY")) &&
    expect.same(Right(Frequency.Weekly), Frequency.fromString("WEEKLY")) &&
    expect.same(Right(Frequency.Never), Frequency.fromString("NEVER"))
  }

  test("It should return an error when parsing an invalid frequency") {
    expect.same(
      Left(new Exception("Invalid frequency: monthly").getMessage),
      Frequency.fromString("monthly").left.map(_.getMessage)
    ) &&
    expect.same(
      Left(new Exception("Invalid frequency: ").getMessage),
      Frequency.fromString("").left.map(_.getMessage)
    )
  }
}
