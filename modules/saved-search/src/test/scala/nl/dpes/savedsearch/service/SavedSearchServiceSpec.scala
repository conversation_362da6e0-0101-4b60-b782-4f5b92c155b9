package nl.dpes.savedsearch.service

import weaver.{FunSuiteIO, SimpleIOSuite}
import cats.effect.IO
import nl.dpes.savedsearch.domain.*
import nl.dpes.savedsearch.service.SavedSearchService
import org.scalamock.stubs.Stubs
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory

import java.util.UUID

object SavedSearchServiceSpec extends SimpleIOSuite with Stubs {
  private given LoggerFactory[IO] = Slf4jFactory.create[IO]

  val unit: Unit = ()

  test("It should be possible to create a saved search") {
    val recruiterId = RecruiterId("recruiter-123")
    val name = Name("My Saved Search")
    val frequency = Frequency.Daily
    val filters = SavedSearchFilters(None, None, None, None, None, None, None, None, None, None, None, None, None)

    val repository = stub[SavedSearchRepository[IO]]
    val id = SavedSearchId("123e4567-e89b-12d3-a456-************")
    repository.create.returns(_ => IO.pure(id))
    repository.getAllForRecruiter.returns(_ => IO.pure(List.empty))

    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.create(recruiterId, name, frequency, filters)
      } yield expect.same(id, result)
    }
  }

  test("It should not allow creating a saved search with duplicate filters for the same recruiter") {
    val recruiterId = RecruiterId("recruiter-123")
    val name = Name("My Saved Search")
    val frequency = Frequency.Daily
    val filters = SavedSearchFilters(None, None, None, None, None, None, None, None, None, None, None, None, None)

    val repository = stub[SavedSearchRepository[IO]]
    repository.create.returns(_ => IO.pure(SavedSearchId("123e4567-e89b-12d3-a456-************")))
    repository.getAllForRecruiter.returns(_ =>
      IO.pure(List(SavedSearch(UUID.fromString("5c809f22-7374-11f0-abdf-df459060ebe2"), name, recruiterId, frequency, filters)))
    )

    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.create(recruiterId, name, frequency, filters).attempt
      } yield expect(
        result.isLeft && result.left.get.isInstanceOf[SavedSearchService.SavedSearchAlreadyExistsException],
        result.toString
      )
    }
  }

  test("It should retrieve all saved searches for a recruiter") {
    val recruiterId = RecruiterId("recruiter-123")
    val filters = SavedSearchFilters(None, None, None, None, None, None, None, None, None, None, None, None, None)

    val repository = stub[SavedSearchRepository[IO]]
    val savedSearches = List(
      SavedSearch(UUID.fromString("123e4567-e89b-12d3-a456-************"), Name("Search 1"), recruiterId, Frequency.Daily, filters),
      SavedSearch(UUID.fromString("123e4567-e89b-12d3-a456-************"), Name("Search 2"), recruiterId, Frequency.Weekly, filters)
    )
    repository.getAllForRecruiter.returns(_ => IO.pure(savedSearches))

    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.getAllForRecruiter(recruiterId)
      } yield expect(
        result == List(
          SavedSearch(UUID.fromString("123e4567-e89b-12d3-a456-************"), Name("Search 1"), recruiterId, Frequency.Daily, filters),
          SavedSearch(UUID.fromString("123e4567-e89b-12d3-a456-************"), Name("Search 2"), recruiterId, Frequency.Weekly, filters)
        )
      )
    }
  }

  test("It should return an empty list if no saved searches exist for a recruiter") {
    val recruiterId = RecruiterId("recruiter-123")
    val repository = stub[SavedSearchRepository[IO]]
    repository.getAllForRecruiter.returns(_ => IO.pure(List.empty))

    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.getAllForRecruiter(recruiterId)
      } yield expect(result.isEmpty)
    }
  }

  test("Errors will be wrapped in a SavedSearchServiceException") {
    val recruiterId = RecruiterId("recruiter-123")
    val repository = stub[SavedSearchRepository[IO]]
    repository.getAllForRecruiter.returns(_ => IO.raiseError(new RuntimeException("Database error")))
    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.getAllForRecruiter(recruiterId).attempt
      } yield result match {
        case Left(value) =>
          expect(value.isInstanceOf[SavedSearchService.SavedSearchServiceException]) &&
          expect(clue(value.getMessage) == "Failed to retrieve saved searches for recruiter recruiter-123: Database error")
        case Right(value) => failure(s"Expected an error but got $value")
      }
    }
  }

  test("It should update the frequency of a saved search") {
    val recruiterId = RecruiterId("recruiter-123")
    val id = SavedSearchId("123e4567-e89b-12d3-a456-************")
    val frequency = Frequency.Weekly

    val repository = stub[SavedSearchRepository[IO]]
    repository.updateFrequency.returns((_, _, _) => IO.pure(true))

    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.updateFrequency(recruiterId, id, frequency)
      } yield expect(result == ())
    }
  }

  test("It should raise an error if the saved search to update does not exist") {
    val recruiterId = RecruiterId("recruiter-123")
    val id = SavedSearchId("123e4567-e89b-12d3-a456-************")
    val frequency = Frequency.Weekly

    val repository = stub[SavedSearchRepository[IO]]
    repository.updateFrequency.returns((_, _, _) => IO.pure(false))

    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.updateFrequency(recruiterId, id, frequency).attempt
      } yield expect.same(
        Left(SavedSearchService.SavedSearchNotFoundException(s"Saved search with id $id not found for recruiter $recruiterId")),
        result
      )
    }
  }

  test("It should handle errors when updating frequency") {
    val recruiterId = RecruiterId("recruiter-123")
    val id = SavedSearchId("123e4567-e89b-12d3-a456-************")
    val frequency = Frequency.Weekly

    val repository = stub[SavedSearchRepository[IO]]
    repository.updateFrequency.returns((_, _, _) => IO.raiseError(Exception("Update error")))

    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.updateFrequency(recruiterId, id, frequency).attempt
      } yield expect.same(
        Left(SavedSearchService.UnknownException(s"Failed to update frequency for saved search $id: Update error")),
        result
      )
    }
  }

  test("It should be able to delete a saved search") {
    val recruiterId = RecruiterId("recruiter-123")

    val repository = stub[SavedSearchRepository[IO]]
    val id = SavedSearchId("123e4567-e89b-12d3-a456-************")
    repository.delete.returns(_ => IO.unit)

    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.delete(recruiterId, id)
      } yield expect(result == unit)
    }
  }

  test("It should return an error when unable to delete a saved search") {
    val recruiterId = RecruiterId("recruiter-123")
    val id = SavedSearchId("123e4567-e89b-12d3-a456-************")

    val repository = stub[SavedSearchRepository[IO]]
    repository.delete.returns(_ => IO.raiseError(Exception("Unable to delete")))

    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.delete(recruiterId, id).attempt
      } yield result match {
        case Left(value) =>
          expect(value.isInstanceOf[SavedSearchService.SavedSearchServiceException]) &&
          expect(
            clue(value.getMessage) == s"Failed to delete saved search '${id.value}' for recruiter '${recruiterId.value}': Unable to delete"
          )
        case Right(value) => failure(s"Expected an error but got $value")
      }
    }
  }

  test("It should retrieve a saved search by its ID when the repository finds one") {
    val recruiterId = RecruiterId("recruiter-123")
    val id = SavedSearchId("123e4567-e89b-12d3-a456-************")
    val filters = SavedSearchFilters(None, None, None, None, None, None, None, None, None, None, None, None, None)

    val repository = stub[SavedSearchRepository[IO]]
    val savedSearch = SavedSearch(UUID.fromString(id.value), Name("My Search"), recruiterId, Frequency.Daily, filters)
    repository.getById.returns((_, _) => IO.pure(Some(savedSearch)))

    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.getById(recruiterId, id)
      } yield expect.same(Some(savedSearch), result)
    }
  }

  test("It should return None when trying to retrieve a saved search by ID that does not exist for the recruiter") {
    val recruiterId = RecruiterId("recruiter-123")
    val id = SavedSearchId("123e4567-e89b-12d3-a456-************")

    val repository = stub[SavedSearchRepository[IO]]
    repository.getById.returns((_, _) => IO.pure(None))

    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.getById(recruiterId, id)
      } yield expect.same(None, result)
    }
  }

  test("When the repository returns any error it will be wrapped in a SavedSearchServiceException") {
    val recruiterId = RecruiterId("recruiter-123")
    val id = SavedSearchId("123e4567-e89b-12d3-a456-************")

    val repository = stub[SavedSearchRepository[IO]]
    repository.getById.returns((_, _) => IO.raiseError(new RuntimeException("Database error")))

    SavedSearchService.resource[IO](repository).use { service =>
      for {
        result <- service.getById(recruiterId, id).attempt
      } yield result match {
        case Left(value) =>
          expect(value.isInstanceOf[SavedSearchService.SavedSearchServiceException]) &&
          expect(clue(value.getMessage) == s"Failed to retrieve saved search '$id' for recruiter '${recruiterId.value}': Database error")
        case Right(value) => failure(s"Expected an error but got $value")
      }
    }
  }
}
