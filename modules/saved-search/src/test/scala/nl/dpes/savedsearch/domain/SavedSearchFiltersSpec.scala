package nl.dpes.savedsearch.domain

import cats.data.NonEmptyList
import io.circe.parser.decode
import io.circe.literal.json
import io.circe.syntax.EncoderOps
import weaver.FunSuite
import nl.dpes.profilesearch.domain.*
import nl.dpes.profilesearch.domain.filter.*

object SavedSearchFiltersSpec extends FunSuite {
  test("Saved search filters should be encoded and decoded correctly") {
    val filters = SavedSearchFilters(
      searchTerm = Some(SearchTermFilter(SearchTerm.unsafe("developer"))),
      city = Some(CityFilter(City("Amsterdam"))),
      geoDistance = Some(GeoDistanceFilter(10, 20, None)),
      provinces = Some(ProvinceFilter(NonEmptyList.of(Province("North Holland")))),
      updatedDate = Some(UpdateDateFilter(UpdateDate.All)),
      functionGroups = Some(FunctionGroupFilter(NonEmptyList.of(FunctionGroup("Engineering")))),
      workLevels = Some(WorkLevelFilter(NonEmptyList.of(WorkLevel("Full-time")))),
      workingHours = Some(WorkingHourFilter(NonEmptyList.of(WorkingHours.From32To40))),
      careerLevels = Some(CareerLevelFilter(NonEmptyList.of(CareerLevel("Senior")))),
      requestedSalaries = Some(RequestedSalaryFilter(NonEmptyList.of(RequestedSalary.MoreThan7000))),
      availabilities = Some(AvailabilityFilter(NonEmptyList.of(Availability.PerDirect))),
      driversLicenses = Some(DriversLicenseFilter(NonEmptyList.of(DriversLicense("A")))),
      languages = Some(LanguageFilter(NonEmptyList.of(Language("English"))))
    )

    val expectedJson =
      json"""
            {
              "searchTerm":"developer",
              "city":"Amsterdam",
              "geoDistance":{"latitude":10.0,"longitude":20.0},
              "provinces":["North Holland"],
              "updatedDate":"Alles",
              "functionGroups":["Engineering"],
              "workLevels":["Full-time"],
              "workingHours":["32 tot en met 40 uur"],
              "careerLevels":["Senior"],
              "requestedSalaries":["> 7.000"],
              "availabilities":["Per direct"],
              "driversLicenses":["A"],
              "languages":["English"]
            }
      """.noSpaces

    decode[SavedSearchFilters](expectedJson) match {
      case Left(value)  => failure("Decoding failed: " + value.getMessage)
      case Right(value) => expect(value == filters)
    }
  }

  test("Nullable saved search filters should be encoded correctly") {
    val filters = SavedSearchFilters(
      searchTerm = None,
      city = None,
      geoDistance = None,
      provinces = None,
      updatedDate = None,
      functionGroups = None,
      workLevels = None,
      workingHours = None,
      careerLevels = None,
      requestedSalaries = None,
      availabilities = None,
      driversLicenses = None,
      languages = None
    )

    val expectedJson =
      json"""
                {
                  "searchTerm":null,
                  "city":null,
                  "geoDistance":null,
                  "provinces":null,
                  "updatedDate":null,
                  "functionGroups":null,
                  "workLevels":null,
                  "workingHours":null,
                  "careerLevels":null,
                  "requestedSalaries":null,
                  "availabilities":null,
                  "driversLicenses":null,
                  "languages":null
                }
          """.noSpaces

    expect.same(expectedJson, filters.asJson.noSpaces)
  }

  test("Nullable (or empty) saved search filters should be decoded correctly") {
    val jsonString =
      json"""
        {
          "searchTerm": null,
          "city": null,
          "geoDistance": null,
          "provinces": null,
          "updatedDate": null,
          "functionGroups": null,
          "workLevels": null,
          "workingHours": null,
          "careerLevels": null,
          "requestedSalaries": null,
          "availabilities": null,
          "driversLicenses": null,
          "languages": null
        }
      """

    val jsonString2 =
      json"""
        {
          "searchTerm": null,
          "city": null,
          "geoDistance": null,
          "provinces": [],
          "updatedDate": null,
          "functionGroups": [],
          "workLevels": [],
          "workingHours": [],
          "careerLevels": [],
          "requestedSalaries": [],
          "availabilities": [],
          "driversLicenses": [],
          "languages": []
        }
      """

    val jsonString3 = json"""{}"""

    val expectedFilters = SavedSearchFilters(
      searchTerm = None,
      city = None,
      geoDistance = None,
      provinces = None,
      updatedDate = None,
      functionGroups = None,
      workLevels = None,
      workingHours = None,
      careerLevels = None,
      requestedSalaries = None,
      availabilities = None,
      driversLicenses = None,
      languages = None
    )

    expect.same(decode[SavedSearchFilters](jsonString.asJson.noSpaces), Right(expectedFilters))
    expect.same(decode[SavedSearchFilters](jsonString2.asJson.noSpaces), Right(expectedFilters))
    expect.same(decode[SavedSearchFilters](jsonString3.asJson.noSpaces), Right(expectedFilters))
  }
}
