{"settings": {"analysis": {"normalizer": {"lowercase_normalizer": {"type": "custom", "filter": ["lowercase"]}}}, "number_of_shards": 8, "number_of_replicas": 2}, "mappings": {"dynamic": "strict", "properties": {"_meta": {"properties": {"timestamp": {"type": "date"}}}, "attachmentContents": {"properties": {"content": {"type": "text"}, "id": {"type": "keyword"}}}, "attachments": {"properties": {"extension": {"type": "keyword"}, "fileName": {"type": "keyword"}, "id": {"type": "keyword"}, "isCv": {"type": "boolean"}, "name": {"type": "keyword"}, "uploadDate": {"type": "long"}}}, "availability": {"type": "keyword", "index": false}, "careerLevel": {"type": "keyword", "index": false}, "commute": {"properties": {"city": {"type": "keyword", "normalizer": "lowercase_normalizer"}, "cityName": {"type": "keyword"}, "geoPoint": {"type": "geo_point"}, "maxTravelDistance": {"type": "long"}, "province": {"type": "keyword", "normalizer": "lowercase_normalizer", "fields": {"raw": {"type": "keyword"}}}, "zipCode": {"type": "keyword", "normalizer": "lowercase_normalizer"}}}, "contractType": {"type": "keyword"}, "country": {"type": "keyword"}, "driverLicenses": {"properties": {"group": {"type": "keyword"}, "type": {"type": "keyword", "index": false}}}, "education": {"properties": {"city": {"type": "keyword"}, "description": {"type": "text"}, "diploma": {"type": "boolean"}, "educationId": {"type": "keyword"}, "fieldOfStudy": {"type": "text"}, "fromDate": {"type": "keyword"}, "grade": {"type": "keyword"}, "gradeRank": {"type": "long"}, "school": {"type": "keyword", "fields": {"raw": {"type": "text"}}}, "toDate": {"type": "keyword"}}}, "emailAddress": {"type": "keyword"}, "experiences": {"properties": {"city": {"type": "keyword", "normalizer": "lowercase_normalizer"}, "companyName": {"type": "keyword"}, "description": {"type": "text"}, "experienceId": {"type": "keyword"}, "fromDate": {"type": "keyword", "index": false}, "jobTitle": {"type": "text", "fields": {"raw": {"type": "text"}}, "analyzer": "whitespace"}, "toDate": {"type": "keyword", "index": false}}}, "expose": {"type": "boolean"}, "findable": {"type": "boolean"}, "firstName": {"type": "keyword"}, "functionGroups": {"type": "keyword", "fields": {"raw": {"type": "text"}}}, "hiddenSensitiveFields": {"type": "nested", "properties": {"emailAddress": {"type": "boolean", "index": false}, "firstName": {"type": "boolean", "index": false}, "lastName": {"type": "boolean", "index": false}, "phoneNumber": {"type": "boolean", "index": false}}}, "hobbies": {"type": "keyword"}, "id": {"type": "keyword", "index": false}, "introductionText": {"type": "text"}, "languages": {"properties": {"isNative": {"type": "boolean"}, "isoCode": {"type": "keyword"}, "name": {"type": "keyword", "index": false}}}, "lastName": {"type": "keyword"}, "maxWorkingHours": {"type": "keyword", "index": false}, "minWorkingHours": {"type": "keyword", "index": false}, "phoneNumber": {"type": "keyword"}, "photo": {"type": "keyword"}, "preferredJobs": {"type": "keyword", "fields": {"raw": {"type": "text"}}}, "requestedSalary": {"type": "keyword", "index": false}, "site": {"type": "keyword", "index": false}, "training": {"properties": {"description": {"type": "text"}, "institute": {"type": "keyword"}, "month": {"type": "keyword"}, "name": {"type": "keyword"}, "trainingId": {"type": "keyword"}, "year": {"type": "keyword"}}}, "trainings": {"type": "nested", "properties": {"description": {"type": "text"}, "institute": {"type": "keyword"}, "month": {"type": "keyword", "index": false}, "name": {"type": "keyword"}, "year": {"type": "keyword", "index": false}}}, "unapprovedEducationIds": {"type": "keyword"}, "unapprovedExperienceIds": {"type": "keyword"}, "updatedDate": {"type": "long"}, "workLevels": {"type": "keyword", "normalizer": "lowercase_normalizer", "fields": {"raw": {"type": "keyword"}}}, "workingHours": {"type": "keyword"}}}}