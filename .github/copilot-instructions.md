# Copilot Instructions for this Repository

Purpose: equip AI coding agents to be productive immediately in the Profile Service mono-repo.

## Architecture at a Glance
- Multi-module Scala 3 project (SBT) under `modules/`:
  - `profile-service` (HTTP service, entrypoint `nl.dpes.profileservice.Main`) exposes Profile Search + Favorites + Saved Search APIs via Tapir + http4s. Swagger at `/docs/profileservice/v1`.
  - `profile-search` domain logic and integrations (Elasticsearch client via Elastic4s; DB via Doobie; locking, viewed/visited/unlocked profiles services; configs in `nl.dpes.profilesearch.config`).
  - `recruiter-favorites` module (MySQL-backed favorites repo and service).
  - `saved-search` module (MySQL-backed saved search repo and service).
  - `saved-search-sender` (separate CLI/cron app, entrypoint `nl.dpes.savedsearchsender.Main`) sends saved-search emails; integrates with Salesforce Gateway (gRPC) and Mail Service (HTTP).
- Shared config model:
  - PureConfig readers in each module; extension `ConfigSource.loadF` defined in `modules/profile-search/src/main/scala/nl/dpes/profilesearch/config/Syntax.scala` for effectful loading.
  - `profile-service` runtime config in `modules/profile-service/src/main/resources/application.conf` (env-var overrides for hostnames, ports, indices, DB creds, etc.).
- Data stores and integration points:
  - Elasticsearch 8.x (index `cv_database`) for search.
  - MySQL for visited/viewed/unlocked/favorites/saved_search tables.
  - External: Credit Service HTTP, Salesforce Gateway gRPC, Mail Service HTTP.

## Local Development
- Prereqs: Java 21 runtime in Docker images, Java 11 for local dev; SBT 1.8.x; Scala 3.7.0; Docker; AWS CLI v1 configured (`~/.aws/{config,credentials}`).
- Start stack and service:
  - Authenticate to ECR and run compose:
    - `aws ecr get-login-password | docker login  -u AWS --password-stdin https://674201978047.dkr.ecr.eu-west-1.amazonaws.com`
    - `docker-compose up -d`
  - Service: http://localhost:14200; Swagger: http://0.0.0.0:14200/docs/profileservice/v1
  - If Elasticsearch VM map count error: `sudo sysctl -w vm.max_map_count=262144`.
- Build/test:
  - All tests: `sbt test`
  - Coverage report: `sbt coverageReport`
  - Single suite: `sbt 'testOnly <FullyQualifiedSpec>'`
  - With shared testcontainers resources: `sbt 'testOnly <Spec> nl.dpes.profilesearch.testcontainers.SharedResources'`
- Dev loop and pre-push checks: `sbt prepare-commit` (formats, compiles, scapegoat, coverage, sonar, dependency updates).
- Rebuild local Docker image and refresh compose:
  - `sbt docker:publishLocal`
  - `docker-compose stop profile-service && docker-compose up -d`
  - Full reset (incl. ES): `docker compose stop && docker compose rm && sbt docker:publishLocal && docker-compose up -d`
- Debugging: remote JVM debug on container `5005`.

## Run Targets and Entrypoints
- `profile-service`: `Compile / packageBin / mainClass := Some("nl.dpes.profileservice.Main")` (HTTP server via Blaze).
- `saved-search-sender`: `Main` is a one-shot `IOApp` meant for cron; docker image name `profile-service-saved-search-sender`.

## Deployment
- CI: Jenkins (`Jenkinsfile`) runs format check, scapegoat, coverage, sonar, then builds Docker images with tag `${BUILD_NUMBER}` and pushes to ECR, then runs `sbt "deploy <acc|pro> ${BUILD_NUMBER}"`.
- K8s: Kustomize manifests in `deployment/kustomize/**`. Image digests are templated with `{{BUILD_NUMBER}}`. `deploy.sbt` resolves the kubectl context and applies manifests; rollback via `sbt "rollback <acc|pro>"`.

## Project Conventions and Patterns
- HTTP API: Tapir endpoints compiled to http4s routes. See controllers under `modules/profile-service/src/main/scala/nl/dpes/profileservice/{search,favorites,savedsearch}` and `StatusController` for health.
- Services as `Resource[F, _]`; wiring done in `Main.scala`. Prefer `Resource`-based construction and `given LoggerFactory[F] = Slf4jFactory.create[F]` for logging.
- Config loading: prefer `ConfigSource.default.loadF[F, AppConfig]` and case classes deriving `ConfigReader`.
- Elasticsearch: use `ElasticClient(JavaClient(ElasticProperties(Seq(ElasticNodeEndpoint(...)))))`; index name comes from config.
- Database: Doobie `Transactor` from `Database.resource`; MySQL connector `com.mysql:mysql-connector-j`. Use repositories per table, configured via table-name in `application.conf`.
- Testing: Weaver test framework; Testcontainers for MySQL and ES; shared global resources via `SharedResources` objects per module.

## Useful Paths and Examples
- App config: `modules/profile-service/src/main/resources/application.conf` (env vars like `DATABASE_USER`, `ELASTICSEARCH_CLUSTER_HOST`, `CREDIT_SERVICE_SERVICE_HOST`).
- Entry points: `modules/profile-service/.../Main.scala`, `modules/saved-search-sender/.../Main.scala`.
- Dependencies versions centralized in `project/*Dependencies.scala`.
- Custom SBT commands: `project/Commands.scala` adds `prepare-commit`.

## Tips for AI Agents
- When adding new endpoints, define Tapir endpoints + http4s routes co-located with the feature, and register them in `createRoutes` in `Main.scala`.
- Keep new resources as `Resource` and inject via `Main` wiring; surface configuration in `application.conf` with PureConfig readers.
- For tests touching DB/ES, reuse `SharedResources` and Testcontainers helpers under `modules/*/src/test/.../testcontainers`.
