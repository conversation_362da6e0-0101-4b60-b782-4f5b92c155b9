## Copilot Instructions (Profile Service)
Purpose: give AI agents the minimum, concrete context to be productive in this Scala 3 SBT mono-repo.

Architecture (what lives where)
- `modules/profile-service`: HTTP API (http4s + Tapir). Entrypoint `nl.dpes.profileservice.Main`. Swagger: `/docs/profileservice/v1`.
- `modules/profile-search`: core search domain (Elastic4s + <PERSON>bie), view/visit/unlock logic, locks, config helpers (`config/Syntax.scala`).
- `modules/recruiter-favorites`: MySQL-backed favorites repo/service.
- `modules/saved-search`: MySQL-backed saved search repo/service.
- `modules/saved-search-sender`: one-shot `IOApp` (cron) emailing saved searches via Salesforce gRPC + Mail HTTP.

How it fits together
- `profile-service` wires services from other modules, exposing endpoints for search, favorites, saved-search.
- Persistence: MySQL tables (visited/viewed/unlocked/favorites/saved_search). Search: Elasticsearch index `cv_database`.
- External deps: Credit Service (HTTP), Salesforce Gateway (gRPC), Mail Service (HTTP).

Local dev & debug
- Prereqs: Java 11, SBT 1.8.x, Scala 3.7.0, <PERSON><PERSON>, AWS CLI v1 configured.
- Start stack:
  - `aws ecr get-login-password | docker login -u AWS --password-stdin https://674201978047.dkr.ecr.eu-west-1.amazonaws.com`
  - `docker-compose up -d`
- App: `http://localhost:14200` (Swagger at `/docs/profileservice/v1`). Debug port: `5005`.
- If ES vm.max_map_count error: `sudo sysctl -w vm.max_map_count=262144`.

Build, test, package
- All tests: `sbt test`; coverage: `sbt coverageReport`.
- Single suite: `sbt 'testOnly <Spec>'`; with shared resources: `sbt 'testOnly <Spec> nl.dpes.profilesearch.testcontainers.SharedResources'`.
- Pre-push pipeline locally: `sbt prepare-commit` (fmt, compile, scapegoat, tests, coverage, sonar, deps).
- Rebuild Docker + refresh compose: `sbt docker:publishLocal && docker-compose stop profile-service && docker-compose up -d`.

Conventions & patterns
- Wiring: `Resource[F, _]` everywhere; wire in `modules/profile-service/.../Main.scala`.
- Config: `ConfigSource.default.loadF` from `config/Syntax.scala`; override via env in `application.conf`.
- HTTP: Define Tapir endpoints + http4s routes per feature; register in `Main.createRoutes`.
- Data: use Doobie `Transactor` from `Database.resource`; Elastic client via Elastic4s Java client.
- Tests: Weaver + Testcontainers; reuse `.../testcontainers/SharedResources.scala`.

Deploy (CI/CD)
- Jenkins builds images tagged `${BUILD_NUMBER}`, pushes to ECR, then `sbt "deploy <acc|pro> ${BUILD_NUMBER}"`.
- Manifests: `deployment/kustomize/**` with `{{BUILD_NUMBER}}`; rollback: `sbt "rollback <acc|pro>"`.

Pointers (files you’ll touch)
- Config: `modules/profile-service/src/main/resources/application.conf`.
- Entry points: `modules/profile-service/.../Main.scala`, `modules/saved-search-sender/.../Main.scala`.
- Versions/deps: `project/*Dependencies.scala`; custom SBT cmd: `project/Commands.scala`.
