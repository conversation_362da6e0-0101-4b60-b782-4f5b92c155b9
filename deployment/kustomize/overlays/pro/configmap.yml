kind: ConfigMap
apiVersion: v1
metadata:
  name: profile-service-config
  namespace: default
  labels:
    environment: production
data:
  LOGGING_LEVEL: "INFO"
  LOGGING_APPENDER: "JSON"
  ELASTICSEARCH_HOST_SCHEME: "http"
  ELAST<PERSON><PERSON>ARCH_CLUSTER_HOST: "es-job-service-v8-pro.persgroep.digital"
  ELASTICSEARCH_CLUSTER_PORT: "9200"
  ELASTICSEARCH_CV_DATABASE_INDEX: "cv_database"
  DATABASE_CONNECTION_STRING: "****************************************************************************************************************************************************************************"
  VISITED_PROFILES_VIEW_LIMIT: "250"
  UNLOCKED_PROFILES_ACCESS_DURATION_IN_DAYS: "30"
  VIEWED_PROFILES_RETENTION_THRESHOLD_IN_DAYS: "365"
  B2B_SALESFORCE_GATEWAY_SERVICE_HOST: "b2b-salesforce-gateway"
  MAIL_SERVICE_BASE_URL: "http://mail-service-pro.persgroep.digital"