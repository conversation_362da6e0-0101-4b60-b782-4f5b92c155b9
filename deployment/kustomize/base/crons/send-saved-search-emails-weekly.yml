kind: CronJob
apiVersion: batch/v1
metadata:
  name: "profile-service-saved-search-sender-weekly"
  namespace: default
spec:
  schedule: "13 6 * * 1"
  suspend: true
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 5
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      activeDeadlineSeconds: 18000
      backoffLimit: 0
      template:
        spec:
          restartPolicy: Never
          tolerations:
            - effect: NoSchedule
              key: dedicated
              operator: Equal
              value: dpgrcron
          containers:
            - name: "profile-service-saved-search-sender-weekly"
              image: 674201978047.dkr.ecr.eu-west-1.amazonaws.com/profile-service-saved-search-sender:{{BUILD_NUMBER}}
              resources:
                limits:
                  memory: 800Mi
                requests:
                  cpu: 20m
                  memory: 500Mi
              envFrom:
                - configMapRef:
                    name: profile-service-config
              env:
              - name: FREQUENCY
                value: "weekly"
              - name: "JAVA_OPTS"
                value: "-XX:MaxRAMPercentage=70.0 -XX:InitialRAMPercentage=50.0 -XX:+UseG1GC -XX:+UseStringDeduplication"
              - name: DATABASE_USER
                valueFrom:
                  secretKeyRef:
                    name: profile-service
                    key: mysql.user
              - name: DATABASE_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: profile-service
                    key: mysql.password
