apiVersion: apps/v1
kind: Deployment
metadata:
  name: profile-service
  annotations:
    jenkins/build-number: "{{BUILD_NUMBER}}"
spec:
  selector:
      matchLabels:
        app: profile-service
  replicas: 2
  revisionHistoryLimit: 5
  progressDeadlineSeconds: 320
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: profile-service
    spec:
      containers:
      - name: profile-service
        image: 674201978047.dkr.ecr.eu-west-1.amazonaws.com/profile-service:{{BUILD_NUMBER}}
        resources:
          limits:
            memory: 800Mi
          requests:
            cpu: 20m
            memory: 500Mi
        ports:
          - name: app-port
            containerPort: 14200
        readinessProbe:
          timeoutSeconds: 10
          initialDelaySeconds: 30
          periodSeconds: 15
          failureThreshold: 4
          httpGet:
            path: "/status"
            port: app-port
        livenessProbe:
          timeoutSeconds: 5
          initialDelaySeconds: 60
          periodSeconds: 15
          failureThreshold: 3
          httpGet:
            path: "/status"
            port: app-port
        envFrom:
        - configMapRef:
            name: profile-service-config
        env:
          - name: "JAVA_OPTS"
            value: "-XX:MaxRAMPercentage=70.0 -XX:InitialRAMPercentage=50.0 -XX:+UseG1GC -XX:+UseStringDeduplication"
          - name: DATABASE_USER
            valueFrom:
              secretKeyRef:
                name: profile-service
                key: mysql.user
          - name: DATABASE_PASSWORD
            valueFrom:
              secretKeyRef:
                name: profile-service
                key: mysql.password
      restartPolicy: Always
      dnsPolicy: ClusterFirst