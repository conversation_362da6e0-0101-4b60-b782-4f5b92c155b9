import sbt.*

object SavedSearchDependencies {

  object TestContainers {
    val TestContainerVersion = "1.21.3"

    val `testcontainers` = "org.testcontainers" % "testcontainers" % TestContainerVersion % Test
    val `mysql` = "org.testcontainers"          % "mysql"          % TestContainerVersion % Test
  }

  object Weaver {
    val WeaverVersion = "0.9.3"

    val `weaver-cats` = "org.typelevel"       %% "weaver-cats"       % WeaverVersion % Test
    val `weaver-scalacheck` = "org.typelevel" %% "weaver-scalacheck" % WeaverVersion % Test
  }

  object Doobie {
    val DoobieVersion = "1.0.0-RC10"

    val `doobie-core` = "org.tpolecat"           %% "doobie-core"           % DoobieVersion
    val `doobie-hikari` = "org.tpolecat"         %% "doobie-hikari"         % DoobieVersion
    val `doobie-postgres` = "org.tpolecat"       %% "doobie-postgres"       % DoobieVersion
    val `doobie-postgres-circe` = "org.tpolecat" %% "doobie-postgres-circe" % DoobieVersion
    val `doobie-scalatest` = "org.tpolecat"      %% "doobie-scalatest"      % DoobieVersion % Test
  }

  object MySql {
    val MySqlVersion = "9.4.0"

    val `mysql-connector-j` = "com.mysql" % "mysql-connector-j" % MySqlVersion
  }

  object Log4Cats {
    val Log4CatsVersion = "2.7.1"

    val `log4cats-slf4j` = "org.typelevel"   %% "log4cats-slf4j"   % Log4CatsVersion
    val `log4cats-testing` = "org.typelevel" %% "log4cats-testing" % Log4CatsVersion % Test
  }

  object PureConfig {
    val PureConfigVersion = "0.17.9"

    val `pureconfig-core` = "com.github.pureconfig" %% "pureconfig-core" % PureConfigVersion
  }

  object Circe {
    val CirceVersion = "0.14.14"

    val `circe-literal` = "io.circe" %% "circe-literal" % CirceVersion
  }
}
