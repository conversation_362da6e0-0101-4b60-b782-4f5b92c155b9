import sbt.*

object SavedSearchSenderDependencies {
  object B2B {
    val serviceDefinitionsVersion = "10437"

    val `service-definitions` = ("nl.dpes.b2b" % "service-definitions_2.13" % serviceDefinitionsVersion).intransitive()
  }

  object Logback {
    val LogbackVersion = "1.5.18"
    val LogbackContribVersion = "0.1.5"

    val `logback-classic` = "ch.qos.logback" % "logback-classic" % LogbackVersion
    val `logback-json-classic` =
      "ch.qos.logback.contrib" % "logback-json-classic" % LogbackContribVersion excludeAll ExclusionRule("ch.qos.logback")
    val `logback-jackson` = "ch.qos.logback.contrib" % "logback-jackson" % LogbackContribVersion exclude ("ch.qos.logback", "logback-core")
  }

  object Log4Cats {
    val Log4CatsVersion = "2.7.1"

    val `log4cats-core` = "org.typelevel"    %% "log4cats-core"    % Log4CatsVersion
    val `log4cats-slf4j` = "org.typelevel"   %% "log4cats-slf4j"   % Log4CatsVersion
    val `log4cats-testing` = "org.typelevel" %% "log4cats-testing" % Log4CatsVersion % Test
  }

  object Protobuf {
    val `grpc-netty-shaded` = "io.grpc"                % "grpc-netty-shaded"    % "1.75.0"
    val `scalapb-runtime` = "com.thesamet.scalapb"    %% "scalapb-runtime"      % scalapb.compiler.Version.scalapbVersion % "protobuf"
    val `scala-runtime-grpc` = "com.thesamet.scalapb" %% "scalapb-runtime-grpc" % scalapb.compiler.Version.scalapbVersion
  }

  object Weaver {
    val WeaverVersion = "0.10.1"

    val `weaver-cats` = "org.typelevel"       %% "weaver-cats"       % WeaverVersion % Test
    val `weaver-scalacheck` = "org.typelevel" %% "weaver-scalacheck" % WeaverVersion % Test
  }
}
