networks:
  default:
    name: b2b_default
    external: true

services:
  profile-service:
    image: "674201978047.dkr.ecr.eu-west-1.amazonaws.com/profile-service"
    container_name: "profile-service"
    depends_on:
      elasticsearch:
        condition: service_started
      profile-service-database:
        condition: service_healthy
    environment:
      - JAVA_OPTS=-XX:MaxRAMPercentage=70.0 -XX:InitialRAMPercentage=50.0 -XX:+UseG1GC -XX:+UseStringDeduplication -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
    env_file:
      - .env
    ports:
      - "14200:14200"
      - "5005:5005"

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.2
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
    ports:
      - "9200:9200"
      - "9300:9300"
    env_file:
      - .env

  index-creation:
    image: curlimages/curl:7.78.0
    container_name: index-creation
    user: root
    depends_on:
      - elasticsearch
    entrypoint: >
      sh -c "
      mkdir -p /es_config;
      chmod -R 755 /es_config;
      until curl -s http://elasticsearch:9200; do
        echo 'Waiting for Elasticsearch...';
        sleep 5;
      done;
      curl -X PUT 'http://elasticsearch:9200/cv_database' -H 'Content-Type: application/json' -d @/es_config/cv_database_mapping.json;
      curl -X POST 'http://elasticsearch:9200/cv_database/_bulk?pretty' -H 'Content-Type: application/json' --data-binary @/es_config/cv_database_data.json;
      "
    volumes:
      - ./docker/es_config:/es_config

  profile-service-database:
    image: mysql:8.4
    container_name: profile-service-database
    environment:
      - MYSQL_DATABASE=profile-service
      - MYSQL_USER=profile-service
      - MYSQL_PASSWORD=password
      - MYSQL_RANDOM_ROOT_PASSWORD=yes
    healthcheck:
      test: [ "CMD", "mysqladmin" ,"ping", "-h", "localhost" ]
      interval: 5s
      retries: 120
    ports:
      - "14206:3306"
    volumes:
      - ./docker/mysql:/docker-entrypoint-initdb.d
      - ./docker/mysql/data:/var/lib/mysql
    ulimits:
      nproc: 65535
      nofile:
        soft: 20000
        hard: 40000
