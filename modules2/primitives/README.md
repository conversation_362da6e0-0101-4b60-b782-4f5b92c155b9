# The primitives module
## What is a Primitive?
A domain primitive is a small, strongly typed value object that wraps a basic type (like String, Int, or UUID) to give it semantic meaning and domain-level validation.

Instead of using raw types like this:

```scala 3
case class Article(id: String, author: String)
```

We use:
```scala 3
case class ArticleId(value: UUID)
case class AuthorName private (value: String)

case class Article(id: ArticleId, author: AuthorName)
```

With a smart constructor that enforces invariants (e.g., a valid email, non-empty string for `AuthorName`, etc.).

## Why Use Primitives?
Using primitives provides several benefits:
- **Type Safety**: Prevents mixing up values like `UserId` and `ArticleId` (even if both are `UUID`)
- **Centralized Validation**: Validation rules live in one place, not scattered throughout the app
- **Domain Clarity**: Code is easier to read and reason about (e.g., `EmailAddress`, not just `String`)
- **Consistency Across Modules**: The same primitive can be used across different modules, ensuring consistent validation and representation of values.

## How to Use Primitives
Define each primitive in the primitives module
Use smart constructors to create values safely (e.g., EmailAddress.from("<EMAIL>"))
Reuse them across:
- Subdomain logic
- Inbound adapters (HTTP, CLI, etc.)
- Outbound adapters (DB, Kafka, etc.)

Define serializers (e.g., Circe, Tapir) in separate adapter modules which can also be shared for the adapters. 
The (sub)domain logic should not need to depend on any serialization library.

## Example
```scala 3
final case class EmailAddress private (value: String) extends AnyVal

object EmailAddress {
  def apply(input: String): Either[String, EmailAddress] =
    if (input.matches("^[^@]+@[^@]+$")) Right(EmailAddress(input))
    else Left("Invalid email address")
}
```

## TL;DR
Use domain primitives to make your types meaningful, your validations centralized, and your codebase safer and more expressive.